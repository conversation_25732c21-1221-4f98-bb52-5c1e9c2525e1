#!/usr/bin/env python3
"""
测试性能监控功能
"""

import asyncio
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, '/Users/<USER>/由己ai项目/需求采集项目')

from backend.utils.performance_monitor import performance_monitor
from backend.agents.llm_service import AutoGenLLMServiceAgent
from backend.data.db.database_manager import DatabaseManager

async def test_llm_performance():
    """测试LLM性能监控"""
    print("=== 测试LLM性能监控 ===")
    
    try:
        # 初始化LLM服务
        llm_service = AutoGenLLMServiceAgent()
        
        # 模拟LLM调用
        with performance_monitor.track_llm_call("deepseek", "deepseek-chat", "test_call"):
            # 模拟一些处理时间
            await asyncio.sleep(0.1)
            print("模拟LLM调用完成")
        
        # 获取LLM指标
        llm_metrics = performance_monitor.get_llm_metrics()
        print(f"LLM指标: {llm_metrics}")
        
    except Exception as e:
        print(f"LLM测试失败: {e}")

async def test_db_performance():
    """测试数据库性能监控"""
    print("\n=== 测试数据库性能监控 ===")
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager("/tmp/test.db")
        
        # 执行一个简单的查询
        await db_manager.execute_query("SELECT 1 as test")
        print("数据库查询完成")
        
        # 获取数据库指标
        db_metrics = performance_monitor.get_db_metrics()
        print(f"数据库指标: {db_metrics}")
        
    except Exception as e:
        print(f"数据库测试失败: {e}")

async def main():
    """主测试函数"""
    print("开始性能监控测试...")

    # 性能监控器已经在导入时初始化了，这里直接使用
    print(f"性能监控器状态: 启用={performance_monitor.enabled}")
    
    # 测试LLM性能监控
    await test_llm_performance()
    
    # 测试数据库性能监控
    await test_db_performance()
    
    # 获取所有指标
    print("\n=== 所有性能指标 ===")
    all_metrics = performance_monitor.get_all_metrics()
    print(f"API指标: {all_metrics['api']}")
    print(f"LLM指标: {all_metrics['llm']}")
    print(f"数据库指标: {all_metrics['db']}")
    
    # 保存指标
    file_path = performance_monitor.save_metrics("test_performance.json")
    print(f"\n性能指标已保存到: {file_path}")

if __name__ == "__main__":
    asyncio.run(main())
