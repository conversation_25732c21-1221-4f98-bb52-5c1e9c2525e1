aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aioredis==2.0.1
aiosignal==1.3.2
aiosqlite==0.21.0
annotated-types==0.7.0
anyio==4.9.0
async-timeout==5.0.1
asyncer==0.0.8
attrs==25.3.0
# Editable install with no version control (autogen_backend==0.1.0)
-e /Users/<USER>/由己ai项目/需求采集项目/autogen_backend
autopep8==2.3.2
black==25.1.0
cachetools==5.5.2
certifi==2025.4.26
charset-normalizer==3.4.2
circuitbreaker==2.1.3
click==8.1.8
coverage==7.8.0
diskcache==5.6.3
distro==1.9.0
docker==7.1.0
fastapi==0.115.12
fastapi-limiter==0.1.6
frozenlist==1.6.0
google-ai-generativelanguage==0.6.15
google-api-core==2.25.0rc1
google-api-python-client==2.169.0
google-auth==2.40.1
google-auth-httplib2==0.2.0
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
grpcio==1.71.0
grpcio-status==1.71.0
h11==0.16.0
httpcore==1.0.9
httplib2==0.22.0
httpx==0.28.1
idna==3.10
iniconfig==2.1.0
Jinja2==3.1.6
jiter==0.9.0
MarkupSafe==3.0.2
multidict==6.4.3
mypy==1.15.0
mypy_extensions==1.1.0
openai==1.78.0
packaging==25.0
pathspec==0.12.1
platformdirs==4.3.8
pluggy==1.5.0
propcache==0.3.1
proto-plus==1.26.1
protobuf==5.29.4
psutil==7.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pyautogen==0.9.0
pycodestyle==2.13.0
pydantic==2.11.4
pydantic_core==2.33.2
pyparsing==3.2.3
pytest==8.3.5
pytest-asyncio==0.26.0
pytest-cov==6.1.1
python-dotenv==1.1.0
python-json-logger==2.0.7
python-multipart==0.0.20
PyYAML==6.0.2
redis==6.2.0
regex==2024.11.6
requests==2.32.3
rsa==4.9.1
ruff==0.11.9
sniffio==1.3.1
SQLAlchemy==2.0.40
starlette==0.46.2
termcolor==3.1.0
tiktoken==0.9.0
tqdm==4.67.1
typing-inspection==0.4.0
typing_extensions==4.13.2
uritemplate==4.1.1
urllib3==2.4.0
uvicorn==0.34.2
wsproto==1.2.0
yarl==1.20.0
