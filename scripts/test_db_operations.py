import sys
import os
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.data.db.database_manager import DatabaseManager

def test_document_insertion():
    """测试文档插入操作"""
    db_manager = DatabaseManager(db_path="backend/data/aidatabase.db")
    
    # 测试数据
    conversation_id = "5a6a94fd-b58e-49fe-b601-71b1c7bade6c"  # 使用实际存在的会话ID
    doc_id = f"doc_{uuid.uuid4().hex}"
    content = "# 测试文档\n\n这是一个测试文档内容。"
    version = 1
    
    try:
        # 插入文档
        insert_query = """
            INSERT INTO documents 
            (document_id, conversation_id, content, status, version, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        """
        
        db_manager.execute_query(
            insert_query,
            (
                doc_id,
                conversation_id,
                content,
                "draft",
                version
            )
        )
        
        print(f"文档插入成功! 文档ID: {doc_id}")
        
        # 验证插入是否成功
        verify_query = """
            SELECT * FROM documents WHERE document_id = ?
        """
        result = db_manager.execute_query(verify_query, (doc_id,))
        
        if result:
            print("验证成功! 查询结果:")
            for key, value in result[0].items():
                print(f"  {key}: {value}")
        else:
            print("验证失败! 未找到插入的文档")
            
    except Exception as e:
        print(f"测试失败: {str(e)}")

def test_conversation_exists():
    """测试会话是否存在"""
    db_manager = DatabaseManager(db_path="backend/data/aidatabase.db")
    
    try:
        # 查询所有会话
        query = "SELECT * FROM conversations"
        result = db_manager.execute_query(query)
        
        if result:
            print(f"找到 {len(result)} 个会话:")
            for i, conv in enumerate(result[:5]):  # 只显示前5个
                print(f"  会话 {i+1}: ID={conv.get('conversation_id')}, 状态={conv.get('status')}")
            if len(result) > 5:
                print(f"  ... 还有 {len(result)-5} 个会话")
        else:
            print("数据库中没有会话记录!")
            
    except Exception as e:
        print(f"查询失败: {str(e)}")

if __name__ == "__main__":
    print("===== 测试会话是否存在 =====")
    test_conversation_exists()
    print("\n===== 测试文档插入 =====")
    test_document_insertion()
