#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
冗余日志记录分析脚本
检查项目中是否存在重复或冗余的日志记录
"""

import os
import re
import sys
from collections import defaultdict, Counter
from pathlib import Path
from typing import Dict, List, Set, Tuple

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class LoggingAnalyzer:
    """日志记录分析器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.log_statements = []
        self.file_logs = defaultdict(list)
        self.duplicate_patterns = defaultdict(list)
        
    def analyze_project(self):
        """分析整个项目的日志记录"""
        print("🔍 开始分析项目中的日志记录...")
        
        # 扫描Python文件
        python_files = list(self.project_root.rglob("*.py"))
        print(f"📁 找到 {len(python_files)} 个Python文件")
        
        for file_path in python_files:
            # 跳过虚拟环境和缓存目录
            if any(skip in str(file_path) for skip in ['.venv', '__pycache__', '.git', 'node_modules']):
                continue
                
            self._analyze_file(file_path)
        
        print(f"📊 总共找到 {len(self.log_statements)} 条日志记录")
        
    def _analyze_file(self, file_path: Path):
        """分析单个文件的日志记录"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                
            # 查找日志记录语句
            log_pattern = re.compile(r'(self\.)?logger\.(debug|info|warning|error|critical)\s*\(\s*["\']([^"\']+)["\']')
            
            for line_num, line in enumerate(lines, 1):
                matches = log_pattern.findall(line.strip())
                for match in matches:
                    log_level = match[1]
                    log_message = match[2]
                    
                    log_info = {
                        'file': str(file_path.relative_to(self.project_root)),
                        'line': line_num,
                        'level': log_level,
                        'message': log_message,
                        'full_line': line.strip()
                    }
                    
                    self.log_statements.append(log_info)
                    self.file_logs[str(file_path.relative_to(self.project_root))].append(log_info)
                    
        except Exception as e:
            print(f"⚠️ 分析文件 {file_path} 时出错: {e}")
    
    def find_duplicate_messages(self) -> Dict[str, List]:
        """查找重复的日志消息"""
        print("\n🔍 查找重复的日志消息...")
        
        message_groups = defaultdict(list)
        
        for log in self.log_statements:
            # 标准化消息（移除变量部分）
            normalized_msg = self._normalize_message(log['message'])
            message_groups[normalized_msg].append(log)
        
        # 找出重复的消息
        duplicates = {msg: logs for msg, logs in message_groups.items() if len(logs) > 1}
        
        if duplicates:
            print(f"❌ 发现 {len(duplicates)} 组重复的日志消息:")
            for i, (msg, logs) in enumerate(duplicates.items(), 1):
                print(f"\n  {i}. 重复消息: '{msg}' (出现 {len(logs)} 次)")
                for log in logs[:3]:  # 只显示前3个
                    print(f"     - {log['file']}:{log['line']} ({log['level']})")
                if len(logs) > 3:
                    print(f"     - ... 还有 {len(logs) - 3} 个位置")
        else:
            print("✅ 未发现重复的日志消息")
            
        return duplicates
    
    def find_similar_operations(self) -> List[Tuple[str, List]]:
        """查找记录相似操作的日志"""
        print("\n🔍 查找记录相似操作的日志...")
        
        operation_patterns = {
            '创建会话': [r'创建.*会话', r'新.*会话'],
            '状态更新': [r'更新.*状态', r'状态.*更新', r'设置.*状态'],
            '数据库操作': [r'执行.*SQL', r'数据库.*操作', r'查询.*数据库'],
            '关注点处理': [r'关注点.*状态', r'处理.*关注点', r'初始化.*关注点'],
            '错误处理': [r'失败', r'错误', r'异常'],
            '同步操作': [r'同步.*', r'加载.*状态', r'恢复.*状态']
        }
        
        similar_groups = []
        
        for operation, patterns in operation_patterns.items():
            matching_logs = []
            for log in self.log_statements:
                for pattern in patterns:
                    if re.search(pattern, log['message'], re.IGNORECASE):
                        matching_logs.append(log)
                        break
            
            if len(matching_logs) > 5:  # 如果某类操作的日志过多
                similar_groups.append((operation, matching_logs))
        
        if similar_groups:
            print(f"⚠️ 发现 {len(similar_groups)} 类可能冗余的操作日志:")
            for operation, logs in similar_groups:
                print(f"\n  📋 {operation}: {len(logs)} 条日志")
                # 按文件分组显示
                file_counts = Counter(log['file'] for log in logs)
                for file_path, count in file_counts.most_common(3):
                    print(f"     - {file_path}: {count} 条")
        else:
            print("✅ 未发现明显冗余的操作日志")
            
        return similar_groups
    
    def analyze_debug_logging(self):
        """分析DEBUG级别日志的使用情况"""
        print("\n🔍 分析DEBUG级别日志的使用情况...")
        
        debug_logs = [log for log in self.log_statements if log['level'] == 'debug']
        
        if not debug_logs:
            print("✅ 未发现DEBUG级别日志")
            return
            
        print(f"📊 发现 {len(debug_logs)} 条DEBUG级别日志")
        
        # 按文件统计
        file_counts = Counter(log['file'] for log in debug_logs)
        print(f"\n📁 DEBUG日志分布 (前5个文件):")
        for file_path, count in file_counts.most_common(5):
            print(f"  - {file_path}: {count} 条")
        
        # 检查是否有过多的DEBUG日志
        excessive_files = [(f, c) for f, c in file_counts.items() if c > 10]
        if excessive_files:
            print(f"\n⚠️ 以下文件的DEBUG日志可能过多:")
            for file_path, count in excessive_files:
                print(f"  - {file_path}: {count} 条 (建议检查是否必要)")
    
    def check_logging_consistency(self):
        """检查日志记录的一致性"""
        print("\n🔍 检查日志记录的一致性...")
        
        issues = []
        
        # 检查相同操作是否使用了不同的日志级别
        operation_levels = defaultdict(set)
        for log in self.log_statements:
            normalized_msg = self._normalize_message(log['message'])
            operation_levels[normalized_msg].add(log['level'])
        
        inconsistent_levels = {msg: levels for msg, levels in operation_levels.items() 
                             if len(levels) > 1}
        
        if inconsistent_levels:
            print(f"⚠️ 发现 {len(inconsistent_levels)} 个操作使用了不同的日志级别:")
            for msg, levels in list(inconsistent_levels.items())[:5]:
                print(f"  - '{msg}': {', '.join(levels)}")
            issues.append("日志级别不一致")
        
        # 检查是否有过长的日志消息
        long_messages = [log for log in self.log_statements if len(log['message']) > 100]
        if long_messages:
            print(f"⚠️ 发现 {len(long_messages)} 条过长的日志消息 (>100字符)")
            issues.append("日志消息过长")
        
        if not issues:
            print("✅ 日志记录一致性良好")
        
        return issues
    
    def _normalize_message(self, message: str) -> str:
        """标准化日志消息，移除变量部分"""
        # 移除常见的变量模式
        patterns = [
            r'\{[^}]+\}',  # {variable}
            r'%[sd]',      # %s, %d
            r':\s*[^,\s]+',  # : value
            r'=\s*[^,\s]+',  # = value
            r'\d+',        # 数字
            r'[a-f0-9]{8,}',  # 长的十六进制字符串（如ID）
        ]
        
        normalized = message
        for pattern in patterns:
            normalized = re.sub(pattern, '<VAR>', normalized)
        
        return normalized.strip()
    
    def generate_report(self):
        """生成分析报告"""
        print("\n" + "="*60)
        print("📋 冗余日志记录分析报告")
        print("="*60)
        
        # 基本统计
        print(f"\n📊 基本统计:")
        print(f"  总日志条数: {len(self.log_statements)}")
        print(f"  涉及文件数: {len(self.file_logs)}")
        
        level_counts = Counter(log['level'] for log in self.log_statements)
        print(f"  日志级别分布:")
        for level, count in level_counts.most_common():
            print(f"    - {level.upper()}: {count} 条")
        
        # 执行各项分析
        duplicates = self.find_duplicate_messages()
        similar_ops = self.find_similar_operations()
        self.analyze_debug_logging()
        consistency_issues = self.check_logging_consistency()
        
        # 生成建议
        print(f"\n💡 优化建议:")
        suggestions = []
        
        if duplicates:
            suggestions.append(f"合并 {len(duplicates)} 组重复的日志消息")
        
        if similar_ops:
            suggestions.append("考虑统一相似操作的日志记录方式")
        
        debug_count = len([log for log in self.log_statements if log['level'] == 'debug'])
        if debug_count > 100:
            suggestions.append(f"检查 {debug_count} 条DEBUG日志是否都必要")
        
        if consistency_issues:
            suggestions.append("统一日志记录的格式和级别")
        
        if suggestions:
            for i, suggestion in enumerate(suggestions, 1):
                print(f"  {i}. {suggestion}")
        else:
            print("  ✅ 日志记录质量良好，无需特别优化")

def main():
    """主函数"""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    analyzer = LoggingAnalyzer(project_root)
    analyzer.analyze_project()
    analyzer.generate_report()

if __name__ == "__main__":
    main()
