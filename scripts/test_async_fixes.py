#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库异步调用修复的脚本
验证修复后的代码是否能正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.data.db.database_manager import DatabaseManager
from backend.data.db.focus_point_manager import FocusPointManager
from backend.agents.document_generator import DocumentGenerator

async def test_focus_point_manager():
    """测试FocusPointManager的异步修复"""
    print("🔍 测试 FocusPointManager 异步修复...")
    
    try:
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        focus_manager = FocusPointManager(db_manager)
        
        # 测试会话创建
        test_conversation_id = "test_async_fix_001"
        result = await focus_manager.ensure_conversation_exists(test_conversation_id)
        print(f"✅ 会话创建测试: {'成功' if result else '失败'}")
        
        # 测试关注点状态初始化
        test_focus_points = [
            {"id": "test_point_1", "name": "测试点1", "priority": "P0"},
            {"id": "test_point_2", "name": "测试点2", "priority": "P1"}
        ]
        result = await focus_manager.initialize_focus_points(test_conversation_id, test_focus_points)
        print(f"✅ 关注点初始化测试: {'成功' if result else '失败'}")
        
        # 测试状态加载
        status_dict = await focus_manager.load_focus_points_status(test_conversation_id)
        print(f"✅ 状态加载测试: {'成功' if isinstance(status_dict, dict) else '失败'}")
        
        # 测试状态更新
        result = await focus_manager.update_focus_point_status(
            test_conversation_id, "test_point_1", "completed", "测试值"
        )
        print(f"✅ 状态更新测试: {'成功' if result else '失败'}")
        
        # 测试状态获取
        status = await focus_manager.get_focus_point_status(test_conversation_id, "test_point_1")
        print(f"✅ 状态获取测试: {'成功' if status is not None else '失败'}")
        
        print("🎉 FocusPointManager 所有测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ FocusPointManager 测试失败: {str(e)}")
        return False

async def test_document_generator():
    """测试DocumentGenerator的异步修复"""
    print("\n🔍 测试 DocumentGenerator 异步修复...")
    
    try:
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        
        # 创建一个简单的LLM客户端模拟
        class MockLLMClient:
            async def call_llm(self, messages, session_id=None, agent_name=None):
                return {"content": "测试文档内容"}
        
        doc_generator = DocumentGenerator(
            llm_client=MockLLMClient(),
            db_manager=db_manager
        )
        
        # 测试获取关注点
        test_conversation_id = "test_async_fix_001"
        concerns = await doc_generator.get_concern_points(test_conversation_id)
        print(f"✅ 获取关注点测试: {'成功' if isinstance(concerns, list) else '失败'}")
        
        print("🎉 DocumentGenerator 测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ DocumentGenerator 测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试数据库异步调用修复...")
    print("=" * 50)
    
    # 测试FocusPointManager
    focus_test_result = await test_focus_point_manager()
    
    # 测试DocumentGenerator
    doc_test_result = await test_document_generator()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"FocusPointManager: {'✅ 通过' if focus_test_result else '❌ 失败'}")
    print(f"DocumentGenerator: {'✅ 通过' if doc_test_result else '❌ 失败'}")
    
    if focus_test_result and doc_test_result:
        print("\n🎉 所有异步修复测试通过! 数据库操作现在都正确使用了await关键字。")
        return True
    else:
        print("\n❌ 部分测试失败，请检查修复是否完整。")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生意外错误: {str(e)}")
        sys.exit(1)
