#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试文档审阅阶段用户修改内容识别修复

验证修复后的文档修改功能是否能正确识别和处理用户的修改要求
"""

import os
import sys
import asyncio
import logging
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.utils.logging_config import configure_logging
from backend.agents.review_and_refine import AutoGenReviewAndRefineAgent
from backend.agents.llm_service import AutoGenLLMServiceAgent

# 配置日志
configure_logging()
logger = logging.getLogger(__name__)

# 测试文档
TEST_DOCUMENT = """# 电商平台开发项目 需求确认文档

**文档日期**: 2023-12-20

## 1. 项目概述

开发一个现代化的电商平台，支持商品展示、在线购买和用户管理功能。

## 2. 需求描述

- **商品管理**: 商品展示、分类浏览、搜索功能
- **用户系统**: 用户注册、登录、个人中心
- **购物功能**: 购物车、下单、支付集成
- **订单管理**: 订单跟踪、状态更新、退换货

## 3. 预估的交付时间和金额

- **预估交付时间:** 4个月
- **预估交付金额:** 150000 元

## 4. 项目建议

- **技术选择**: 建议使用React前端和Node.js后端
- **支付集成**: 建议集成支付宝和微信支付
- **安全考虑**: 注重用户数据安全和支付安全
"""

# 测试用例
TEST_CASES = [
    {
        "name": "明确的修改请求",
        "user_feedback": "将交付时间改为3个月",
        "expected_type": "modification",
        "description": "用户明确指出要修改的内容和目标"
    },
    {
        "name": "删除功能请求", 
        "user_feedback": "删除退换货功能，我们暂时不需要",
        "expected_type": "modification",
        "description": "用户明确要求删除某个功能"
    },
    {
        "name": "模糊的修改请求",
        "user_feedback": "这个不太合适",
        "expected_type": "clarification",
        "description": "用户反馈模糊，需要澄清具体要修改什么"
    },
    {
        "name": "添加功能请求",
        "user_feedback": "在需求描述中添加客服系统功能",
        "expected_type": "modification",
        "description": "用户明确要求添加新功能"
    },
    {
        "name": "非常模糊的反馈",
        "user_feedback": "不行",
        "expected_type": "clarification",
        "description": "用户反馈过于简单，需要澄清"
    },
    {
        "name": "情感化反馈",
        "user_feedback": "我不喜欢这样",
        "expected_type": "clarification",
        "description": "用户表达不满但没有具体指出问题"
    }
]

async def test_document_review_fix():
    """测试文档审阅修复效果"""
    try:
        print("🔧 开始测试文档审阅阶段用户修改内容识别修复")
        print("=" * 70)
        
        # 初始化LLM客户端和review_and_refine代理
        llm_client = AutoGenLLMServiceAgent()
        review_agent = AutoGenReviewAndRefineAgent(
            llm_client=llm_client,
            agent_name="review_and_refine"
        )
        
        # 模拟会话ID
        test_session_id = "test_session_document_review_fix"
        
        # 首先保存测试文档到数据库
        print("📄 准备测试文档...")
        from datetime import datetime
        await review_agent.document_repository.save_document(
            document_id="test_doc_review_001",
            conversation_id=test_session_id,
            content=TEST_DOCUMENT,
            version=1,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            status='draft'
        )
        print("✅ 测试文档已保存")
        
        # 测试每个用例
        for i, test_case in enumerate(TEST_CASES):
            print(f"\n{'='*70}")
            print(f"🧪 测试用例 {i+1}: {test_case['name']}")
            print(f"{'='*70}")
            print(f"用户反馈: {test_case['user_feedback']}")
            print(f"预期类型: {test_case['expected_type']}")
            print(f"描述: {test_case['description']}")
            
            # 调用文档修改处理
            result = await review_agent.process_message({
                "text": test_case['user_feedback'],
                "session_id": test_session_id
            })
            
            print(f"\n📊 处理结果:")
            print(f"成功: {result.get('success', False)}")
            print(f"下一步动作: {result.get('next_action', 'unknown')}")
            
            # 显示响应内容（截取前200字符）
            response_text = result.get('text_response', '')
            if len(response_text) > 200:
                print(f"响应内容: {response_text[:200]}...")
            else:
                print(f"响应内容: {response_text}")
            
            # 验证结果类型
            next_action = result.get('next_action')
            if test_case['expected_type'] == 'clarification':
                if next_action == 'clarify':
                    print("✅ 正确识别为澄清请求")
                else:
                    print("❌ 未能正确识别为澄清请求")
                    print(f"   期望: clarify, 实际: {next_action}")
            elif test_case['expected_type'] == 'modification':
                if next_action == 'review_again':
                    print("✅ 正确执行文档修改")
                else:
                    print("❌ 未能正确执行文档修改")
                    print(f"   期望: review_again, 实际: {next_action}")
        
        print(f"\n{'='*70}")
        print("🎉 测试完成！")
        print("📈 修复效果总结:")
        print("- ✅ 优化了review_and_refine.md提示词模板")
        print("- ✅ 添加了澄清判断逻辑(_is_clarification_request)")
        print("- ✅ 支持智能区分修改和澄清请求")
        print("- ✅ 改善了用户体验，减少不必要的强制修改")
        print("- ✅ 保持了原有的文档修改功能")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}", exc_info=True)
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_document_review_fix())
