import asyncio
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from backend.agents.document_generator import DocumentGenerator

async def test_document_generation():
    """测试文档生成功能"""
    from backend.agents.document_generator import DocumentGenerator
    from backend.agents.llm_service import AutoGenLLMServiceAgent
    from backend.data.db.database_manager import DatabaseManager
    from backend.config.settings import LLM_CONFIGS, SCENARIO_LLM_MAPPING
    import logging
    
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 检查配置
    logger.info("检查LLM配置...")
    document_model = SCENARIO_LLM_MAPPING.get("document_generator", "")
    if document_model:
        logger.info(f"文档生成器使用模型: {document_model}")
        if document_model in LLM_CONFIGS:
            model_config = LLM_CONFIGS[document_model]
            logger.info(f"模型配置: {model_config}")
            logger.info(f"模型名称: {model_config.get('model_name')}")
            logger.info(f"提供商: {model_config.get('provider')}")
            logger.info(f"温度: {model_config.get('temperature')}")
            logger.info(f"最大tokens: {model_config.get('max_tokens')}")
        else:
            logger.warning(f"找不到模型配置: {document_model}")
    else:
        logger.warning("文档生成器没有指定模型")
    
    # 初始化服务
    db_manager = DatabaseManager(db_path="backend/data/aidatabase.db")
    llm_service = AutoGenLLMServiceAgent()
    
    # 创建文档生成器
    generator = DocumentGenerator(
        llm_client=llm_service,
        agent_name="document_generator",  # 确保agent_name与SCENARIO_LLM_MAPPING中的键匹配
        db_manager=db_manager
    )
    
    # 测试生成文档
    conversation_id = "5a6a94fd-b58e-49fe-b601-71b1c7bade6c"  # 替换为实际的会话ID
    project_name = "测试项目"
    
    logger.info(f"开始生成文档，会话ID: {conversation_id}, 项目名称: {project_name}")
    doc_id = await generator.generate_document(conversation_id, project_name)
    
    if doc_id is not None:
        logger.info(f"文档生成成功! 文档ID: {doc_id}")
        
        # 获取并显示生成的文档内容
        try:
            document = await db_manager.get_document(doc_id)
            if document:
                logger.info(f"文档内容预览 (前500字符):\n{document.get('content', '')[:500]}...")
            else:
                logger.warning(f"无法获取文档内容，文档ID: {doc_id}")
        except Exception as e:
            logger.error(f"获取文档内容失败: {str(e)}")
    else:
        logger.error("文档生成失败")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_document_generation())
