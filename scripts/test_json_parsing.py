import json
import re
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_json_parsing():
    """测试JSON解析"""
    # 测试数据
    test_json = """{
  "overview": "这是一个电商平台升级项目，旨在提升用户体验和转化率。",
  "requirements": "1. 实现个性化推荐功能\\n2. 优化移动端支付流程\\n3. 增加社交分享功能",
  "Delivery_time": "2023年12月31日前",
  "Delivery_Amount": "120000",
  "recommendations": "1. 建议分阶段实施，先上线核心功能\\n2. 注意数据安全合规\\n3. 预留接口扩展性"
}"""

    # 尝试直接解析
    print("\n===== 尝试直接解析 =====\n")
    try:
        result = json.loads(test_json)
        print("成功解析JSON:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {str(e)}")
        
        # 尝试修复多行字符串问题
        print("\n===== 尝试修复多行字符串 =====\n")
        try:
            # 替换换行符
            fixed_json = test_json.replace('\n', '\\n')
            # 但保留大括号后的换行
            fixed_json = fixed_json.replace('{\\n', '{\n')
            fixed_json = fixed_json.replace('\\n}', '\n}')
            # 修复引号内的换行
            fixed_json = fix_newlines_in_json(fixed_json)
            
            print("修复后的JSON:")
            print(fixed_json)
            
            result = json.loads(fixed_json)
            print("\n成功解析修复后的JSON:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        except json.JSONDecodeError as e:
            print(f"修复后的JSON仍然解析失败: {str(e)}")
            
            # 尝试手动解析
            print("\n===== 尝试手动解析 =====\n")
            result = manual_parse_json(test_json)
            print("手动解析结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))

def manual_parse_json(json_str):
    """手动解析JSON字符串"""
    result = {}
    
    # 提取overview
    overview_match = re.search(r'"overview"\s*:\s*"([^"]*)"', json_str)
    if overview_match:
        result["overview"] = overview_match.group(1)
    
    # 提取requirements (可能包含多行)
    requirements_match = re.search(r'"requirements"\s*:\s*"(.*?)(?="[,}])', json_str, re.DOTALL)
    if requirements_match:
        requirements = requirements_match.group(1)
        result["requirements"] = requirements
    
    # 提取Delivery_time
    delivery_time_match = re.search(r'"Delivery_time"\s*:\s*"([^"]*)"', json_str)
    if delivery_time_match:
        result["Delivery_time"] = delivery_time_match.group(1)
    
    # 提取Delivery_Amount
    delivery_amount_match = re.search(r'"Delivery_Amount"\s*:\s*"?(\d+)"?', json_str)
    if delivery_amount_match:
        result["Delivery_Amount"] = delivery_amount_match.group(1)
    
    # 提取recommendations (可能包含多行)
    recommendations_match = re.search(r'"recommendations"\s*:\s*"(.*?)(?="[,}]|\Z)', json_str, re.DOTALL)
    if recommendations_match:
        recommendations = recommendations_match.group(1)
        result["recommendations"] = recommendations
    
    return result

def fix_newlines_in_json(json_str):
    """修复JSON字符串中引号内的换行符"""
    result = []
    in_quotes = False
    escape = False
    
    for char in json_str:
        if not escape and char == '"':
            in_quotes = not in_quotes
            result.append(char)
        elif in_quotes and char == '\n':
            result.append('\\n')
        elif in_quotes and char == '\\' and not escape:
            escape = True
            result.append(char)
        else:
            if escape:
                escape = False
            result.append(char)
    
    return ''.join(result)

if __name__ == "__main__":
    test_json_parsing()
