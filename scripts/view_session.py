#!/usr/bin/env python
"""
会话日志查看工具

用法:
    python view_session.py <session_id> [--output <output_file>] [--format <format>]

参数:
    session_id: 要查看的会话ID
    --output: 输出文件路径，默认为标准输出
    --format: 输出格式，可选值为text(默认)、json、markdown

示例:
    python view_session.py 123456
    python view_session.py 123456 --output session_123456.md --format markdown
"""

import sys
import re
import json
import argparse
from datetime import datetime
from pathlib import Path
import os

def parse_log_line(line):
    """解析日志行，提取时间戳、会话ID、阶段和消息"""
    # 匹配格式: 2025-05-15 17:35:53,659 - [session_id] - [stage] - module - LEVEL - message
    pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - \[([^\]]+)\] - \[([^\]]+)\] - ([^-]+) - ([^-]+) - (.*)'
    match = re.match(pattern, line)
    
    if match:
        timestamp, session_id, stage, module, level, message = match.groups()
        return {
            "timestamp": timestamp,
            "session_id": session_id.strip(),
            "stage": stage.strip(),
            "module": module.strip(),
            "level": level.strip(),
            "message": message.strip()
        }
    
    # 尝试匹配没有会话ID和阶段的格式
    pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - ([^-]+) - ([^-]+) - (.*)'
    match = re.match(pattern, line)
    
    if match:
        timestamp, module, level, message = match.groups()
        return {
            "timestamp": timestamp,
            "session_id": None,
            "stage": None,
            "module": module.strip(),
            "level": level.strip(),
            "message": message.strip()
        }
    
    return None

def view_session(session_id, log_file="logs/session.log"):
    """查看特定会话的日志"""
    if not os.path.exists(log_file):
        print(f"错误: 日志文件 {log_file} 不存在")
        return []
    
    session_logs = []
    
    with open(log_file, "r", encoding="utf-8") as f:
        for line in f:
            parsed = parse_log_line(line.strip())
            if parsed and parsed["session_id"] == session_id:
                session_logs.append(parsed)
    
    return session_logs

def format_logs_as_text(logs):
    """将日志格式化为文本"""
    if not logs:
        return "未找到该会话的日志记录"
    
    result = []
    for log in logs:
        result.append(f"[{log['timestamp']}] [{log['stage']}] [{log['level']}] {log['message']}")
    
    return "\n".join(result)

def format_logs_as_json(logs):
    """将日志格式化为JSON"""
    return json.dumps(logs, ensure_ascii=False, indent=2)

def format_logs_as_markdown(logs):
    """将日志格式化为Markdown"""
    if not logs:
        return "# 会话日志\n\n未找到该会话的日志记录"
    
    result = [f"# 会话 {logs[0]['session_id']} 日志\n"]
    
    # 按阶段分组
    stages = {}
    for log in logs:
        stage = log['stage']
        if stage not in stages:
            stages[stage] = []
        stages[stage].append(log)
    
    # 生成每个阶段的日志
    for stage, stage_logs in stages.items():
        result.append(f"## 阶段: {stage}\n")
        
        for log in stage_logs:
            level_mark = "📝"
            if log['level'] == "ERROR":
                level_mark = "❌"
            elif log['level'] == "WARNING":
                level_mark = "⚠️"
            elif log['level'] == "INFO":
                level_mark = "ℹ️"
            elif log['level'] == "DEBUG":
                level_mark = "🔍"
            
            result.append(f"### {level_mark} {log['timestamp']}\n")
            result.append(f"{log['message']}\n")
    
    return "\n".join(result)

def main():
    parser = argparse.ArgumentParser(description="查看特定会话的日志")
    parser.add_argument("session_id", help="要查看的会话ID")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--format", choices=["text", "json", "markdown"], default="text", help="输出格式")
    
    args = parser.parse_args()
    
    logs = view_session(args.session_id)
    
    if args.format == "text":
        output = format_logs_as_text(logs)
    elif args.format == "json":
        output = format_logs_as_json(logs)
    elif args.format == "markdown":
        output = format_logs_as_markdown(logs)
    
    if args.output:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(output)
        print(f"日志已保存到 {args.output}")
    else:
        print(output)

if __name__ == "__main__":
    main()
