#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一关注点状态管理的脚本
验证FocusPointStatusManager和FocusPointManager协同工作
"""

import asyncio
import sys
import os
import json

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.data.db.database_manager import DatabaseManager
from backend.data.db.focus_point_manager import FocusPointManager
from backend.agents.conversation_flow import FocusPointStatusManager

async def test_unified_status_management():
    """测试统一状态管理"""
    print("🔍 测试统一关注点状态管理...")
    
    try:
        # 初始化组件
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        focus_point_manager = FocusPointManager(db_manager)
        status_manager = FocusPointStatusManager(db_manager, focus_point_manager)
        
        test_session_id = "test_unified_001"
        
        # 测试数据
        test_focus_points = [
            {"id": "test_point_1", "name": "测试点1", "priority": "P0"},
            {"id": "test_point_2", "name": "测试点2", "priority": "P1"},
            {"id": "test_point_3", "name": "测试点3", "priority": "P2"}
        ]
        
        print("\n📋 第一阶段：测试协同初始化")
        
        # 1. 通过FocusPointManager初始化
        result1 = await focus_point_manager.initialize_focus_points(test_session_id, test_focus_points)
        print(f"✅ FocusPointManager初始化: {'成功' if result1 else '失败'}")
        
        # 2. 通过FocusPointStatusManager同步
        await status_manager.sync_with_focus_point_manager(test_session_id)
        status_dict = status_manager.get_all_focus_points_status()
        print(f"✅ 状态同步: {'成功' if len(status_dict) == 3 else '失败'} (同步了{len(status_dict)}个关注点)")
        
        print("\n📋 第二阶段：测试协同更新")
        
        # 3. 通过FocusPointStatusManager更新状态
        result2 = await status_manager.update_focus_point_status(
            test_session_id, "test_point_1", "processing", "测试值1"
        )
        print(f"✅ StatusManager更新: {'成功' if result2 else '失败'}")
        
        # 4. 验证FocusPointManager能看到更新
        fp_status = await focus_point_manager.get_focus_point_status(test_session_id, "test_point_1")
        print(f"✅ FocusPointManager验证: {'成功' if fp_status and fp_status['status'] == 'processing' else '失败'}")
        
        # 5. 通过FocusPointManager更新状态
        result3 = await focus_point_manager.update_focus_point_status(
            test_session_id, "test_point_2", "completed", "测试值2"
        )
        print(f"✅ FocusPointManager更新: {'成功' if result3 else '失败'}")
        
        # 6. 验证FocusPointStatusManager能看到更新
        await status_manager.sync_with_focus_point_manager(test_session_id)
        sm_status = status_manager.get_focus_point_status("test_point_2")
        print(f"✅ StatusManager验证: {'成功' if sm_status.get('status') == 'completed' else '失败'}")
        
        print("\n📋 第三阶段：测试状态一致性")
        
        # 7. 比较两个管理器的状态
        fp_all_status = await focus_point_manager.load_focus_points_status(test_session_id)
        sm_all_status = status_manager.get_all_focus_points_status()
        
        consistency_check = True
        for point_id in ["test_point_1", "test_point_2", "test_point_3"]:
            fp_status = fp_all_status.get(point_id, {}).get("status", "unknown")
            sm_status = sm_all_status.get(point_id, {}).get("status", "unknown")
            if fp_status != sm_status:
                print(f"❌ 状态不一致: {point_id} - FP:{fp_status} vs SM:{sm_status}")
                consistency_check = False
        
        print(f"✅ 状态一致性检查: {'通过' if consistency_check else '失败'}")
        
        print("\n📋 第四阶段：测试同步开关")
        
        # 8. 禁用同步
        status_manager.set_sync_enabled(False)
        result4 = await status_manager.update_focus_point_status(
            test_session_id, "test_point_3", "processing", "测试值3"
        )
        print(f"✅ 禁用同步后更新: {'成功' if result4 else '失败'}")
        
        # 9. 启用同步
        status_manager.set_sync_enabled(True)
        await status_manager.sync_with_focus_point_manager(test_session_id)
        print("✅ 重新启用同步并同步状态")
        
        print("\n📋 第五阶段：测试特殊功能")
        
        # 10. 测试安全设置processing状态
        result5 = await status_manager.set_point_processing(test_session_id, "test_point_1")
        print(f"✅ 安全设置processing: {'成功' if result5 else '失败'}")
        
        # 11. 验证只有一个processing状态
        processing_count = 0
        for point_id, status_info in status_manager.get_all_focus_points_status().items():
            if status_info.get("status") == "processing":
                processing_count += 1
        print(f"✅ Processing状态唯一性: {'通过' if processing_count == 1 else '失败'} (发现{processing_count}个)")
        
        # 12. 测试获取下一个待处理关注点
        next_point = await status_manager.get_next_pending_point(test_focus_points)
        print(f"✅ 获取下一个待处理点: {'成功' if next_point else '失败'} ({'找到' + next_point['id'] if next_point else '无待处理点'})")
        
        print("\n🎉 统一状态管理测试完成!")
        
        # 打印最终状态摘要
        print("\n📊 最终状态摘要:")
        final_status = status_manager.get_all_focus_points_status()
        for point_id, status_info in final_status.items():
            print(f"  {point_id}: {status_info.get('status', 'unknown')} - {status_info.get('value', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 统一状态管理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_performance_comparison():
    """测试性能对比"""
    print("\n🚀 测试性能对比...")
    
    import time
    
    try:
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        focus_point_manager = FocusPointManager(db_manager)
        status_manager = FocusPointStatusManager(db_manager, focus_point_manager)
        
        test_session_id = "test_perf_001"
        test_points = [{"id": f"perf_point_{i}", "name": f"性能测试点{i}", "priority": "P1"} for i in range(10)]
        
        # 初始化
        await focus_point_manager.initialize_focus_points(test_session_id, test_points)
        
        # 测试协同模式性能
        start_time = time.time()
        for i in range(10):
            await status_manager.update_focus_point_status(
                test_session_id, f"perf_point_{i}", "completed", f"值{i}"
            )
        sync_time = time.time() - start_time
        
        # 测试禁用同步模式性能
        status_manager.set_sync_enabled(False)
        start_time = time.time()
        for i in range(10):
            await status_manager.update_focus_point_status(
                test_session_id, f"perf_point_{i}", "pending", f"值{i}_reset"
            )
        direct_time = time.time() - start_time
        
        print(f"📈 性能对比结果:")
        print(f"  协同模式: {sync_time:.3f}秒 (10次操作)")
        print(f"  直接模式: {direct_time:.3f}秒 (10次操作)")
        print(f"  性能差异: {((sync_time - direct_time) / direct_time * 100):+.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试统一关注点状态管理...")
    print("=" * 60)
    
    # 测试统一状态管理
    unified_test_result = await test_unified_status_management()
    
    # 测试性能对比
    perf_test_result = await test_performance_comparison()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"统一状态管理: {'✅ 通过' if unified_test_result else '❌ 失败'}")
    print(f"性能对比测试: {'✅ 通过' if perf_test_result else '❌ 失败'}")
    
    if unified_test_result and perf_test_result:
        print("\n🎉 所有测试通过! 统一状态管理协同工作正常。")
        print("\n💡 关键特性:")
        print("  ✅ 两个管理器状态保持一致")
        print("  ✅ 支持同步开关控制")
        print("  ✅ 自动回退机制保证稳定性")
        print("  ✅ 保持原有功能完整性")
        return True
    else:
        print("\n❌ 部分测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生意外错误: {str(e)}")
        sys.exit(1)
