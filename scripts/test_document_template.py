import sys
import os
import json
import re

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_document_template():
    """测试文档模板格式化"""
    # 获取模板文件路径
    template_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        'backend',
        'prompts',
        'document_template.md'
    )
    
    # 检查模板文件是否存在
    if not os.path.exists(template_path):
        print(f"模板文件不存在: {template_path}")
        return
    
    # 读取模板文件
    with open(template_path, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    # 打印模板内容的前200个字符，帮助调试
    print(f"模板内容前200个字符: {template_content[:200]}")
    
    # 提取提示词部分和模板部分
    start_marker = "```template"
    end_marker = "```"
    
    start_pos = template_content.find(start_marker)
    if start_pos != -1:
        prompt_part = template_content[:start_pos].strip()
        print("\n===== 提示词部分 =====\n")
        print(f"{prompt_part[:200]}...(省略)")
        
        start_pos += len(start_marker)
        end_pos = template_content.find(end_marker, start_pos)
        if end_pos != -1:
            template = template_content[start_pos:end_pos].strip()
            print("\n===== 成功提取模板部分 =====\n")
            print(f"{template[:200]}...(省略)")
            
            after_template = template_content[end_pos + len(end_marker):].strip()
            print("\n===== 模板后部分 =====\n")
            print(f"{after_template[:200]}...(省略)")
        else:
            print("找不到结束标记")
            template = ""
    else:
        print("找不到开始标记")
        template = ""
    
    if not template:
        print("无法提取模板，测试终止")
        return
    
    # 测试数据
    test_data = {
        "project_name": "测试项目",
        "date": "2023-08-15",
        "overview": "这是一个测试项目的概述。",
        "requirements": "这是测试项目的需求描述。",
        "Delivery_time": "2023年9月30日",
        "Delivery_Amount": "15000",
        "recommendations": "这是对测试项目的建议。"
    }
    
    try:
        # 格式化模板
        formatted_doc = template.format(**test_data)
        print("\n===== 格式化后的文档 =====\n")
        print(formatted_doc)
        print("\n格式化成功!")
    except KeyError as e:
        print(f"模板格式化错误: 缺少键 {e}")
        # 分析模板中的所有占位符
        placeholders = re.findall(r'\{([^{}]*)\}', template)
        print("\n模板中的占位符:")
        for p in placeholders:
            print(f"- {p}")
        
        print("\n测试数据中的键:")
        for k in test_data.keys():
            print(f"- {k}")
            if isinstance(test_data[k], dict):
                for sub_k in test_data[k].keys():
                    print(f"  - {k}[{sub_k}]")
    except Exception as e:
        print(f"模板格式化失败: {str(e)}")

def test_parse_llm_response():
    """测试LLM响应解析"""
    # 测试JSON格式响应
    json_response = """
以下是润色和提炼后的信息：

```json
{
  "overview": "这是一个电商平台升级项目，旨在提升用户体验和转化率。",
  "requirements": "1. 实现个性化推荐功能\n2. 优化移动端支付流程\n3. 增加社交分享功能",
  "Delivery_time": "2023年12月31日前",
  "Delivery_Amount": "120000",
  "recommendations": "1. 建议分阶段实施，先上线核心功能\n2. 注意数据安全合规\n3. 预留接口扩展性"
}
```
    """
    
    # 测试键值对格式响应
    kv_response = """
以下是润色和提炼后的信息：

**{overview}**: 这是一个电商平台升级项目，旨在提升用户体验和转化率。

**{requirements}**: 
1. 实现个性化推荐功能
2. 优化移动端支付流程
3. 增加社交分享功能

**{Delivery_time}**: 2023年12月31日前

**{Delivery_Amount}**: 120000

**{recommendations}**: 
1. 建议分阶段实施，先上线核心功能
2. 注意数据安全合规
3. 预留接口扩展性
    """
    
    # 导入DocumentGenerator类
    try:
        from backend.agents.document_generator import DocumentGenerator
        
        # 创建DocumentGenerator实例
        doc_gen = DocumentGenerator()
        
        # 测试JSON格式响应解析
        print("\n===== 测试JSON格式响应解析 =====\n")
        json_result = doc_gen._parse_llm_response(json_response)
        print(json.dumps(json_result, ensure_ascii=False, indent=2))
        
        # 测试键值对格式响应解析
        print("\n===== 测试键值对格式响应解析 =====\n")
        kv_result = doc_gen._parse_llm_response(kv_response)
        print(json.dumps(kv_result, ensure_ascii=False, indent=2))
        
        # 测试模板应用
        print("\n===== 测试模板应用 =====\n")
        test_data = {
            "project_name": "电商平台升级项目",
            "date": "2023-08-15",
            "overview": json_result.get("overview", "无项目概述"),
            "requirements": json_result.get("requirements", "无需求描述"),
            "Delivery_time": json_result.get("Delivery_time", "待定"),
            "Delivery_Amount": json_result.get("Delivery_Amount", "待定"),
            "recommendations": json_result.get("recommendations", "无项目建议")
        }
        
        formatted_doc = doc_gen._apply_template(test_data)
        print(formatted_doc)
        
    except ImportError as e:
        print(f"导入DocumentGenerator类失败: {str(e)}")
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")

def test_template_extraction():
    """测试模板提取"""
    # 获取模板文件路径
    template_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        'backend',
        'prompts',
        'document_template.md'
    )
    
    # 检查模板文件是否存在
    if not os.path.exists(template_path):
        print(f"模板文件不存在: {template_path}")
        return
    
    # 读取模板文件
    with open(template_path, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    # 提取模板部分
    start_marker = "```template"
    end_marker = "```"
    
    start_pos = template_content.find(start_marker)
    if start_pos != -1:
        start_pos += len(start_marker)
        end_pos = template_content.find(end_marker, start_pos)
        if end_pos != -1:
            template = template_content[start_pos:end_pos].strip()
            print("\n===== 成功提取模板 =====\n")
            print(template)
            
            # 检查模板中的占位符
            placeholders = re.findall(r'\{([^{}]*)\}', template)
            print("\n===== 模板中的占位符 =====\n")
            for p in placeholders:
                print(f"- {p}")
        else:
            print("找不到结束标记")
    else:
        print("找不到开始标记")

if __name__ == "__main__":
    test_document_template()
    test_parse_llm_response()
    test_template_extraction()  # 添加新的测试函数
