#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库查询优化效果的脚本
验证减少重复查询的实现
"""

import asyncio
import sys
import os
import time
from unittest.mock import patch, MagicMock

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.data.db.database_manager import DatabaseManager
from backend.data.db.focus_point_manager import FocusPointManager
from backend.agents.conversation_flow import FocusPointStatusManager

class QueryCounter:
    """查询计数器，用于统计数据库查询次数"""
    
    def __init__(self):
        self.query_count = 0
        self.queries = []
        
    def reset(self):
        self.query_count = 0
        self.queries = []
        
    def record_query(self, query, params=None):
        self.query_count += 1
        self.queries.append((query, params))

async def test_conversation_exists_optimization():
    """测试会话存在性检查优化"""
    print("🔍 测试会话存在性检查优化...")
    
    try:
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        focus_point_manager = FocusPointManager(db_manager)
        status_manager = FocusPointStatusManager(db_manager, focus_point_manager)
        
        test_session_id = "test_query_opt_001"
        counter = QueryCounter()
        
        # 模拟数据库查询计数
        original_execute_query = db_manager.execute_query
        original_record_exists = db_manager.record_exists
        
        async def counted_execute_query(query, params=None):
            counter.record_query(query, params)
            return await original_execute_query(query, params)
            
        async def counted_record_exists(query, params=None):
            counter.record_query(query, params)
            return await original_record_exists(query, params)
        
        db_manager.execute_query = counted_execute_query
        db_manager.record_exists = counted_record_exists
        
        # 测试优化前的行为（多次调用会话检查）
        print("\n📊 测试优化效果:")
        
        # 第一次调用 - 应该执行查询
        counter.reset()
        await status_manager._ensure_conversation_exists(test_session_id)
        first_call_queries = counter.query_count
        print(f"  第一次会话检查查询数: {first_call_queries}")
        
        # 第二次调用 - 应该跳过查询（已缓存）
        counter.reset()
        await status_manager._ensure_conversation_exists(test_session_id)
        second_call_queries = counter.query_count
        print(f"  第二次会话检查查询数: {second_call_queries}")
        
        # 验证优化效果
        if second_call_queries == 0:
            print("  ✅ 会话存在性检查优化成功 - 避免了重复查询")
            optimization_success = True
        else:
            print("  ❌ 会话存在性检查优化失败 - 仍有重复查询")
            optimization_success = False
            
        return optimization_success
        
    except Exception as e:
        print(f"❌ 会话存在性检查优化测试失败: {str(e)}")
        return False

async def test_status_loading_optimization():
    """测试状态加载优化"""
    print("\n🔍 测试状态加载优化...")
    
    try:
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        focus_point_manager = FocusPointManager(db_manager)
        status_manager = FocusPointStatusManager(db_manager, focus_point_manager)
        
        test_session_id = "test_query_opt_002"
        counter = QueryCounter()
        
        # 模拟数据库查询计数
        original_execute_query = db_manager.execute_query
        
        async def counted_execute_query(query, params=None):
            counter.record_query(query, params)
            return await original_execute_query(query, params)
        
        db_manager.execute_query = counted_execute_query
        
        # 初始化测试数据
        test_focus_points = [
            {"id": "test_point_1", "name": "测试点1", "priority": "P0"},
            {"id": "test_point_2", "name": "测试点2", "priority": "P1"}
        ]
        await focus_point_manager.initialize_focus_points(test_session_id, test_focus_points)
        
        # 设置当前会话ID
        status_manager.current_session_id = test_session_id
        
        print("\n📊 测试状态加载优化:")
        
        # 第一次调用get_processing_point - 应该加载状态
        counter.reset()
        await status_manager.get_processing_point()
        first_call_queries = counter.query_count
        print(f"  第一次获取processing点查询数: {first_call_queries}")
        
        # 第二次调用get_processing_point - 应该使用内存状态
        counter.reset()
        await status_manager.get_processing_point()
        second_call_queries = counter.query_count
        print(f"  第二次获取processing点查询数: {second_call_queries}")
        
        # 调用get_next_pending_point - 应该使用已加载的状态
        counter.reset()
        await status_manager.get_next_pending_point(test_focus_points)
        pending_call_queries = counter.query_count
        print(f"  获取下一个待处理点查询数: {pending_call_queries}")
        
        # 验证优化效果
        if second_call_queries == 0 and pending_call_queries == 0:
            print("  ✅ 状态加载优化成功 - 避免了重复查询")
            optimization_success = True
        else:
            print("  ❌ 状态加载优化失败 - 仍有重复查询")
            optimization_success = False
            
        return optimization_success
        
    except Exception as e:
        print(f"❌ 状态加载优化测试失败: {str(e)}")
        return False

async def test_single_point_sync_optimization():
    """测试单点同步优化"""
    print("\n🔍 测试单点同步优化...")
    
    try:
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        focus_point_manager = FocusPointManager(db_manager)
        status_manager = FocusPointStatusManager(db_manager, focus_point_manager)
        
        test_session_id = "test_query_opt_003"
        counter = QueryCounter()
        
        # 模拟数据库查询计数
        original_execute_query = db_manager.execute_query
        original_get_record = db_manager.get_record
        
        async def counted_execute_query(query, params=None):
            counter.record_query(query, params)
            return await original_execute_query(query, params)
            
        async def counted_get_record(query, params=None):
            counter.record_query(query, params)
            return await original_get_record(query, params)
        
        db_manager.execute_query = counted_execute_query
        db_manager.get_record = counted_get_record
        
        # 初始化测试数据
        test_focus_points = [
            {"id": "test_point_1", "name": "测试点1", "priority": "P0"}
        ]
        await focus_point_manager.initialize_focus_points(test_session_id, test_focus_points)
        
        print("\n📊 测试单点同步优化:")
        
        # 测试单点同步 vs 全量同步的查询数量
        counter.reset()
        await status_manager._sync_single_point_from_db(test_session_id, "test_point_1")
        single_sync_queries = counter.query_count
        print(f"  单点同步查询数: {single_sync_queries}")
        
        counter.reset()
        await status_manager.sync_with_focus_point_manager(test_session_id)
        full_sync_queries = counter.query_count
        print(f"  全量同步查询数: {full_sync_queries}")
        
        # 验证优化效果
        if single_sync_queries < full_sync_queries:
            print(f"  ✅ 单点同步优化成功 - 减少了 {full_sync_queries - single_sync_queries} 次查询")
            optimization_success = True
        else:
            print("  ❌ 单点同步优化失败 - 查询数量未减少")
            optimization_success = False
            
        return optimization_success
        
    except Exception as e:
        print(f"❌ 单点同步优化测试失败: {str(e)}")
        return False

async def test_performance_comparison():
    """测试性能对比"""
    print("\n🚀 测试性能对比...")
    
    try:
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        focus_point_manager = FocusPointManager(db_manager)
        status_manager = FocusPointStatusManager(db_manager, focus_point_manager)
        
        test_session_id = "test_perf_comp_001"
        
        # 初始化测试数据
        test_focus_points = [
            {"id": f"perf_point_{i}", "name": f"性能测试点{i}", "priority": "P1"} 
            for i in range(5)
        ]
        await focus_point_manager.initialize_focus_points(test_session_id, test_focus_points)
        status_manager.current_session_id = test_session_id
        
        # 测试优化后的性能
        start_time = time.time()
        for i in range(10):
            await status_manager.get_processing_point()
            await status_manager.get_next_pending_point(test_focus_points)
        optimized_time = time.time() - start_time
        
        print(f"📈 性能测试结果:")
        print(f"  优化后10次查询耗时: {optimized_time:.3f}秒")
        print(f"  平均每次查询耗时: {optimized_time/10:.3f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能对比测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试数据库查询优化...")
    print("=" * 60)
    
    # 测试各项优化
    conv_exists_result = await test_conversation_exists_optimization()
    status_loading_result = await test_status_loading_optimization()
    single_sync_result = await test_single_point_sync_optimization()
    performance_result = await test_performance_comparison()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"会话存在性检查优化: {'✅ 通过' if conv_exists_result else '❌ 失败'}")
    print(f"状态加载优化: {'✅ 通过' if status_loading_result else '❌ 失败'}")
    print(f"单点同步优化: {'✅ 通过' if single_sync_result else '❌ 失败'}")
    print(f"性能测试: {'✅ 通过' if performance_result else '❌ 失败'}")
    
    all_passed = all([conv_exists_result, status_loading_result, single_sync_result, performance_result])
    
    if all_passed:
        print("\n🎉 所有查询优化测试通过!")
        print("\n💡 优化效果:")
        print("  ✅ 避免了重复的会话存在性检查")
        print("  ✅ 减少了状态加载的重复查询")
        print("  ✅ 实现了高效的单点同步")
        print("  ✅ 提升了整体查询性能")
        return True
    else:
        print("\n❌ 部分查询优化测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生意外错误: {str(e)}")
        sys.exit(1)
