#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试reply_factory问题

检查ConversationFlow中reply_factory的实际类型和状态
"""

import os
import sys
import asyncio
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.utils.logging_config import configure_logging
from backend.agents.conversation_flow import AutoGenConversationFlowAgent
from backend.agents.llm_service import AutoGenLLMServiceAgent

# 配置日志
configure_logging()
logger = logging.getLogger(__name__)

async def debug_reply_factory():
    """调试reply_factory的状态"""
    try:
        print("🔍 开始调试reply_factory问题")
        print("=" * 60)
        
        # 初始化LLM客户端
        llm_client = AutoGenLLMServiceAgent()
        print("✅ LLM客户端初始化成功")
        
        # 初始化ConversationFlow
        conversation_flow = AutoGenConversationFlowAgent(llm_client=llm_client)
        print("✅ ConversationFlow初始化成功")
        
        # 检查reply_factory的类型
        print(f"\n📊 reply_factory状态检查:")
        print(f"reply_factory类型: {type(conversation_flow.reply_factory)}")
        print(f"reply_factory值: {conversation_flow.reply_factory}")
        
        if conversation_flow.reply_factory:
            print(f"reply_factory.__class__.__name__: {conversation_flow.reply_factory.__class__.__name__}")
            print(f"reply_factory.__module__: {conversation_flow.reply_factory.__class__.__module__}")
            
            # 检查是否有generate_domain_guidance方法
            has_method = hasattr(conversation_flow.reply_factory, 'generate_domain_guidance')
            print(f"是否有generate_domain_guidance方法: {has_method}")
            
            if has_method:
                method = getattr(conversation_flow.reply_factory, 'generate_domain_guidance')
                print(f"generate_domain_guidance方法类型: {type(method)}")
            else:
                print("❌ 缺少generate_domain_guidance方法")
                # 列出所有可用的方法
                methods = [attr for attr in dir(conversation_flow.reply_factory) 
                          if not attr.startswith('_') and callable(getattr(conversation_flow.reply_factory, attr))]
                print(f"可用方法: {methods}")
        else:
            print("❌ reply_factory为None")
        
        # 检查integrated_reply_system的reply_factory
        print(f"\n📊 integrated_reply_system.reply_factory状态检查:")
        if hasattr(conversation_flow, 'integrated_reply_system') and conversation_flow.integrated_reply_system:
            integrated_factory = conversation_flow.integrated_reply_system.reply_factory
            print(f"integrated_reply_system.reply_factory类型: {type(integrated_factory)}")
            print(f"integrated_reply_system.reply_factory.__class__.__name__: {integrated_factory.__class__.__name__}")
            
            has_method = hasattr(integrated_factory, 'generate_domain_guidance')
            print(f"是否有generate_domain_guidance方法: {has_method}")
        else:
            print("❌ integrated_reply_system未初始化或为None")
        
        # 检查是否有任何属性指向了错误的对象
        print(f"\n📊 其他相关属性检查:")
        if hasattr(conversation_flow, 'dynamic_generator'):
            print(f"dynamic_generator类型: {type(getattr(conversation_flow, 'dynamic_generator', None))}")
        
        # 尝试手动创建正确的factory
        print(f"\n🔧 手动创建DynamicReplyFactory测试:")
        from backend.agents.dynamic_reply_generator import DynamicReplyGenerator, DynamicReplyFactory
        
        test_generator = DynamicReplyGenerator(llm_client=llm_client)
        test_factory = DynamicReplyFactory(test_generator)
        
        print(f"测试factory类型: {type(test_factory)}")
        print(f"测试factory.__class__.__name__: {test_factory.__class__.__name__}")
        
        has_method = hasattr(test_factory, 'generate_domain_guidance')
        print(f"测试factory是否有generate_domain_guidance方法: {has_method}")
        
        if has_method:
            print("✅ 手动创建的factory正常")
        else:
            print("❌ 手动创建的factory也有问题")
        
        # 检查模块导入情况
        print(f"\n📦 模块导入检查:")
        import backend.agents.dynamic_reply_generator as drg_module
        print(f"DynamicReplyFactory类: {drg_module.DynamicReplyFactory}")
        print(f"DynamicReplyGenerator类: {drg_module.DynamicReplyGenerator}")
        
        # 检查类的方法
        factory_methods = [method for method in dir(drg_module.DynamicReplyFactory) 
                          if not method.startswith('_')]
        print(f"DynamicReplyFactory的方法: {factory_methods}")
        
        generator_methods = [method for method in dir(drg_module.DynamicReplyGenerator) 
                            if not method.startswith('_')]
        print(f"DynamicReplyGenerator的方法: {generator_methods}")
        
        print(f"\n{'='*60}")
        print("🎯 调试完成！")
        
    except Exception as e:
        logger.error(f"调试过程中出错: {str(e)}", exc_info=True)
        print(f"❌ 调试失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(debug_reply_factory())
