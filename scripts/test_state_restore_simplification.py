#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试状态恢复逻辑简化效果的脚本
验证重构后的状态恢复方法是否正常工作
"""

import asyncio
import sys
import os
import json
from unittest.mock import AsyncMock, MagicMock, patch

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.data.db.database_manager import DatabaseManager
from backend.data.db.focus_point_manager import FocusPointManager
from backend.agents.conversation_flow import AutoGenConversationFlowAgent, ConversationState

async def test_state_restore_with_documents():
    """测试有文档的会话状态恢复"""
    print("🔍 测试有文档的会话状态恢复...")
    
    try:
        # 创建模拟的组件
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        
        # 创建模拟的LLM客户端和其他依赖
        mock_llm_client = AsyncMock()
        mock_knowledge_base = MagicMock()
        mock_domain_classifier = AsyncMock()
        mock_category_classifier = AsyncMock()
        mock_information_extractor = AsyncMock()
        
        # 创建会话流代理
        agent = AutoGenConversationFlowAgent(
            llm_client=mock_llm_client,
            knowledge_base_agent=mock_knowledge_base,
            domain_classifier_agent=mock_domain_classifier,
            category_classifier_agent=mock_category_classifier,
            information_extractor_agent=mock_information_extractor,
            db_path="backend/data/aidatabase.db"
        )
        
        test_session_id = "test_restore_docs_001"
        
        # 模拟数据库中有领域/类别信息
        with patch.object(agent, '_restore_domain_category_info', return_value=True):
            # 模拟数据库中有文档
            with patch.object(db_manager, 'get_documents_by_conversation_id', return_value=[{"id": "doc1"}]):
                
                # 执行状态恢复
                await agent.try_restore_session_state(test_session_id)
                
                # 验证状态
                if agent.current_state == ConversationState.DOCUMENTING:
                    print("  ✅ 有文档的会话正确恢复到DOCUMENTING状态")
                    return True
                else:
                    print(f"  ❌ 状态恢复错误，期望DOCUMENTING，实际{agent.current_state}")
                    return False
                    
    except Exception as e:
        print(f"❌ 有文档的会话状态恢复测试失败: {str(e)}")
        return False

async def test_state_restore_without_documents():
    """测试无文档的会话状态恢复"""
    print("\n🔍 测试无文档的会话状态恢复...")
    
    try:
        # 创建模拟的组件
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        
        # 创建模拟的LLM客户端和其他依赖
        mock_llm_client = AsyncMock()
        mock_knowledge_base = MagicMock()
        mock_domain_classifier = AsyncMock()
        mock_category_classifier = AsyncMock()
        mock_information_extractor = AsyncMock()
        
        # 创建会话流代理
        agent = AutoGenConversationFlowAgent(
            llm_client=mock_llm_client,
            knowledge_base_agent=mock_knowledge_base,
            domain_classifier_agent=mock_domain_classifier,
            category_classifier_agent=mock_category_classifier,
            information_extractor_agent=mock_information_extractor,
            db_path="backend/data/aidatabase.db"
        )
        
        test_session_id = "test_restore_no_docs_001"
        
        # 模拟数据库中有领域/类别信息
        with patch.object(agent, '_restore_domain_category_info', return_value=True):
            # 模拟数据库中无文档
            with patch.object(db_manager, 'get_documents_by_conversation_id', return_value=[]):
                # 模拟状态管理器的方法
                with patch.object(agent.status_manager, '_load_status_to_memory', return_value={}):
                    with patch.object(agent.status_manager, 'clear_all_processing_status', return_value=True):
                        
                        # 执行状态恢复
                        await agent.try_restore_session_state(test_session_id)
                        
                        # 验证状态
                        if agent.current_state == ConversationState.COLLECTING_INFO:
                            print("  ✅ 无文档的会话正确恢复到COLLECTING_INFO状态")
                            return True
                        else:
                            print(f"  ❌ 状态恢复错误，期望COLLECTING_INFO，实际{agent.current_state}")
                            return False
                    
    except Exception as e:
        print(f"❌ 无文档的会话状态恢复测试失败: {str(e)}")
        return False

async def test_state_restore_no_domain():
    """测试无领域信息的会话状态恢复"""
    print("\n🔍 测试无领域信息的会话状态恢复...")
    
    try:
        # 创建模拟的组件
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        
        # 创建模拟的LLM客户端和其他依赖
        mock_llm_client = AsyncMock()
        mock_knowledge_base = MagicMock()
        mock_domain_classifier = AsyncMock()
        mock_category_classifier = AsyncMock()
        mock_information_extractor = AsyncMock()
        
        # 创建会话流代理
        agent = AutoGenConversationFlowAgent(
            llm_client=mock_llm_client,
            knowledge_base_agent=mock_knowledge_base,
            domain_classifier_agent=mock_domain_classifier,
            category_classifier_agent=mock_category_classifier,
            information_extractor_agent=mock_information_extractor,
            db_path="backend/data/aidatabase.db"
        )
        
        test_session_id = "test_restore_no_domain_001"
        
        # 模拟数据库中无领域/类别信息
        with patch.object(agent, '_restore_domain_category_info', return_value=False):
            
            # 执行状态恢复
            await agent.try_restore_session_state(test_session_id)
            
            # 验证状态
            if agent.current_state == ConversationState.IDLE:
                print("  ✅ 无领域信息的会话正确保持IDLE状态")
                return True
            else:
                print(f"  ❌ 状态恢复错误，期望IDLE，实际{agent.current_state}")
                return False
                
    except Exception as e:
        print(f"❌ 无领域信息的会话状态恢复测试失败: {str(e)}")
        return False

async def test_state_restore_error_handling():
    """测试状态恢复的错误处理"""
    print("\n🔍 测试状态恢复的错误处理...")
    
    try:
        # 创建模拟的组件
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        
        # 创建模拟的LLM客户端和其他依赖
        mock_llm_client = AsyncMock()
        mock_knowledge_base = MagicMock()
        mock_domain_classifier = AsyncMock()
        mock_category_classifier = AsyncMock()
        mock_information_extractor = AsyncMock()
        
        # 创建会话流代理
        agent = AutoGenConversationFlowAgent(
            llm_client=mock_llm_client,
            knowledge_base_agent=mock_knowledge_base,
            domain_classifier_agent=mock_domain_classifier,
            category_classifier_agent=mock_category_classifier,
            information_extractor_agent=mock_information_extractor,
            db_path="backend/data/aidatabase.db"
        )
        
        test_session_id = "test_restore_error_001"
        
        # 模拟恢复过程中出现异常
        with patch.object(agent, '_restore_domain_category_info', side_effect=Exception("模拟错误")):
            
            # 执行状态恢复
            await agent.try_restore_session_state(test_session_id)
            
            # 验证错误处理后的状态
            if agent.current_state == ConversationState.IDLE:
                print("  ✅ 错误处理正确，系统恢复到安全的IDLE状态")
                return True
            else:
                print(f"  ❌ 错误处理失败，期望IDLE，实际{agent.current_state}")
                return False
                
    except Exception as e:
        print(f"❌ 状态恢复错误处理测试失败: {str(e)}")
        return False

async def test_method_decomposition():
    """测试方法分解的效果"""
    print("\n🔍 测试方法分解的效果...")
    
    try:
        # 创建模拟的组件
        db_manager = DatabaseManager("backend/data/aidatabase.db")
        
        # 创建模拟的LLM客户端和其他依赖
        mock_llm_client = AsyncMock()
        mock_knowledge_base = MagicMock()
        mock_domain_classifier = AsyncMock()
        mock_category_classifier = AsyncMock()
        mock_information_extractor = AsyncMock()
        
        # 创建会话流代理
        agent = AutoGenConversationFlowAgent(
            llm_client=mock_llm_client,
            knowledge_base_agent=mock_knowledge_base,
            domain_classifier_agent=mock_domain_classifier,
            category_classifier_agent=mock_category_classifier,
            information_extractor_agent=mock_information_extractor,
            db_path="backend/data/aidatabase.db"
        )
        
        test_session_id = "test_decomposition_001"
        
        # 验证新方法是否存在
        methods_to_check = [
            '_execute_session_restore',
            '_determine_conversation_state',
            '_transition_to_restored_state',
            '_initialize_collecting_info_state',
            '_handle_no_active_state',
            '_handle_restore_failure'
        ]
        
        missing_methods = []
        for method_name in methods_to_check:
            if not hasattr(agent, method_name):
                missing_methods.append(method_name)
        
        if not missing_methods:
            print("  ✅ 所有新分解的方法都存在")
            
            # 测试方法调用链
            with patch.object(agent, '_restore_domain_category_info', return_value=True):
                with patch.object(agent, '_determine_conversation_state', return_value="COLLECTING_INFO"):
                    with patch.object(agent, '_transition_to_restored_state', return_value=None):
                        
                        await agent._execute_session_restore(test_session_id)
                        print("  ✅ 方法调用链正常工作")
                        return True
        else:
            print(f"  ❌ 缺少方法: {missing_methods}")
            return False
            
    except Exception as e:
        print(f"❌ 方法分解测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试状态恢复逻辑简化...")
    print("=" * 60)
    
    # 测试各种状态恢复场景
    docs_test_result = await test_state_restore_with_documents()
    no_docs_test_result = await test_state_restore_without_documents()
    no_domain_test_result = await test_state_restore_no_domain()
    error_handling_result = await test_state_restore_error_handling()
    decomposition_result = await test_method_decomposition()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"有文档的状态恢复: {'✅ 通过' if docs_test_result else '❌ 失败'}")
    print(f"无文档的状态恢复: {'✅ 通过' if no_docs_test_result else '❌ 失败'}")
    print(f"无领域信息的状态恢复: {'✅ 通过' if no_domain_test_result else '❌ 失败'}")
    print(f"错误处理测试: {'✅ 通过' if error_handling_result else '❌ 失败'}")
    print(f"方法分解测试: {'✅ 通过' if decomposition_result else '❌ 失败'}")
    
    all_passed = all([
        docs_test_result, no_docs_test_result, no_domain_test_result,
        error_handling_result, decomposition_result
    ])
    
    if all_passed:
        print("\n🎉 所有状态恢复逻辑简化测试通过!")
        print("\n💡 简化效果:")
        print("  ✅ 复杂的嵌套逻辑被拆分为清晰的小方法")
        print("  ✅ 每个方法职责单一，易于理解和测试")
        print("  ✅ 错误处理更加清晰和可靠")
        print("  ✅ 保持了原有的调用接口")
        print("  ✅ 提高了代码的可维护性")
        return True
    else:
        print("\n❌ 部分状态恢复逻辑简化测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生意外错误: {str(e)}")
        sys.exit(1)
