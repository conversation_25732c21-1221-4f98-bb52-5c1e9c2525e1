#!/usr/bin/env python
"""
测试聊天API的脚本
"""

import requests
import json
import time
import sys

def send_message(message, session_id=None):
    """发送消息到聊天API"""
    url = "http://localhost:8000/chat"
    data = {
        "message": message,
        "session_id": session_id
    }
    
    print(f"发送消息: {message}")
    response = requests.post(url, json=data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"收到响应: {result['response']}")
        return result
    else:
        print(f"请求失败: {response.status_code} - {response.text}")
        return None

def main():
    """主函数"""
    # 创建一个会话
    session_id = f"test_session_{int(time.time())}"
    print(f"会话ID: {session_id}")
    
    # 发送第一条消息
    result = send_message("你好，我想设计一个电商网站", session_id)
    if not result:
        return
    
    # 等待一下
    time.sleep(1)
    
    # 发送第二条消息
    result = send_message("我想要包含商品展示、购物车和支付功能", session_id)
    if not result:
        return
    
    # 等待一下
    time.sleep(1)
    
    # 发送第三条消息
    result = send_message("我需要一个管理后台来管理商品和订单", session_id)
    if not result:
        return
    
    print(f"\n测试完成，会话ID: {session_id}")
    print(f"可以使用以下命令查看会话日志:")
    print(f"python scripts/view_session.py {session_id}")
    print(f"python scripts/view_session.py {session_id} --format markdown --output {session_id}.md")

if __name__ == "__main__":
    main()
