#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修订后的文档是否包含操作指引

验证文档修改后是否正确添加了操作指引
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.utils.logging_config import configure_logging
from backend.agents.conversation_flow import AutoGenConversationFlowAgent
from backend.agents.llm_service import AutoGenLLMServiceAgent

# 配置日志
configure_logging()
logger = logging.getLogger(__name__)

async def test_document_guidance():
    """测试修订后的文档是否包含操作指引"""
    try:
        print("🔧 开始测试修订后文档的操作指引")
        print("=" * 60)
        
        # 初始化LLM客户端和ConversationFlow
        llm_client = AutoGenLLMServiceAgent()
        conversation_flow = AutoGenConversationFlowAgent(llm_client=llm_client)
        
        # 模拟会话ID
        test_session_id = "test_session_guidance"
        
        print("📄 准备测试环境...")
        
        # 1. 创建会话记录
        await conversation_flow.db_manager.execute_update(
            """
            INSERT OR REPLACE INTO conversations (conversation_id, domain_id, category_id, created_at)
            VALUES (?, ?, ?, ?)
            """,
            (test_session_id, "design", "ui_design", datetime.now())
        )
        
        # 2. 创建一个测试文档
        test_document_content = """# UI设计项目需求文档

## 项目概述
设计一个现代化的小程序UI界面。

## 设计要求
- 整体风格简洁现代
- 适合90、00后年轻群体

## 页面数量
共60张页面。

## 交付时间
预计1个月完成。

## 预算
10000元
"""
        
        await conversation_flow.db_manager.execute_update(
            """
            INSERT OR REPLACE INTO documents (document_id, conversation_id, content, version, created_at, updated_at, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
            ("test_doc_guidance", test_session_id, test_document_content, 1, datetime.now(), datetime.now(), "draft")
        )
        
        # 3. 设置ConversationFlow状态
        conversation_flow.current_domain = "design"
        conversation_flow.current_category = "ui_design"
        
        from backend.agents.conversation_flow import ConversationState
        conversation_flow.transition_to(ConversationState.DOCUMENTING)
        
        print("✅ 测试环境准备完成")
        print(f"当前状态: {conversation_flow.current_state.name}")
        
        # 4. 测试文档修改请求
        print(f"\n{'='*60}")
        print("🧪 测试文档修改并检查操作指引")
        print(f"{'='*60}")
        
        user_input = "将预算改为15000元"
        print(f"用户输入: {user_input}")
        
        # 调用process_message
        result = await conversation_flow.process_message({
            "text": user_input,
            "session_id": test_session_id
        })
        
        print(f"\n📊 处理结果:")
        print(f"成功: {result is not None}")
        
        if result:
            response_text = result.get('text_response', '')
            print(f"响应长度: {len(response_text)} 字符")
            
            # 检查是否包含操作指引
            guidance_indicators = [
                "文档操作指引",
                "输入'确认'",
                "指出需要修改的部分",
                "输入'新需求'",
                "输入'全部重来'"
            ]
            
            found_indicators = []
            for indicator in guidance_indicators:
                if indicator in response_text:
                    found_indicators.append(indicator)
            
            print(f"\n📋 操作指引检查:")
            print(f"找到的指引元素: {len(found_indicators)}/{len(guidance_indicators)}")
            
            for indicator in found_indicators:
                print(f"  ✅ {indicator}")
            
            missing_indicators = [ind for ind in guidance_indicators if ind not in found_indicators]
            for indicator in missing_indicators:
                print(f"  ❌ {indicator}")
            
            if len(found_indicators) >= 4:  # 至少包含4个指引元素
                print("\n🎉 操作指引添加成功！")
            else:
                print("\n⚠️  操作指引可能不完整")
            
            # 显示完整响应（截取部分）
            print(f"\n📄 完整响应预览:")
            if len(response_text) > 500:
                print(f"{response_text[:300]}...")
                print("...")
                print(f"{response_text[-200:]}")
            else:
                print(response_text)
        else:
            print("❌ 处理失败，返回None")
        
        print(f"\n{'='*60}")
        print("🎉 测试完成！")
        
        # 清理测试数据
        print("\n🧹 清理测试数据...")
        await conversation_flow.db_manager.execute_update(
            "DELETE FROM conversations WHERE conversation_id = ?",
            (test_session_id,)
        )
        await conversation_flow.db_manager.execute_update(
            "DELETE FROM documents WHERE conversation_id = ?",
            (test_session_id,)
        )
        print("✅ 测试数据清理完成")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}", exc_info=True)
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_document_guidance())
