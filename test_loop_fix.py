#!/usr/bin/env python3
"""
测试循环回复修复的脚本
"""

import requests
import json
import time

API_BASE = "http://localhost:8000"

def send_message(session_id, message):
    """发送消息到API"""
    url = f"{API_BASE}/chat"
    payload = {
        "message": message,
        "session_id": session_id
    }
    
    print(f"\n🔵 用户输入: {message}")
    response = requests.post(url, json=payload)
    
    if response.status_code == 200:
        result = response.json()
        ai_response = result.get("response", "")
        print(f"🤖 AI回复: {ai_response}")
        return ai_response
    else:
        print(f"❌ 请求失败: {response.status_code}")
        print(response.text)
        return None

def test_loop_scenario():
    """测试之前出现循环的场景"""
    session_id = f"test_loop_fix_{int(time.time())}"
    
    print("=" * 60)
    print("测试循环回复修复")
    print("=" * 60)
    
    # 模拟之前的对话流程
    messages = [
        "根据已编写好的文案排版设计公众号内容，素材不得含有侵权图片",
        "允许对内容进行润色和调整",
        "3天",
        "100元",
        "其他补充说明方面的建议",
        "没有什么了"
    ]
    
    for i, message in enumerate(messages, 1):
        print(f"\n--- 第 {i} 轮对话 ---")
        response = send_message(session_id, message)
        
        if response is None:
            print("❌ 测试失败，API请求出错")
            return False
        
        # 检查是否出现循环回复
        if i >= 4 and "我注意到还有几个重要信息需要确认" in response:
            if i == 4:
                print("✅ 第4轮：AI正常提供选择")
            elif i >= 5:
                print(f"⚠️  第{i}轮：检测到可能的循环回复")
                # 但这次应该有所不同
                if "其他补充说明" in response and "特殊要求" in response:
                    print("✅ AI提供了针对性的建议，避免了完全相同的回复")
                elif "没有什么了" in message and ("生成文档" in response or "继续" in response):
                    print("✅ AI正确理解了用户的完成意图")
                else:
                    print("❌ 仍然存在循环回复问题")
        
        time.sleep(1)  # 避免请求过快
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_loop_scenario()
