import React from 'react';
// Import shared types
import { DomainResult, CategoryResult, FocusPointStatus } from '../types';


interface AnalysisResultAreaProps {
  domainResult: DomainResult | null;
  categoryResult: CategoryResult | null;
  focusPointsStatus: FocusPointStatus[] | null; // Uses the imported FocusPointStatus now
}

const AnalysisResultArea: React.FC<AnalysisResultAreaProps> = ({
  domainResult,
  categoryResult,
  focusPointsStatus,
}) => {
  // Helper function to determine display status
  const getDisplayStatus = (value: string | null): string => {
    return value !== null ? '已覆盖' : '未覆盖';
  };

  return (
    <div className="h-full bg-white">
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center">
          <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
          AI 分析结果
        </h3>
      </div>
      <div className="p-4 h-full overflow-y-auto text-sm space-y-4">

        {/* 领域分类结果区域 */}
        <div className="bg-white border border-gray-200 rounded-lg p-3">
          <h4 className="text-sm font-medium mb-3 text-gray-800 flex items-center">
            <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2"></div>
            领域分类
          </h4>
          {domainResult ? (
            <div className="bg-gray-50 p-3 rounded-md">
              <pre className="text-xs overflow-x-auto text-gray-700">
                {JSON.stringify(domainResult, null, 2)}
              </pre>
            </div>
          ) : (
            <p className="text-xs text-gray-500 italic">暂无领域分类结果。</p>
          )}
        </div>

        {/* 类别识别结果区域 */}
        <div className="bg-white border border-gray-200 rounded-lg p-3">
          <h4 className="text-sm font-medium mb-3 text-gray-800 flex items-center">
            <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-2"></div>
            类别识别
          </h4>
          {categoryResult ? (
            <div className="bg-gray-50 p-3 rounded-md">
              <pre className="text-xs overflow-x-auto text-gray-700">
                {JSON.stringify(categoryResult, null, 2)}
              </pre>
            </div>
          ) : (
            <p className="text-xs text-gray-500 italic">暂无类别识别结果。</p>
          )}
        </div>

        {/* 关注点状态区域 */}
        <div className="bg-white border border-gray-200 rounded-lg p-3">
          <h4 className="text-sm font-medium mb-3 text-gray-800 flex items-center">
            <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mr-2"></div>
            关注点状态
          </h4>
          {focusPointsStatus && focusPointsStatus.length > 0 ? (
            <div className="space-y-2">
              {focusPointsStatus.map((item, index) => {
                const isCovered = item.value !== null;
                const displayStatus = getDisplayStatus(item.value);
                return (
                  <div key={index} className="bg-gray-50 p-2 rounded-md">
                    <div className="flex items-center justify-between mb-1">
                      <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                        item.priority === 'P0' ? 'bg-red-100 text-red-800 border border-red-200' :
                        item.priority === 'P1' ? 'bg-yellow-100 text-yellow-800 border border-yellow-200' :
                        item.priority === 'P2' ? 'bg-blue-100 text-blue-800 border border-blue-200' :
                        'bg-gray-100 text-gray-800 border border-gray-200'
                      }`}>
                        {item.priority}
                      </span>
                      <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                        isCovered ? 'bg-green-100 text-green-700' : 'bg-orange-100 text-orange-700'
                      }`}>
                        {displayStatus}
                      </span>
                    </div>
                    <p className="text-xs text-gray-700 font-medium mb-1">{item.point}</p>
                    {isCovered && (
                      <p className="text-xs text-gray-600 bg-white p-2 rounded border" title={item.value ?? undefined}>
                        <span className="font-medium">值:</span> {item.value}
                      </p>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <p className="text-xs text-gray-500 italic">暂无关注点状态信息。</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnalysisResultArea;
