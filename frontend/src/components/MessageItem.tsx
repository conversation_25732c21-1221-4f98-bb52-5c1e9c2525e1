import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import { Sparkles } from 'lucide-react';

interface MessageItemProps {
  message: {
    text: string;
    sender: 'user' | 'ai';
  };
  isLoading?: boolean;
  isStreaming?: boolean; // 新增：是否使用流式输出
}

const MessageItem: React.FC<MessageItemProps> = ({ message, isLoading, isStreaming = false }) => {
  const isUser = message.sender === 'user';
  const [displayText, setDisplayText] = useState('');
  const [charIndex, setCharIndex] = useState(0);
  
  // 当收到新消息时，模拟打字效果
  useEffect(() => {
    if (!isUser && message.text && isStreaming) {
      setDisplayText('');
      setCharIndex(0);

      const typingInterval = setInterval(() => {
        setCharIndex(prevIndex => {
          const nextIndex = prevIndex + 1;
          if (nextIndex <= message.text.length) {
            setDisplayText(message.text.substring(0, nextIndex));
            return nextIndex;
          } else {
            clearInterval(typingInterval);
            return prevIndex;
          }
        });
      }, 15); // 调整速度

      return () => clearInterval(typingInterval);
    } else if (!isUser && !isStreaming) {
      // 非流式模式，直接显示全部文本
      setDisplayText(message.text);
    }
  }, [isUser, message.text, isStreaming]); // 移除 charIndex 依赖

  const renderContent = () => {
    if (isLoading && !isUser) {
      // AI is typing - show improved loading animation
      return (
        <div className="flex items-center space-x-1 py-2">
          <div className="h-2 w-2 bg-gray-400 rounded-full animate-pulse"></div>
          <div className="h-2 w-2 bg-gray-500 rounded-full animate-pulse [animation-delay:-0.3s]"></div>
          <div className="h-2 w-2 bg-gray-600 rounded-full animate-pulse [animation-delay:-0.6s]"></div>
        </div>
      );
    } else if (!isUser) {
      // AI message - 使用流式显示的文本或完整文本
      const textToRender = isStreaming ? displayText : message.text;
      
      return (
        <div className="prose prose-sm max-w-none"> {/* 移除 prose-p 样式，让自定义组件样式生效 */}
          <ReactMarkdown
            remarkPlugins={[remarkGfm, remarkBreaks]}
            components={{
              // 段落组件 - 自然间距（仿照排版.md）
              p: ({ children }) => (
                <p className="mb-2 leading-relaxed text-gray-900">{children}</p>
              ),

              // 标题组件 - 支持文档中的标题显示
              h1: ({ children }) => (
                <h1 className="text-xl font-bold mb-3 mt-4 text-gray-900">{children}</h1>
              ),
              h2: ({ children }) => (
                <h2 className="text-lg font-semibold mb-2 mt-3 text-gray-900">{children}</h2>
              ),
              h3: ({ children }) => (
                <h3 className="text-base font-medium mb-2 mt-2 text-gray-900">{children}</h3>
              ),

              // 列表组件 - 简洁风格（仿照排版.md）
              ul: ({ children }) => (
                <ul className="ml-4 mb-3 space-y-1">{children}</ul>
              ),
              ol: ({ children }) => (
                <ol className="ml-4 mb-3 space-y-1">{children}</ol>
              ),
              li: ({ children }) => (
                <li className="leading-relaxed text-gray-900">{children}</li>
              ),

              // 强调文本 - 自然风格（仿照 **比如** 的效果）
              strong: ({ children }) => (
                <strong className="font-semibold text-gray-900">{children}</strong>
              ),

              // 代码块和行内代码 - 只对真正的代码块应用样式
              code: ({ children, className, ...props }: any) => {
                // 如果是行内代码（没有className或不是language-开头），应用代码样式
                if (!className || !className.startsWith('language-')) {
                  return <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">{children}</code>;
                }
                // 否则是代码块内的代码，不应用特殊样式
                return <code {...props}>{children}</code>;
              },
              pre: ({ children }) => {
                // 检查是否真的是代码块（包含language-类名的code元素）
                const hasCodeBlock = React.Children.toArray(children).some(child =>
                  React.isValidElement(child) &&
                  (child.props as any)?.className?.startsWith?.('language-')
                );

                if (hasCodeBlock) {
                  return <pre className="bg-gray-100 p-3 rounded mb-3 overflow-x-auto">{children}</pre>;
                }
                // 否则是普通的pre元素，不应用代码块样式
                return <div>{children}</div>;
              },
            }}
          >
            {textToRender}
          </ReactMarkdown>
          {isStreaming && charIndex < message.text.length && (
            <span className="inline-block w-1 h-4 bg-blue-500 ml-1 animate-pulse"></span>
          )}
        </div>
      );
    } else {
      // User message - Gemini 风格的用户消息
      return (
        <div className="user-message-text" style={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit', margin: 0 }}>
          {message.text}
        </div>
      );
    }
  };

  // 用户消息 - Gemini风格，右对齐，蓝色背景
  if (isUser) {
    return (
      <div className="flex w-full justify-end mb-8">
        <div className="flex flex-col max-w-[85%]">
          <div className="bg-blue-600 text-white px-5 py-4 rounded-2xl shadow-sm">
            {renderContent()}
          </div>
        </div>
      </div>
    );
  } else {
    // AI消息 - Gemini风格，无边框，带图标和状态信息
    return (
      <div className="flex w-full justify-start mb-8">
        <div className="flex flex-col max-w-[85%]">
          {/* 顶部：图标和状态信息 */}
          <div className="flex items-center space-x-3 mb-2">
            {/* AI图标 */}
            <div className="flex-shrink-0">
              <div className="w-7 h-7 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-white" />
              </div>
            </div>

            {/* 状态信息 */}
            <div className="text-sm text-gray-600">
              {isLoading ? "正在思考..." : "由己小助手"}
            </div>
          </div>

          {/* 正文内容 - 从图标下方开始 */}
          <div className="ml-10 text-gray-900">
            {renderContent()}
          </div>
        </div>
      </div>
    );
  }
};

export default MessageItem;
