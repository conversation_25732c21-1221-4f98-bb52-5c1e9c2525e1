import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Copy, RefreshCw, TestTube, Eye, EyeOff } from 'lucide-react';

interface SessionTestPanelProps {
  currentSessionId: string;
  onSessionIdChange: (newSessionId: string) => void;
  onRecoveryTest: () => void;
}

const SessionTestPanel: React.FC<SessionTestPanelProps> = ({
  currentSessionId,
  onSessionIdChange,
  onRecoveryTest
}) => {
  const [inputSessionId, setInputSessionId] = useState('');
  const [isVisible, setIsVisible] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  const handleCopySessionId = async () => {
    try {
      await navigator.clipboard.writeText(currentSessionId);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const handleSetSessionId = () => {
    if (inputSessionId.trim()) {
      onSessionIdChange(inputSessionId.trim());
      setInputSessionId('');
    }
  };

  const handleGenerateNewSessionId = () => {
    const newSessionId = `test_${crypto.randomUUID().substring(0, 8)}`;
    onSessionIdChange(newSessionId);
  };

  const handleRecoveryTest = () => {
    onRecoveryTest();
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsVisible(true)}
          className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
        >
          <TestTube className="h-4 w-4 mr-2" />
          测试面板
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className="w-80 bg-white shadow-lg border-blue-200">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-blue-700 flex items-center">
              <TestTube className="h-4 w-4 mr-2" />
              会话测试面板
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="h-6 w-6 p-0"
            >
              <EyeOff className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* 当前Session ID显示 */}
          <div className="space-y-2">
            <Label className="text-xs font-medium text-gray-600">当前会话ID</Label>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="flex-1 justify-start font-mono text-xs p-2">
                {currentSessionId.length > 20 
                  ? `${currentSessionId.substring(0, 20)}...` 
                  : currentSessionId
                }
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopySessionId}
                className="h-8 w-8 p-0"
                title="复制Session ID"
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
            {copySuccess && (
              <p className="text-xs text-green-600">✓ 已复制到剪贴板</p>
            )}
          </div>

          {/* 手动设置Session ID */}
          <div className="space-y-2">
            <Label className="text-xs font-medium text-gray-600">设置会话ID</Label>
            <div className="flex space-x-2">
              <Input
                placeholder="输入会话ID"
                value={inputSessionId}
                onChange={(e) => setInputSessionId(e.target.value)}
                className="text-xs"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleSetSessionId();
                  }
                }}
              />
              <Button
                variant="outline"
                size="sm"
                onClick={handleSetSessionId}
                disabled={!inputSessionId.trim()}
                className="whitespace-nowrap"
              >
                设置
              </Button>
            </div>
          </div>

          {/* 测试操作按钮 */}
          <div className="space-y-2">
            <Label className="text-xs font-medium text-gray-600">测试操作</Label>
            <div className="grid grid-cols-1 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleGenerateNewSessionId}
                className="text-xs"
              >
                <RefreshCw className="h-3 w-3 mr-2" />
                生成新会话ID
              </Button>
              
              <Button
                variant="default"
                size="sm"
                onClick={handleRecoveryTest}
                className="text-xs bg-blue-600 hover:bg-blue-700"
              >
                <TestTube className="h-3 w-3 mr-2" />
                测试会话恢复
              </Button>
            </div>
          </div>

          {/* 使用说明 */}
          <div className="bg-gray-50 p-3 rounded-md">
            <Label className="text-xs font-medium text-gray-600 mb-2 block">使用说明</Label>
            <ul className="text-xs text-gray-500 space-y-1">
              <li>• 复制当前会话ID用于测试</li>
              <li>• 手动设置会话ID来恢复历史会话</li>
              <li>• 点击"测试会话恢复"发送测试消息</li>
              <li>• 生成新会话ID开始新对话</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SessionTestPanel;
