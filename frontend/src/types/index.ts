export interface Message {
  text: string;
  sender: 'user' | 'ai';
}

export interface DomainResult {
  domain: string;
  confidence: number;
}

export interface CategoryResult {
  category: string;
  confidence: number;
  extracted_info: Record<string, string>;
}

// Updated definition matching backend response structure
export interface FocusPointStatus {
  priority: string;
  point: string; // Corresponds to backend's 'point' or 'description'
  description: string; // Corresponds to backend's 'description'
  value: string | null; // Corresponds to backend's 'value'
}

// Type for the expected structure of the /chat API response data
export interface ChatResponseData {
    response: string;
    domain_result: DomainResult | null;
    category_result: CategoryResult | null;
    focus_points_status: FocusPointStatus[] | null; // Use the updated FocusPointStatus
    follow_up: string | null;
    session_id: string;
}