"""
测试导入模块
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

try:
    from backend.agents.domain_classifier import AutoGenDomainClassifierAgent
    print("成功导入 AutoGenDomainClassifierAgent")
    
    from backend.agents.conversation_flow import AutoGenConversationFlowAgent
    print("成功导入 AutoGenConversationFlowAgent")
    
    # 测试classify_custom方法是否存在
    if hasattr(AutoGenDomainClassifierAgent, 'classify_custom'):
        print("AutoGenDomainClassifierAgent 有 classify_custom 方法")
    else:
        print("AutoGenDomainClassifierAgent 没有 classify_custom 方法")
        
except ImportError as e:
    print(f"导入失败: {str(e)}")
