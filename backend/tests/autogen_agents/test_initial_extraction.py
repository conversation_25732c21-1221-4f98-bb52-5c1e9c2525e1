"""测试初始信息提取功能"""
import pytest
import json
import os
import sys
from typing import Dict, Any, List
from unittest.mock import MagicMock

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

class MockInformationExtractor:
    """模拟信息提取器"""
    
    def __init__(self, llm_service=None):
        self.llm_service = llm_service or MagicMock()
    
    async def extract_value(self, user_response: str, focus_point: Dict[str, Any]):
        """模拟单值提取"""
        return {
            "value": "50万" if "预算" in focus_point["name"] else "3个月",
            "completeness": 0.8,
            "missing_aspects": [],
            "needs_clarification": []
        }
    
    async def batch_extract_initial_input(self, initial_input: str, focus_points: List[Dict[str, Any]]):
        """模拟批量提取"""
        results = {
            "extracted_points": [],
            "missing_points": [],
            "summary": ""
        }
        
        for focus_point in focus_points:
            extraction = await self.extract_value(initial_input, focus_point)
            if extraction["completeness"] >= 0.7:
                results["extracted_points"].append({
                    "focus_point": focus_point,
                    "extracted_value": extraction["value"],
                    "completeness": extraction["completeness"]
                })
            else:
                results["missing_points"].append(focus_point)
        
        results["summary"] = self._generate_extraction_summary(results["extracted_points"])
        return results
    
    def _generate_extraction_summary(self, extracted_points):
        """生成提取摘要"""
        if not extracted_points:
            return "未从您的描述中提取到任何信息。"
        
        summary = "我从您的描述中提取到以下信息:\n"
        for point in extracted_points:
            name = point["focus_point"]["name"]
            value = point["extracted_value"]
            summary += f"- {name}: {value}\n"
        return summary

@pytest.mark.asyncio
async def test_extract_single_value():
    """测试单个值提取"""
    extractor = MockInformationExtractor()
    
    result = await extractor.extract_value(
        user_response="预算是50万",
        focus_point={
            "id": "1",
            "name": "项目预算",
            "description": "项目的预算金额",
            "required": 1
        }
    )
    
    assert isinstance(result, dict)
    assert "value" in result
    assert result["value"] == "50万"
    assert "completeness" in result
    assert result["completeness"] == 0.8

@pytest.mark.asyncio
async def test_batch_extract():
    """测试批量提取功能"""
    extractor = MockInformationExtractor()
    
    # 准备测试数据
    focus_points = [
        {
            "id": "1",
            "name": "项目预算",
            "description": "项目的预算金额",
            "required": 1
        },
        {
            "id": "2",
            "name": "项目周期",
            "description": "项目的预计周期",
            "required": 1
        }
    ]
    
    initial_input = "我们要做一个电商项目,预算50万,预计3个月完成"
    
    # 执行批量提取
    result = await extractor.batch_extract_initial_input(
        initial_input=initial_input,
        focus_points=focus_points
    )
    
    # 验证结果格式
    assert isinstance(result, dict)
    assert "extracted_points" in result
    assert "missing_points" in result
    assert "summary" in result
    
    # 验证提取的点
    assert len(result["extracted_points"]) == 2
    for point in result["extracted_points"]:
        assert "focus_point" in point
        assert "extracted_value" in point
        assert "completeness" in point
        assert point["completeness"] >= 0.7
    
    # 验证摘要内容
    assert "项目预算: 50万" in result["summary"]
    assert "项目周期: 3个月" in result["summary"]
