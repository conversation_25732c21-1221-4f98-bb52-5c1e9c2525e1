"""
AutoGen基础Agent测试
"""
import pytest
from unittest.mock import AsyncMock, patch
from autogen_agents.base import AutoGenBaseAgent

class TestAutoGenBaseAgent:
    """AutoGenBaseAgent测试类"""

    @pytest.fixture
    def agent(self):
        """测试Agent实例"""
        return AutoGenBaseAgent(name="TestAgent")

    @pytest.mark.asyncio
    async def test_agent_initialization(self, agent):
        """测试Agent初始化"""
        assert agent.name == "TestAgent"
        assert isinstance(agent.state, dict)
        assert agent.logger is not None

    @pytest.mark.asyncio
    async def test_state_management(self, agent):
        """测试状态管理"""
        # 测试更新状态
        agent.update_state("key1", "value1")
        assert agent.get_state("key1") == "value1"
        
        # 测试获取不存在的状态
        assert agent.get_state("nonexistent") is None
        assert agent.get_state("nonexistent", "default") == "default"

    @pytest.mark.asyncio
    async def test_process_message_not_implemented(self, agent):
        """测试未实现的消息处理方法"""
        with pytest.raises(NotImplementedError):
            await agent.process_message("test message")

    @pytest.mark.asyncio
    async def test_ensure_response_format_decorator(self):
        """测试响应格式装饰器"""
        class TestAgent(AutoGenBaseAgent):
            @AutoGenBaseAgent.ensure_response_format
            async def process_message(self, message, context=None):
                return message

        agent = TestAgent(name="TestAgent")
        response = await agent.process_message("test")
        assert response.startswith("正在处理您的请求")

        # 测试错误处理
        with patch.object(TestAgent, 'process_message', 
                        side_effect=Exception("test error")):
            error_response = await agent.process_message("test")
            assert "抱歉" in error_response
