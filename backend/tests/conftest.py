"""
Pytest配置文件
"""
import os
import sys
import pytest
from unittest.mock import patch

# 将项目根目录添加到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 测试配置
pytest_plugins = [
    "pytest_asyncio",
]

# 在所有测试前mock日志系统
@pytest.fixture(autouse=True, scope="session")
def mock_logging():
    with patch('backend.utils.logging_config.get_logger'), \
         patch('backend.utils.logging_config.configure_logging'):
        yield
