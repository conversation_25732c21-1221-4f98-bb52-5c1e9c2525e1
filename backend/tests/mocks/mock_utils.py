"""
测试用的Mock工具和数据
"""
from unittest.mock import AsyncMock, MagicMock
from agents.conversation_flow import ConversationState

class MockLLM:
    """Mock LLM客户端"""
    def __init__(self, responses=None):
        self.responses = responses or {}
        self._chat = MagicMock()
        self._chat.completions = MagicMock()
        self._chat.completions.create = AsyncMock(return_value=MagicMock(
            choices=[MagicMock(
                message=MagicMock(
                    content="mock response"
                )
            )]
        ))
        self.call_count = 0
        
    async def generate(self, prompt):
        self.call_count += 1
        return self.responses.get(prompt, "mock response")
        
    # Add properties to match OpenAI client
    @property
    def completions(self):
        return self._chat.completions

    @property
    def chat(self):
        self.call_count += 1
        return self._chat

class MockSessionState:
    """Mock会话状态"""
    def __init__(self):
        self.conversation_history = []
        self.focus_points = {}
        self.current_phase = "initial"
        
    def to_prompt_context(self):
        return "mock context"

def create_mock_response(intent_type="greeting", focus_point_id=None, value=None, confidence=0.9):
    """创建Mock的意图识别响应"""
    return {
        "type": intent_type,
        "focus_point_id": focus_point_id,
        "value": value,
        "confidence": confidence
    }

class MockConversationFlow:
    """Mock对话流程管理器"""
    def __init__(self, responses=None):
        self.current_state = ConversationState.IDLE
        self.responses = responses or {}
        self.call_history = []
        
    async def process_message(self, message: str, context=None):
        self.call_history.append((message, context))
        self.current_state = ConversationState.PROCESSING_INTENT
        return self.responses.get(message, "Mock response")
        
    def transition_to(self, new_state: ConversationState):
        self.current_state = new_state
