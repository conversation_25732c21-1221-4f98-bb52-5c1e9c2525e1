"""
配置设置模块测试
"""
import os
import pytest
from unittest.mock import patch
from config.settings import get_required_env, LLM_CONFIGS

def test_get_required_env():
    """测试获取必要的环境变量函数"""
    # 测试存在的环境变量
    with patch.dict(os.environ, {"TEST_VAR": "test_value"}):
        assert get_required_env("TEST_VAR") == "test_value"
    
    # 测试不存在的环境变量，使用默认值
    assert get_required_env("NON_EXISTENT_VAR", "default_value") == "default_value"
    
    # 测试不存在的环境变量，无默认值
    assert get_required_env("NON_EXISTENT_VAR") == ""

def test_llm_configs_structure():
    """测试LLM配置结构"""
    # 验证所有模型配置都包含必要的字段
    for model_name, config in LLM_CONFIGS.items():
        assert "provider" in config
        assert "api_key" in config
        assert "model_name" in config
        assert isinstance(config.get("temperature", 0.7), float)
        
        # 验证token限制字段
        assert "max_tokens" in config
        assert isinstance(config["max_tokens"], int)

def test_model_configs():
    """测试模型配置有效性"""
    required_fields = [
        "provider",
        "api_key",
        "model_name",
        "temperature",
        "max_tokens",
        "top_p"
    ]
    
    for model_name, config in LLM_CONFIGS.items():
        # 验证所有必需字段都存在
        for field in required_fields:
            assert field in config, f"模型 {model_name} 缺少字段 {field}"
        
        # 验证温度值范围
        assert 0 <= config["temperature"] <= 2.0, f"模型 {model_name} 温度值超出范围"
        
        # 验证token限制
        assert config["max_tokens"] > 0, f"模型 {model_name} 的max_tokens必须大于0"
