import pytest
from unittest.mock import AsyncMock, MagicMock
from backend.agents.conversation_flow import AutoGenConversationFlowAgent
from backend.agents.intent_recognition import IntentRecognitionAgent
from backend.agents.decision_engine import DecisionEngine

@pytest.fixture
def mock_llm_client():
    return AsyncMock()

@pytest.fixture
def conversation_agent(mock_llm_client):
    return AutoGenConversationFlowAgent(
        llm_client=mock_llm_client,
        intent_recognition_agent=IntentRecognitionAgent(mock_llm_client),
        decision_engine=DecisionEngine()
    )

@pytest.mark.asyncio
async def test_intent_recognition_integration(conversation_agent, mock_llm_client):
    # 模拟意图识别结果
    mock_intent_result = {
        "intent": "query",
        "emotion": "neutral",
        "confidence": 0.9
    }
    conversation_agent.intent_recognition_agent.recognize_intent = AsyncMock(return_value=mock_intent_result)
    
    # 模拟决策引擎结果
    mock_strategy = {
        "action": "detail_amplify",
        "response_template": "请详细说明您的需求"
    }
    conversation_agent.decision_engine.get_strategy = MagicMock(return_value=mock_strategy)
    
    # 测试消息处理
    test_message = {
        "text": "我想了解项目进度",
        "session_id": "test123"
    }
    response = await conversation_agent.process_message(test_message)
    
    # 验证意图识别被调用
    conversation_agent.intent_recognition_agent.recognize_intent.assert_called_once_with("我想了解项目进度")
    
    # 验证决策引擎被调用
    conversation_agent.decision_engine.get_strategy.assert_called_once_with(
        intent="query",
        emotion="neutral",
        context={
            "session_id": "test123",
            "current_state": "IDLE",
            "domain": None,
            "category": None
        }
    )
    
    # 验证响应包含决策结果
    assert "请详细说明您的需求" in response["text_response"]

@pytest.mark.asyncio
async def test_emotion_based_strategy(conversation_agent, mock_llm_client):
    # 模拟负面情绪意图识别
    mock_intent_result = {
        "intent": "complain", 
        "emotion": "angry",
        "confidence": 0.85
    }
    conversation_agent.intent_recognition_agent.recognize_intent = AsyncMock(return_value=mock_intent_result)
    
    # 模拟决策引擎返回安抚策略
    mock_strategy = {
        "action": "apologize_and_correct",
        "response_template": "非常抱歉给您带来不便"
    }
    conversation_agent.decision_engine.get_strategy = MagicMock(return_value=mock_strategy)
    
    # 测试消息处理
    test_message = {
        "text": "你们的服务太差了",
        "session_id": "test456"
    }
    response = await conversation_agent.process_message(test_message)
    
    # 验证响应包含安抚内容
    assert "非常抱歉" in response["text_response"]

@pytest.mark.asyncio
async def test_positive_emotion_response(conversation_agent, mock_llm_client):
    # 模拟正面情绪意图识别
    mock_intent_result = {
        "intent": "praise",
        "emotion": "happy",
        "confidence": 0.9
    }
    conversation_agent.intent_recognition_agent.recognize_intent = AsyncMock(return_value=mock_intent_result)
    
    # 模拟决策引擎返回感谢策略
    mock_strategy = {
        "action": "appreciate_and_continue",
        "response_template": "感谢您的肯定"
    }
    conversation_agent.decision_engine.get_strategy = MagicMock(return_value=mock_strategy)
    
    test_message = {
        "text": "你们的服务很棒",
        "session_id": "test789"
    }
    response = await conversation_agent.process_message(test_message)
    
    assert "感谢您的肯定" in response["text_response"]

@pytest.mark.asyncio
async def test_ambiguous_intent_handling(conversation_agent, mock_llm_client):
    # 模拟模糊意图识别
    mock_intent_result = {
        "intent": "unclear",
        "emotion": "neutral",
        "confidence": 0.5
    }
    conversation_agent.intent_recognition_agent.recognize_intent = AsyncMock(return_value=mock_intent_result)
    
    # 模拟决策引擎返回澄清策略
    mock_strategy = {
        "action": "clarify_intent",
        "response_template": "请您再详细说明一下需求"
    }
    conversation_agent.decision_engine.get_strategy = MagicMock(return_value=mock_strategy)
    
    test_message = {
        "text": "我不太确定",
        "session_id": "test101"
    }
    response = await conversation_agent.process_message(test_message)
    
    assert "请您再详细说明" in response["text_response"]
    assert response["domain_result"] is None
    assert response["category_result"] is None

@pytest.mark.asyncio
async def test_low_confidence_handling(conversation_agent, mock_llm_client):
    # 模拟低置信度意图
    mock_intent_result = {
        "intent": "query",
        "emotion": "neutral",
        "confidence": 0.3  # 低于阈值
    }
    conversation_agent.intent_recognition_agent.recognize_intent = AsyncMock(return_value=mock_intent_result)
    
    # 模拟决策引擎返回验证策略
    mock_strategy = {
        "action": "verify_intent",
        "response_template": "您是想查询相关信息吗？"
    }
    conversation_agent.decision_engine.get_strategy = MagicMock(return_value=mock_strategy)
    
    test_message = {
        "text": "可能想了解",
        "session_id": "test202"
    }
    response = await conversation_agent.process_message(test_message)
    
    assert "您是想查询" in response["text_response"]
    assert response["requires_confirmation"] is True

@pytest.mark.asyncio
async def test_mixed_emotion_handling(conversation_agent, mock_llm_client):
    # 模拟混合情绪意图
    mock_intent_result = {
        "intent": "feedback",
        "emotion": "mixed",
        "confidence": 0.8
    }
    conversation_agent.intent_recognition_agent.recognize_intent = AsyncMock(return_value=mock_intent_result)
    
    # 模拟决策引擎返回中性策略
    mock_strategy = {
        "action": "neutral_response",
        "response_template": "我们已收到您的反馈"
    }
    conversation_agent.decision_engine.get_strategy = MagicMock(return_value=mock_strategy)
    
    test_message = {
        "text": "功能不错但速度慢",
        "session_id": "test303"
    }
    response = await conversation_agent.process_message(test_message)
    
    assert "收到您的反馈" in response["text_response"]
    assert response["emotion"] == "mixed"
