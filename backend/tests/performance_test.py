"""
性能测试脚本

此脚本用于测试系统在高负载下的性能，包括：
1. API响应时间测试
2. 并发请求测试
3. 长时间运行测试
4. 内存泄漏测试
"""

import os
import sys
import json
import time
import asyncio
import logging
import argparse
import aiohttp
import random
import psutil
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

# 设置日志记录器
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 测试配置
DEFAULT_API_URL = "http://localhost:8000"
DEFAULT_TEST_DURATION = 60  # 秒
DEFAULT_CONCURRENCY = 5
DEFAULT_REQUEST_INTERVAL = 1.0  # 秒
DEFAULT_OUTPUT_DIR = "logs/performance_test"

# 测试消息模板
TEST_MESSAGES = [
    "你好，我想做一个海报",
    "我需要一个电商网站",
    "我想做一个APP",
    "我需要一个企业网站",
    "我想做一个小程序",
    "我需要一个管理系统",
    "我想做一个社交平台",
    "我需要一个教育网站",
    "我想做一个游戏",
    "我需要一个博客"
]

class PerformanceTester:
    """性能测试器"""
    
    def __init__(
        self,
        api_url: str = DEFAULT_API_URL,
        test_duration: int = DEFAULT_TEST_DURATION,
        concurrency: int = DEFAULT_CONCURRENCY,
        request_interval: float = DEFAULT_REQUEST_INTERVAL,
        output_dir: str = DEFAULT_OUTPUT_DIR
    ):
        """
        初始化性能测试器
        
        Args:
            api_url: API URL
            test_duration: 测试持续时间（秒）
            concurrency: 并发数
            request_interval: 请求间隔（秒）
            output_dir: 输出目录
        """
        self.api_url = api_url
        self.test_duration = test_duration
        self.concurrency = concurrency
        self.request_interval = request_interval
        self.output_dir = output_dir
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 测试结果
        self.results = {
            "api_response_times": [],
            "request_timestamps": [],
            "success_count": 0,
            "error_count": 0,
            "error_details": [],
            "memory_usage": [],
            "cpu_usage": []
        }
        
        # 会话ID
        self.session_id = None
        
        logger.info(f"性能测试器初始化完成，API URL：{api_url}，"
                   f"测试持续时间：{test_duration}秒，"
                   f"并发数：{concurrency}，"
                   f"请求间隔：{request_interval}秒")
    
    async def create_session(self) -> str:
        """
        创建会话
        
        Returns:
            str: 会话ID
        """
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{self.api_url}/session/create") as response:
                if response.status == 200:
                    data = await response.json()
                    return data["session_id"]
                else:
                    error_text = await response.text()
                    raise RuntimeError(f"创建会话失败：{response.status} - {error_text}")
    
    async def send_message(self, session_id: str, message: str) -> Dict[str, Any]:
        """
        发送消息
        
        Args:
            session_id: 会话ID
            message: 消息内容
            
        Returns:
            Dict[str, Any]: 响应数据
        """
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_url}/chat",
                    json={"session_id": session_id, "text": message}
                ) as response:
                    elapsed_time = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        self.results["api_response_times"].append(elapsed_time)
                        self.results["request_timestamps"].append(time.time())
                        self.results["success_count"] += 1
                        return data
                    else:
                        error_text = await response.text()
                        self.results["error_count"] += 1
                        self.results["error_details"].append({
                            "timestamp": time.time(),
                            "status": response.status,
                            "error": error_text,
                            "message": message
                        })
                        logger.error(f"发送消息失败：{response.status} - {error_text}")
                        return {"error": error_text}
        except Exception as e:
            elapsed_time = time.time() - start_time
            self.results["error_count"] += 1
            self.results["error_details"].append({
                "timestamp": time.time(),
                "error": str(e),
                "message": message
            })
            logger.error(f"发送消息异常：{str(e)}")
            return {"error": str(e)}
    
    async def monitor_resources(self, interval: float = 1.0):
        """
        监控系统资源
        
        Args:
            interval: 监控间隔（秒）
        """
        end_time = time.time() + self.test_duration
        
        while time.time() < end_time:
            # 获取内存使用情况
            memory_info = psutil.virtual_memory()
            self.results["memory_usage"].append({
                "timestamp": time.time(),
                "percent": memory_info.percent,
                "used": memory_info.used,
                "total": memory_info.total
            })
            
            # 获取CPU使用情况
            cpu_percent = psutil.cpu_percent(interval=0.1)
            self.results["cpu_usage"].append({
                "timestamp": time.time(),
                "percent": cpu_percent
            })
            
            await asyncio.sleep(interval)
    
    async def run_client(self, client_id: int):
        """
        运行客户端
        
        Args:
            client_id: 客户端ID
        """
        logger.info(f"客户端 {client_id} 启动")
        
        # 创建会话
        if not self.session_id:
            try:
                self.session_id = await self.create_session()
                logger.info(f"客户端 {client_id} 创建会话：{self.session_id}")
            except Exception as e:
                logger.error(f"客户端 {client_id} 创建会话失败：{str(e)}")
                return
        
        # 发送消息
        end_time = time.time() + self.test_duration
        message_index = 0
        
        while time.time() < end_time:
            # 选择消息
            message = TEST_MESSAGES[message_index % len(TEST_MESSAGES)]
            message_index += 1
            
            # 发送消息
            logger.info(f"客户端 {client_id} 发送消息：{message}")
            response = await self.send_message(self.session_id, message)
            
            # 检查响应
            if "error" in response:
                logger.error(f"客户端 {client_id} 收到错误响应：{response['error']}")
            else:
                logger.info(f"客户端 {client_id} 收到响应：{response.get('response', '')[:50]}...")
            
            # 等待一段时间
            await asyncio.sleep(self.request_interval)
        
        logger.info(f"客户端 {client_id} 结束")
    
    async def run_test(self):
        """运行测试"""
        logger.info(f"开始性能测试，持续时间：{self.test_duration}秒，并发数：{self.concurrency}")
        
        # 创建会话
        try:
            self.session_id = await self.create_session()
            logger.info(f"创建会话：{self.session_id}")
        except Exception as e:
            logger.error(f"创建会话失败：{str(e)}")
            return
        
        # 启动资源监控
        monitor_task = asyncio.create_task(self.monitor_resources())
        
        # 启动客户端
        client_tasks = []
        for i in range(self.concurrency):
            client_tasks.append(asyncio.create_task(self.run_client(i)))
        
        # 等待所有任务完成
        await asyncio.gather(*client_tasks)
        
        # 停止资源监控
        monitor_task.cancel()
        
        # 分析结果
        self.analyze_results()
    
    def analyze_results(self):
        """分析测试结果"""
        logger.info("分析测试结果")
        
        # 计算API响应时间统计
        if self.results["api_response_times"]:
            response_times = np.array(self.results["api_response_times"])
            avg_response_time = np.mean(response_times)
            min_response_time = np.min(response_times)
            max_response_time = np.max(response_times)
            p50_response_time = np.percentile(response_times, 50)
            p90_response_time = np.percentile(response_times, 90)
            p95_response_time = np.percentile(response_times, 95)
            p99_response_time = np.percentile(response_times, 99)
            
            logger.info(f"API响应时间统计：")
            logger.info(f"  平均：{avg_response_time:.3f}秒")
            logger.info(f"  最小：{min_response_time:.3f}秒")
            logger.info(f"  最大：{max_response_time:.3f}秒")
            logger.info(f"  P50：{p50_response_time:.3f}秒")
            logger.info(f"  P90：{p90_response_time:.3f}秒")
            logger.info(f"  P95：{p95_response_time:.3f}秒")
            logger.info(f"  P99：{p99_response_time:.3f}秒")
            
            # 保存统计结果
            stats = {
                "api_response_time": {
                    "avg": float(avg_response_time),
                    "min": float(min_response_time),
                    "max": float(max_response_time),
                    "p50": float(p50_response_time),
                    "p90": float(p90_response_time),
                    "p95": float(p95_response_time),
                    "p99": float(p99_response_time)
                },
                "success_count": self.results["success_count"],
                "error_count": self.results["error_count"],
                "error_rate": self.results["error_count"] / (self.results["success_count"] + self.results["error_count"]) if (self.results["success_count"] + self.results["error_count"]) > 0 else 0,
                "test_duration": self.test_duration,
                "concurrency": self.concurrency,
                "request_interval": self.request_interval,
                "timestamp": datetime.now().isoformat()
            }
            
            # 保存统计结果
            stats_file = os.path.join(self.output_dir, f"stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(stats_file, "w", encoding="utf-8") as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            
            logger.info(f"统计结果已保存到：{stats_file}")
            
            # 绘制响应时间图表
            self.plot_response_times()
            
            # 绘制资源使用图表
            self.plot_resource_usage()
        else:
            logger.warning("没有收集到API响应时间数据")
    
    def plot_response_times(self):
        """绘制响应时间图表"""
        if not self.results["api_response_times"] or not self.results["request_timestamps"]:
            logger.warning("没有足够的数据绘制响应时间图表")
            return
        
        # 创建图表
        plt.figure(figsize=(10, 6))
        
        # 计算相对时间（从测试开始到现在的秒数）
        start_time = min(self.results["request_timestamps"])
        relative_times = [t - start_time for t in self.results["request_timestamps"]]
        
        # 绘制响应时间
        plt.plot(relative_times, self.results["api_response_times"], "o-", label="API响应时间")
        
        # 添加标题和标签
        plt.title("API响应时间")
        plt.xlabel("测试时间（秒）")
        plt.ylabel("响应时间（秒）")
        plt.grid(True)
        plt.legend()
        
        # 保存图表
        chart_file = os.path.join(self.output_dir, f"response_times_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
        plt.savefig(chart_file)
        
        logger.info(f"响应时间图表已保存到：{chart_file}")
    
    def plot_resource_usage(self):
        """绘制资源使用图表"""
        if not self.results["memory_usage"] or not self.results["cpu_usage"]:
            logger.warning("没有足够的数据绘制资源使用图表")
            return
        
        # 创建图表
        plt.figure(figsize=(10, 8))
        
        # 计算相对时间（从测试开始到现在的秒数）
        memory_start_time = min(item["timestamp"] for item in self.results["memory_usage"])
        memory_relative_times = [item["timestamp"] - memory_start_time for item in self.results["memory_usage"]]
        memory_percents = [item["percent"] for item in self.results["memory_usage"]]
        
        cpu_start_time = min(item["timestamp"] for item in self.results["cpu_usage"])
        cpu_relative_times = [item["timestamp"] - cpu_start_time for item in self.results["cpu_usage"]]
        cpu_percents = [item["percent"] for item in self.results["cpu_usage"]]
        
        # 绘制内存使用率
        plt.subplot(2, 1, 1)
        plt.plot(memory_relative_times, memory_percents, "b-", label="内存使用率")
        plt.title("内存使用率")
        plt.xlabel("测试时间（秒）")
        plt.ylabel("使用率（%）")
        plt.grid(True)
        plt.legend()
        
        # 绘制CPU使用率
        plt.subplot(2, 1, 2)
        plt.plot(cpu_relative_times, cpu_percents, "r-", label="CPU使用率")
        plt.title("CPU使用率")
        plt.xlabel("测试时间（秒）")
        plt.ylabel("使用率（%）")
        plt.grid(True)
        plt.legend()
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = os.path.join(self.output_dir, f"resource_usage_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
        plt.savefig(chart_file)
        
        logger.info(f"资源使用图表已保存到：{chart_file}")

async def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="性能测试脚本")
    parser.add_argument("--api-url", type=str, default=DEFAULT_API_URL, help="API URL")
    parser.add_argument("--duration", type=int, default=DEFAULT_TEST_DURATION, help="测试持续时间（秒）")
    parser.add_argument("--concurrency", type=int, default=DEFAULT_CONCURRENCY, help="并发数")
    parser.add_argument("--interval", type=float, default=DEFAULT_REQUEST_INTERVAL, help="请求间隔（秒）")
    parser.add_argument("--output-dir", type=str, default=DEFAULT_OUTPUT_DIR, help="输出目录")
    args = parser.parse_args()
    
    # 创建性能测试器
    tester = PerformanceTester(
        api_url=args.api_url,
        test_duration=args.duration,
        concurrency=args.concurrency,
        request_interval=args.interval,
        output_dir=args.output_dir
    )
    
    # 运行测试
    await tester.run_test()

if __name__ == "__main__":
    asyncio.run(main())
