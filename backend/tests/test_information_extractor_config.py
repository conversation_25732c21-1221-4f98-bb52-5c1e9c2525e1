"""信息提取代理配置测试

测试配置外部化功能是否正常工作。
"""

import pytest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from config.information_extractor_config import InformationExtractorConfig, DEFAULT_CONFIG


class TestInformationExtractorConfig:
    """信息提取代理配置测试类"""
    
    def test_default_config_values(self):
        """测试默认配置值"""
        config = DEFAULT_CONFIG
        
        # 测试LLM参数
        assert config.extraction_temperature == 0.3
        assert config.question_generation_temperature == 0.7
        assert config.clarification_temperature == 0.5
        
        # 测试阈值
        assert config.completeness_threshold == 0.6
        assert config.confidence_threshold == 0.7
        assert config.quality_threshold == 0.5
        
        # 测试其他参数
        assert config.max_retry_attempts == 3
        assert config.timeout_seconds == 30
        assert config.max_focus_points_per_batch == 5
        assert config.context_window_size == 2000
        
    def test_custom_config_creation(self):
        """测试自定义配置创建"""
        custom_config = InformationExtractorConfig(
            extraction_temperature=0.2,
            completeness_threshold=0.8,
            max_retry_attempts=5
        )
        
        assert custom_config.extraction_temperature == 0.2
        assert custom_config.completeness_threshold == 0.8
        assert custom_config.max_retry_attempts == 5
        # 其他值应该使用默认值
        assert custom_config.question_generation_temperature == 0.7
        
    def test_config_to_dict(self):
        """测试配置转换为字典"""
        config = InformationExtractorConfig()
        config_dict = config.to_dict()
        
        assert isinstance(config_dict, dict)
        assert 'extraction_temperature' in config_dict
        assert 'completeness_threshold' in config_dict
        assert config_dict['extraction_temperature'] == 0.3
        
    def test_config_from_dict(self):
        """测试从字典创建配置"""
        config_dict = {
            'extraction_temperature': 0.4,
            'completeness_threshold': 0.7,
            'max_retry_attempts': 4
        }
        
        config = InformationExtractorConfig.from_dict(config_dict)
        
        assert config.extraction_temperature == 0.4
        assert config.completeness_threshold == 0.7
        assert config.max_retry_attempts == 4
        
    def test_get_llm_params(self):
        """测试获取LLM参数"""
        config = InformationExtractorConfig()
        
        # 测试不同操作类型的参数
        extraction_params = config.get_llm_params('extraction')
        assert extraction_params['temperature'] == 0.3
        assert extraction_params['timeout'] == 30
        
        question_params = config.get_llm_params('question_generation')
        assert question_params['temperature'] == 0.7
        
        clarification_params = config.get_llm_params('clarification')
        assert clarification_params['temperature'] == 0.5
        
        # 测试未知操作类型（应该返回默认提取参数）
        unknown_params = config.get_llm_params('unknown_operation')
        assert unknown_params['temperature'] == 0.3


if __name__ == '__main__':
    # 运行简单测试
    test_instance = TestInformationExtractorConfig()
    
    print("运行配置测试...")
    
    try:
        test_instance.test_default_config_values()
        print("✓ 默认配置值测试通过")
        
        test_instance.test_custom_config_creation()
        print("✓ 自定义配置创建测试通过")
        
        test_instance.test_config_to_dict()
        print("✓ 配置转字典测试通过")
        
        test_instance.test_config_from_dict()
        print("✓ 从字典创建配置测试通过")
        
        test_instance.test_get_llm_params()
        print("✓ LLM参数获取测试通过")
        
        print("\n所有测试通过！配置外部化功能正常工作。")
        
    except Exception as e:
        print(f"测试失败: {e}")
        raise