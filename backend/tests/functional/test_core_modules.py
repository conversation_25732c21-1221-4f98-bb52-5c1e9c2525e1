"""
核心功能模块测试

此脚本用于测试系统的核心功能模块，包括：
1. LLMServiceAgent - LLM服务代理
2. KnowledgeBaseAgent - 知识库代理
3. IntentRecognitionAgent - 意图识别代理
4. InformationExtractorAgent - 信息提取代理
"""

import os
import json
import asyncio
import logging
import pytest
import time
from typing import Dict, Any, Optional, List
from unittest.mock import AsyncMock, MagicMock, patch

# 导入被测试的模块
from backend.agents.llm_service import LLMServiceAgent
from backend.agents.knowledge_base import KnowledgeBaseAgent
from backend.agents.intent_recognition import IntentRecognitionAgent
from backend.agents.information_extractor import InformationExtractorAgent
from backend.agents.conversation_flow import ConversationFlowAgent, ConversationState

# Note: 以下模块目前不可用，需要更新
# from backend.core.llm_factory import LLMFactory
# from backend.agents.document_generator import DocumentGeneratorAgent
# from backend.agents.review_and_refine import ReviewAndRefineAgent

# 设置日志级别
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 模拟LLM响应
class MockLLMResponse:
    def __init__(self, content):
        self.content = content
        self.choices = [MagicMock()]
        self.choices[0].message.content = content

# 模拟LLM客户端
class MockLLMClient:
    def __init__(self):
        self.chat = MagicMock()
        self.chat.completions = MagicMock()
        self.chat.completions.create = AsyncMock(return_value=MockLLMResponse("模拟的LLM响应"))

# 模拟LLMServiceAgent
class MockLLMServiceAgent(LLMServiceAgent):
    def __init__(self):
        # 不调用父类的__init__方法，避免实际连接LLM服务
        self.name = "MockLLMService"
        self.logger = logging.getLogger(f"agent.{self.name}")
        self.state = {}

        # 模拟响应
        self.responses = {
            "intent_recognition": {
                "content": json.dumps({
                    "type": "modify",
                    "confidence": 0.9,
                    "focus_point_id": "1",
                    "value": "这是修改后的值",
                    "explanation": "用户想要修改关注点1的值"
                })
            },
            "information_extraction": {
                "content": "这是提取的信息"
            },
            "document_generation": {
                "content": "# 生成的文档\n\n这是一个自动生成的文档。"
            },
            "feedback_analysis": {
                "content": json.dumps({
                    "is_satisfied": False,
                    "modifications": [
                        {
                            "focus_point": "文档标题",
                            "old_value": "生成的文档",
                            "new_value": "需求文档",
                            "reason": "标题不够具体"
                        }
                    ]
                })
            }
        }

    async def call_llm(self, messages, scenario="conversation", **kwargs):
        """模拟调用LLM"""
        logger.info(f"模拟调用LLM，场景：{scenario}")

        # 根据场景返回不同的响应
        if scenario in self.responses:
            return self.responses[scenario]

        # 默认响应
        return {
            "content": "这是默认的模拟响应",
            "role": "assistant",
            "model": "mock-model",
            "provider": "mock-provider",
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 10,
                "total_tokens": 20
            }
        }

    async def generate(self, prompt, **kwargs):
        """模拟生成文本"""
        logger.info(f"模拟生成文本，提示词：{prompt[:50]}...")
        return "这是模拟生成的文本"

# 模拟数据库连接
class MockDBConnection:
    def __init__(self):
        self.cursor = MagicMock()
        self.cursor.execute = MagicMock()
        self.cursor.fetchall = MagicMock(return_value=[
            (1, "软件开发", "软件开发领域的需求采集"),
            (2, "设计", "设计领域的需求采集")
        ])
        self.cursor.fetchone = MagicMock(return_value=(1, "软件开发", "软件开发领域的需求采集"))

    def cursor(self):
        return self.cursor

    def commit(self):
        pass

    def close(self):
        pass

# 测试LLMServiceAgent
@pytest.mark.asyncio
async def test_llm_service_agent():
    """测试LLMServiceAgent的基本功能"""
    logger.info("开始测试LLMServiceAgent")

    # 创建MockLLMServiceAgent
    llm_agent = MockLLMServiceAgent()

    # 测试call_llm方法
    response = await llm_agent.call_llm(
        messages=[{"role": "user", "content": "测试消息"}],
        scenario="intent_recognition"
    )

    assert "content" in response
    assert isinstance(response["content"], str)

    # 测试generate方法
    text = await llm_agent.generate("测试提示词")
    assert isinstance(text, str)
    assert len(text) > 0

    logger.info("LLMServiceAgent测试完成")

# 测试KnowledgeBaseAgent
@pytest.mark.asyncio
async def test_knowledge_base_agent():
    """测试KnowledgeBaseAgent的基本功能"""
    logger.info("开始测试KnowledgeBaseAgent")

    # 模拟数据库路径
    db_path = "mock_db.db"

    # 创建模拟的LLMServiceAgent
    llm_agent = MockLLMServiceAgent()

    # 创建KnowledgeBaseAgent，使用测试模式
    kb_agent = KnowledgeBaseAgent(db_path=db_path, llm_service_agent=llm_agent, test_mode=True)

    # 测试get_domains方法
    domains = await kb_agent.get_domains()
    assert isinstance(domains, list)
    assert len(domains) > 0

    # 测试get_domain_by_name方法
    domain = await kb_agent.get_domain_by_name("软件开发")
    assert isinstance(domain, dict)
    assert "id" in domain
    assert "name" in domain

    # 测试get_categories方法
    categories = await kb_agent.get_categories(domain_id=1)
    assert isinstance(categories, list)

    # 测试get_focus_points方法
    focus_points = await kb_agent.get_focus_points(domain_id=1, category_id=1)
    assert isinstance(focus_points, list)

    logger.info("KnowledgeBaseAgent测试完成")

# 测试IntentRecognitionAgent
@pytest.mark.asyncio
async def test_intent_recognition_agent():
    """测试IntentRecognitionAgent的基本功能"""
    logger.info("开始测试IntentRecognitionAgent")

    # 创建模拟的LLMServiceAgent
    llm_agent = MockLLMServiceAgent()

    # 创建IntentRecognitionAgent
    intent_agent = IntentRecognitionAgent(llm_client=llm_agent)

    # 测试analyze_intent_rule_based方法
    intent_result = await intent_agent.analyze_intent_rule_based("你好")
    assert isinstance(intent_result, dict)
    assert "type" in intent_result
    assert "confidence" in intent_result

    # 测试analyze_intent_llm_based方法
    intent_result = await intent_agent.analyze_intent_llm_based("我想修改项目背景")
    assert isinstance(intent_result, dict)
    assert "type" in intent_result
    assert "confidence" in intent_result

    # 测试analyze_intent方法
    intent_result = await intent_agent.analyze_intent("我想修改项目背景", use_llm=True)
    assert isinstance(intent_result, dict)
    assert "type" in intent_result
    assert "confidence" in intent_result

    logger.info("IntentRecognitionAgent测试完成")

# 测试InformationExtractorAgent
@pytest.mark.asyncio
async def test_information_extractor_agent():
    """测试InformationExtractorAgent的基本功能"""
    logger.info("开始测试InformationExtractorAgent")

    # 创建模拟的LLMServiceAgent
    llm_agent = MockLLMServiceAgent()

    # 使用patch模拟sqlite3.connect
    with patch("sqlite3.connect", return_value=MockDBConnection()):
        # 创建KnowledgeBaseAgent
        kb_agent = KnowledgeBaseAgent(db_path="mock_db.db", llm_service_agent=llm_agent)

        # 创建InformationExtractorAgent
        info_agent = InformationExtractorAgent(knowledge_base_agent=kb_agent, llm_service_agent=llm_agent)

        # 模拟关注点
        info_agent.current_focus_points = [
            {"id": "1", "name": "项目背景", "description": "项目的背景和目标", "required": True},
            {"id": "2", "name": "功能需求", "description": "项目的功能需求", "required": True}
        ]

        # 测试generate_collection_question方法
        question = await info_agent.generate_collection_question(info_agent.current_focus_points[0])
        assert isinstance(question, str)
        assert len(question) > 0

        # 测试process_user_response方法
        intent = {"type": "modify", "value": "这是项目背景"}
        result = await info_agent.process_user_response("项目背景是开发一个需求采集系统", "1", intent)
        assert isinstance(result, dict)
        assert "focus_point_id" in result
        assert "extracted_value" in result

        # 测试should_continue_collection方法
        should_continue = await info_agent.should_continue_collection()
        assert isinstance(should_continue, bool)

    logger.info("InformationExtractorAgent测试完成")

# Note: 这些测试功能暂时被注释掉，因为相关模块已经被移除或重命名
"""
# 测试DocumentGeneratorAgent
@pytest.mark.skip(reason="DocumentGeneratorAgent模块当前不可用")
@pytest.mark.asyncio
async def test_document_generator_agent():
    pass

# 测试ReviewAndRefineAgent
@pytest.mark.skip(reason="ReviewAndRefineAgent模块当前不可用")
@pytest.mark.asyncio
async def test_review_and_refine_agent():
    pass
"""

# 运行所有测试
async def run_all_tests():
    """运行所有功能测试"""
    try:
        await test_llm_service_agent()
        await test_knowledge_base_agent()
        await test_intent_recognition_agent()
        await test_information_extractor_agent()

        logger.info("所有功能测试完成")
    except Exception as e:
        logger.error(f"测试过程中出错：{str(e)}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(run_all_tests())
