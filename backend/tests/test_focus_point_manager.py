"""
测试关注点状态管理功能
"""
import sys
import os
import asyncio
import logging
import uuid
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from backend.data.db.database_manager import DatabaseManager
from backend.data.db.focus_point_manager import FocusPointManager
from backend.data.db.message_manager import MessageManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 测试数据库路径
TEST_DB_PATH = "test_focus_point.db"

async def setup_test_db():
    """设置测试数据库"""
    # 删除已存在的测试数据库
    if os.path.exists(TEST_DB_PATH):
        os.remove(TEST_DB_PATH)
    
    # 创建数据库管理器
    db_manager = DatabaseManager(TEST_DB_PATH)
    
    # 创建测试表
    db_manager.execute_update("""
    CREATE TABLE IF NOT EXISTS conversations (
        conversation_id TEXT PRIMARY KEY,
        user_id TEXT,
        title TEXT,
        domain_id TEXT,
        category_id TEXT,
        state TEXT,
        created_at TEXT,
        updated_at TEXT,
        last_activity_at TEXT,
        metadata TEXT
    )
    """)
    
    db_manager.execute_update("""
    CREATE TABLE IF NOT EXISTS concern_point_coverage (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        conversation_id TEXT,
        focus_id TEXT,
        status TEXT,
        attempts INTEGER,
        is_covered INTEGER,
        extracted_info TEXT,
        updated_at TEXT,
        FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id),
        UNIQUE(conversation_id, focus_id)
    )
    """)
    
    db_manager.execute_update("""
    CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        conversation_id TEXT,
        sender_type TEXT,
        content TEXT,
        focus_id TEXT,
        message_type TEXT,
        created_at TEXT,
        FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id)
    )
    """)
    
    # 创建测试会话
    conversation_id = str(uuid.uuid4())
    db_manager.execute_update(
        """
        INSERT INTO conversations
        (conversation_id, user_id, title, state, created_at, updated_at, last_activity_at)
        VALUES (?, ?, ?, ?, datetime('now'), datetime('now'), datetime('now'))
        """,
        (conversation_id, "test_user", "测试会话", "IDLE")
    )
    
    return db_manager, conversation_id

async def test_focus_point_manager():
    """测试关注点状态管理器"""
    logger.info("开始测试关注点状态管理器")
    
    # 设置测试数据库
    db_manager, conversation_id = await setup_test_db()
    focus_point_manager = FocusPointManager(db_manager)
    
    # 测试数据
    focus_points = [
        {
            "id": "fp1",
            "name": "功能需求",
            "description": "请描述您需要的主要功能",
            "priority": "P0"
        },
        {
            "id": "fp2",
            "name": "用户角色",
            "description": "请描述系统的目标用户",
            "priority": "P1"
        },
        {
            "id": "fp3",
            "name": "性能要求",
            "description": "请描述系统的性能要求",
            "priority": "P2"
        }
    ]
    
    # 测试初始化关注点状态
    logger.info("测试初始化关注点状态")
    result = await focus_point_manager.initialize_focus_points(conversation_id, focus_points)
    assert result, "初始化关注点状态失败"
    
    # 测试加载关注点状态
    logger.info("测试加载关注点状态")
    status = await focus_point_manager.load_focus_points_status(conversation_id)
    assert len(status) == 3, f"加载的关注点状态数量不正确: {len(status)}"
    assert status["fp1"]["status"] == "pending", f"fp1状态不正确: {status['fp1']['status']}"
    
    # 测试更新关注点状态
    logger.info("测试更新关注点状态")
    result = await focus_point_manager.update_focus_point_status(
        conversation_id, "fp1", "processing"
    )
    assert result, "更新关注点状态失败"
    
    # 测试再次加载关注点状态
    status = await focus_point_manager.load_focus_points_status(conversation_id)
    assert status["fp1"]["status"] == "processing", f"fp1状态更新不正确: {status['fp1']['status']}"
    
    # 测试更新关注点状态和值
    result = await focus_point_manager.update_focus_point_status(
        conversation_id, "fp1", "completed", "用户需要一个任务管理系统"
    )
    assert result, "更新关注点状态和值失败"
    
    # 测试再次加载关注点状态
    status = await focus_point_manager.load_focus_points_status(conversation_id)
    assert status["fp1"]["status"] == "completed", f"fp1状态更新不正确: {status['fp1']['status']}"
    assert status["fp1"]["value"] == "用户需要一个任务管理系统", f"fp1值更新不正确: {status['fp1']['value']}"
    
    logger.info("关注点状态管理器测试通过")

async def test_message_manager():
    """测试消息管理器"""
    logger.info("开始测试消息管理器")
    
    # 设置测试数据库
    db_manager, conversation_id = await setup_test_db()
    message_manager = MessageManager(db_manager)
    
    # 测试保存消息
    logger.info("测试保存消息")
    result = await message_manager.save_message(
        conversation_id=conversation_id,
        sender_type="user",
        content="我需要一个任务管理系统"
    )
    assert result, "保存用户消息失败"
    
    result = await message_manager.save_message(
        conversation_id=conversation_id,
        sender_type="ai",
        content="请描述您需要的主要功能",
        focus_id="fp1",
        message_type="question"
    )
    assert result, "保存AI消息失败"
    
    # 测试加载会话历史
    logger.info("测试加载会话历史")
    history = await message_manager.load_conversation_history(conversation_id)
    assert len(history) == 2, f"加载的会话历史数量不正确: {len(history)}"
    assert history[0]["role"] == "user", f"第一条消息角色不正确: {history[0]['role']}"
    assert history[1]["role"] == "assistant", f"第二条消息角色不正确: {history[1]['role']}"
    
    # 测试获取关注点消息
    logger.info("测试获取关注点消息")
    focus_messages = await message_manager.get_focus_point_messages(conversation_id, "fp1")
    assert len(focus_messages) == 1, f"加载的关注点消息数量不正确: {len(focus_messages)}"
    assert focus_messages[0]["content"] == "请描述您需要的主要功能", f"关注点消息内容不正确: {focus_messages[0]['content']}"
    
    logger.info("消息管理器测试通过")

async def main():
    """主函数"""
    try:
        await test_focus_point_manager()
        await test_message_manager()
        logger.info("所有测试通过")
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise
    finally:
        # 清理测试数据库
        if os.path.exists(TEST_DB_PATH):
            os.remove(TEST_DB_PATH)

if __name__ == "__main__":
    asyncio.run(main())
