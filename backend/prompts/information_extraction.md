# 信息提取模板 (V3 - 精简优化)

## 任务描述
请结合**已知信息摘要**，从**用户的最新输入**中提取、更新或补充与关注点相关的信息。**请务必只返回JSON格式的内容，不得包含任何其他文字、注释、代码块标记或换行。**

## 关注点列表
{focus_points_str}

## 历史摘要 (Context)
{historical_summary_json}

## 用户输入 (Latest User Input)
{user_input}

## 提取原则与输出要求
1.专注关联：仅提取与关注点直接相关的信息。
2.用户优先：新旧信息冲突时，优先采纳用户的最新描述。
3.完整度评估：根据信息的完整性与明确性评估每个关注点的 completeness。
    1.0: 信息完整且明确
    0.7-0.9: 信息基本完整，可能缺少细节
    0.4-0.6: 信息部分完整，缺少重要细节
    0.1-0.3: 信息不完整，仅有少量相关内容
    0.0: 未提供任何相关信息
4.严格格式：输出必须为以下JSON结构。extracted_points 数组只包含从用户输入中实际能提取到信息的关注点。

{
    "extracted_points": [
        {
            "name": "关注点名称",
            "value": "更新或补充后的具体信息",
            "completeness": 0.8
        }
    ]
}
请严格按照上述JSON格式返回结果。

注意事项
确保提取信息准确反映用户描述。
完整度评分应基于实际信息的质量。
注意关注点之间可能存在的信息关联。
用简体中文回答。
