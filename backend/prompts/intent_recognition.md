# 意图识别提示词模板 (版本 v2)

## ！！！严格输出格式要求！！！
⚠️ 只允许输出唯一一行纯 JSON 对象，禁止包含：
  • 任何额外文本、说明、解释、注释
  • 代码块标记（如 ```json）
  • 多余的空格、换行、标点
  • 额外字段、顺序错误

✅ 唯一可接受格式：
{
  "intent": "provide_information",
  "emotion": "neutral",
  "confidence": 0.92,
  "entities": {}
}

## 意图识别任务
请根据下面的**完整对话记录**，分析**最后一条用户消息**的意图和情感。

## 完整对话记录
{full_conversation}

## 基础意图类型（intent，必选其一）
- provide_information: 用户提供信息或回答问题
- ask_question: 用户提出问题或描述需求（包括新的项目需求、业务需求等）
- request_clarification: 用户请求澄清或解释
- confirm: 用户确认或同意
- reject: 用户拒绝或否定
- modify: 用户想要修改之前提供的信息
- complete: 用户表示完成或结束
- greeting: 用户的问候或打招呼
- unknown: 无法确定用户意图

## 意图识别指导原则
- 如果用户描述了一个具体的项目、任务或业务需求，应识别为 ask_question
- 如果用户在回答之前的问题或提供相关信息，应识别为 provide_information
- 如果用户说"你好"、"hi"等问候语，应识别为 greeting
- 只有在完全无法理解用户意图时才使用 unknown

## 典型示例
### ask_question 示例：
- "项目名称：小程序UI设计项目，任务描述：根据原型文档设计用工类小程序..."
- "我需要开发一个电商网站"
- "帮我设计一个移动应用"
- "我想做一个数据分析项目"

### provide_information 示例：
- "我们的预算是10万元"
- "项目周期大概3个月"
- "目标用户是年轻人群体"

### greeting 示例：
- "你好"、"hi"、"hello"
- "早上好"、"下午好"

### 重要提醒：
- 包含项目名称、任务描述、设计要求等详细需求信息的输入，必须识别为 ask_question
- 置信度应该根据输入内容的明确程度设置，详细的项目需求描述置信度应该 >= 0.8

## 情感类型（emotion，必选其一） ￼
- positive（积极）
- negative（消极）
- neutral（中性）
- anxious（焦虑）
- confused（困惑）

## 字段要求
{
  "intent": "必须从上述基础意图类型中选择",
  "emotion": "必须从上述情感类型中选择", 
  "confidence": "0.0-1.0之间的数值",
  "entities": "空对象{}或实体字典"
}

### 违反格式后果
1. 系统将自动重试（最多3次）
2. 每次失败将增加处理延迟
3. 最终失败将导致错误响应

请严格按要求返回JSON：