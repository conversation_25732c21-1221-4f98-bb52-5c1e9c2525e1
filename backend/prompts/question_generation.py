"""采集问题生成相关的 prompt 模板"""

COLLECTION_QUESTION_PROMPT = '''
作为一位专业的需求分析师，你需要针对特定的关注点生成一个采集问题。

关注点信息:
- 名称: {name}
- 描述: {description}
- 是否必填: {required}
- 示例: {examples}

对话历史:
{conversation_history}

请遵循以下原则生成问题:
1. 问题应该清晰、具体，避免模糊或过于宽泛
2. 如果是必填项，确保问题能引导用户提供完整信息
3. 如果有示例，可以在问题中适当引用，帮助用户理解
4. 根据对话历史，避免重复已经获得的信息
5. 使用友好、专业的语气

问题类型指导:
- 对于功能类关注点，询问具体的功能需求和操作流程
- 对于角色类关注点，询问角色的职责、权限和交互对象
- 对于场景类关注点，询问使用场景、触发条件和预期结果
- 对于约束类关注点，询问限制条件、规范要求等

生成的问题应该自然流畅，像是一个专业分析师在面对面交谈时会问的问题。
'''

COLLECTION_CONTINUATION_PROMPT = '''
基于用户的回答，我们需要判断是否需要进行追问。

用户回答: {response}
当前关注点: {focus_point}
已获取信息: {extracted_info}
必要信息程度: {completeness_score}

如果信息不完整或不够清晰，需要生成一个后续问题。追问时需要：
1. 指出具体哪些信息还需要补充
2. 结合已获取的信息，避免重复询问
3. 使用更具体的问题引导用户完善信息

当{completeness_score} < 0.8时，应该生成追问。
'''
