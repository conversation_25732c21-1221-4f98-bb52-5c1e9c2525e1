"""
增强版Prompt加载器 - v2.1 (最终完整版)

主要功能：
1. 提供一个统一的、健壮的模板加载方法。
2. 消除冗余代码。
3. 提供便捷的模块级访问函数。
"""
import os
import re
import logging
from typing import Dict, Any, Optional, List
from .logging_config import is_template_logging_enabled

# 设置日志记录器
logger = logging.getLogger(__name__)


class PromptLoader:
    """统一和优化后的Prompt加载器类"""

    def __init__(self, prompts_dir: Optional[str] = None):
        if prompts_dir is None:
            # 采用更稳健的方式定位prompts目录 (假设此文件在utils下)
            self.prompts_dir = os.path.join(
                os.path.dirname(os.path.abspath(__file__)),
                "..", "prompts"
            )
        else:
            self.prompts_dir = prompts_dir
        logger.info(f"初始化Prompt加载器，使用目录：{self.prompts_dir}")
        if not os.path.exists(self.prompts_dir):
            logger.warning(f"Prompts目录不存在: {self.prompts_dir}")

    def load_prompt(self, template_name: str, variables: Dict[str, Any] = None, log_content: bool = None) -> str:
        """
        【核心加载方法】
        这是系统中唯一的、最健壮的模板加载器。
        它优先从文件加载，失败时使用正确的兜底模板。

        Args:
            template_name: 模板名称
            variables: 替换变量
            log_content: 是否在日志中输出替换后的完整内容，None时使用全局配置
        """
        if variables is None:
            variables = {}

        # 如果没有明确指定log_content，则使用全局配置
        if log_content is None:
            log_content = is_template_logging_enabled()

        try:
            # 统一逻辑：总是添加.md后缀
            template_path = os.path.join(self.prompts_dir, f"{template_name}.md")
            absolute_path = os.path.abspath(template_path)

            if os.path.exists(absolute_path):
                with open(absolute_path, 'r', encoding='utf-8') as f:
                    template = f.read()
                logger.debug(f"成功从文件加载模板: {template_name}.md")
                formatted_content = self._format_template(template, variables)

                # 如果需要，在日志中输出替换后的完整内容
                if log_content:
                    logger.info(f"[模板内容] {template_name} 替换占位符后的完整内容:\n{'-'*50}\n{formatted_content}\n{'-'*50}")

                return formatted_content

            # 文件不存在时，记录警告并使用默认兜底
            logger.warning(f"模板文件不存在: {absolute_path}，将使用默认兜底模板。")
            formatted_content = self._create_default_prompt(template_name, variables)

            if log_content:
                logger.info(f"[模板内容] {template_name} (兜底模板) 替换占位符后的完整内容:\n{'-'*50}\n{formatted_content}\n{'-'*50}")

            return formatted_content

        except Exception as e:
            logger.error(f"加载提示词 '{template_name}' 失败: {str(e)}", exc_info=True)
            formatted_content = self._create_default_prompt(template_name, variables)

            if log_content:
                logger.info(f"[模板内容] {template_name} (错误兜底) 替换占位符后的完整内容:\n{'-'*50}\n{formatted_content}\n{'-'*50}")

            return formatted_content

    def load_markdown_prompt(self, prompt_name: str, **kwargs) -> Optional[str]:
        """
        【便捷别名方法】
        这个方法现在只是 load_prompt 的一个简单包装，以兼容旧的调用方式。
        它不再包含重复的逻辑。
        """
        return self.load_prompt(prompt_name, kwargs)

    def _format_template(self, template: str, variables: Dict[str, Any]) -> str:
        formatted_template = template
        for key, value in variables.items():
            placeholder = f"{{{key}}}"
            formatted_template = formatted_template.replace(placeholder, str(value))

        # 检查是否还有未替换的占位符，并给出警告
        # 使用更精确的正则表达式，只匹配简单的变量名占位符，避免匹配JSON内容和代码块内容
        import re

        # 先移除代码块中的内容，避免误报代码块中的占位符
        temp_content = formatted_template
        # 移除 ```template...``` 代码块
        temp_content = re.sub(r'```template.*?```', '', temp_content, flags=re.DOTALL)
        # 移除 ```json...``` 代码块
        temp_content = re.sub(r'```json.*?```', '', temp_content, flags=re.DOTALL)
        # 移除其他代码块
        temp_content = re.sub(r'```.*?```', '', temp_content, flags=re.DOTALL)

        remaining_placeholders = re.findall(r'\{([a-zA-Z_][a-zA-Z0-9_]*)\}', temp_content)
        if remaining_placeholders:
            logger.warning(f"模板中存在未替换的占位符: {remaining_placeholders}")
            # 将未替换的占位符替换为默认值
            for placeholder_name in remaining_placeholders:
                placeholder = f"{{{placeholder_name}}}"
                if placeholder_name == "prompt_instruction":
                    formatted_template = formatted_template.replace(placeholder, "无")
                else:
                    formatted_template = formatted_template.replace(placeholder, f"[缺失参数: {placeholder_name}]")

        return formatted_template


    def _create_default_prompt(self, template_name: str, variables: Dict[str, Any]) -> str:
        """根据模板名称生成相应的默认提示词。"""
        if "intent_recognition" in template_name:
            return self._create_intent_prompt(variables)
        elif "domain_classifier" in template_name:
            return self._create_domain_prompt(variables)
        else:
            logger.warning(f"没有为'{template_name}'找到特定的默认模板，将返回通用格式。")
            return "\n".join([f"{k}: {v}" for k, v in variables.items()])

    def _create_intent_prompt(self, variables: Dict[str, Any]) -> str:
        """意图识别的v2兜底模板"""
        logger.warning("正在为 'intent_recognition' 使用硬编码的v2兜底模板。")
        base_prompt = """# 意图识别提示词模板 (版本 v2 - 兜底)

## ！！！严格输出格式要求！！！
⚠️ 必须只返回纯JSON对象，禁止包含：
- 任何额外文本说明
- 代码块标记（如 ```json`）
- 解释性内容
- 额外字段

✅ 唯一可接受格式：
{
  "intent": "provide_information",
  "emotion": "neutral",
  "confidence": 0.92,
  "entities": {}
}

## 意图识别任务
请根据下面的**完整对话记录**，分析**最后一条用户消息**的意图和情感。

## 完整对话记录
{full_conversation}

## 基础意图类型
- provide_information: 用户提供信息或回答问题
- ask_question: 用户提出问题
- request_clarification: 用户请求澄清或解释
- confirm: 用户确认或同意
- reject: 用户拒绝或否定
- modify: 用户想要修改之前提供的信息
- complete: 用户表示完成或结束
- unknown: 无法确定用户意图

## 情感类型
- positive（积极）
- negative（消极）
- neutral（中性）
- anxious（焦虑）
- confused（困惑）

## 字段要求
{
  "intent": "必须从上述基础意图类型中选择",
  "emotion": "必须从上述情感类型中选择", 
  "confidence": "0.0-1.0之间的数值",
  "entities": "空对象{}或实体字典"
}

### 违反格式后果
1. 系统将自动重试（最多3次）
2. 每次失败将增加处理延迟
3. 最终失败将导致错误响应

请严格按要求返回JSON：
"""
        return self._format_template(base_prompt, variables)

    def _create_domain_prompt(self, variables: Dict[str, Any]) -> str:
        """领域分类默认提示词"""
        # 您可以在这里添加或完善领域分类的兜底模板
        return f"""
## 领域分类任务
用户输入: {variables.get('user_input', '')}
可用领域: {variables.get('domains_list', '')}
请返回JSON格式的分类结果"""

    def get_prompt_version(self, prompt_name: str) -> Dict[str, str]:
        """获取模板版本信息"""
        prompt_path = os.path.join(self.prompts_dir, f"{prompt_name}.md")
        if not os.path.exists(prompt_path):
            return {"version": "unknown", "last_updated": "unknown"}
        try:
            last_updated = str(os.path.getmtime(prompt_path))
            with open(prompt_path, "r", encoding="utf-8") as f:
                content = f.read()
            version_match = re.search(r'版本\s*v?([0-9.]+)', content, re.IGNORECASE)
            version = version_match.group(1) if version_match else "1.0"
            return {"version": version, "last_updated": last_updated}
        except Exception as e:
            logger.error(f"获取版本信息失败：{str(e)}")
            return {"version": "error", "last_updated": "error"}

    def list_available_prompts(self, category: Optional[str] = None) -> List[str]:
        """列出可用模板"""
        search_dir = os.path.join(self.prompts_dir, category) if category else self.prompts_dir
        if not os.path.exists(search_dir):
            return []
        prompts = []
        for root, _, files in os.walk(search_dir):
            for file in files:
                if file.endswith(".md"):
                    rel_path = os.path.relpath(os.path.join(root, file), self.prompts_dir)
                    prompts.append(os.path.splitext(rel_path)[0].replace("\\", "/"))
        return prompts

# --- 您指出的、需要被包含的模块级代码 ---

# 创建全局实例
_loader = PromptLoader()

# 定义模块级函数，作为对全局实例方法的便捷调用
def load_prompt(template_name: str, variables: Dict[str, Any] = None) -> str:
    """便捷函数：加载并格式化一个Prompt模板。"""
    return _loader.load_prompt(template_name, variables)

def load_markdown_prompt(prompt_name: str, **kwargs) -> Optional[str]:
    """便捷函数：加载并格式化一个Markdown模板，使用kwargs作为变量。"""
    return _loader.load_markdown_prompt(prompt_name, **kwargs)

def get_prompt_version(prompt_name: str) -> Dict[str, str]:
    """便捷函数：获取指定Prompt模板的版本信息。"""
    return _loader.get_prompt_version(prompt_name)

def list_available_prompts(category: Optional[str] = None) -> List[str]:
    """便捷函数：列出所有可用的Prompt模板。"""
    return _loader.list_available_prompts(category)
