"""
性能监控初始化模块

此模块提供性能监控系统的初始化和关闭功能。
主要功能包括：
1. 初始化性能监控器
2. 配置性能监控参数
3. 启动后台监控任务
4. 优雅关闭监控系统
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional
from pathlib import Path

from .performance_monitor import performance_monitor, PerformanceMonitor
from ..config.settings import LOG_DIR

logger = logging.getLogger(__name__)


def init_performance_monitoring(
    enabled: bool = True,
    data_dir: Optional[str] = None,
    auto_save: bool = True,
    save_interval: int = 300,
    config: Optional[Dict[str, Any]] = None
) -> PerformanceMonitor:
    """
    初始化性能监控系统
    
    Args:
        enabled: 是否启用性能监控
        data_dir: 性能数据保存目录，默认使用 logs/performance
        auto_save: 是否自动保存性能数据
        save_interval: 自动保存间隔（秒）
        config: 额外配置参数
        
    Returns:
        PerformanceMonitor: 性能监控器实例
    """
    try:
        # 设置默认数据目录
        if data_dir is None:
            data_dir = str(LOG_DIR / "performance")
        
        # 确保数据目录存在
        if enabled:
            os.makedirs(data_dir, exist_ok=True)
            logger.info(f"性能监控数据目录: {data_dir}")
        
        # 应用配置
        if config:
            logger.info(f"应用性能监控配置: {config}")
        
        # 重新初始化性能监控器（如果需要）
        if not performance_monitor._initialized or not performance_monitor.enabled:
            # 关闭现有监控器
            if performance_monitor._initialized:
                performance_monitor.shutdown()
            
            # 重新初始化
            performance_monitor.__init__(
                data_dir=data_dir,
                enabled=enabled,
                auto_save=auto_save,
                save_interval=save_interval
            )
        
        if enabled:
            logger.info("性能监控系统初始化完成")
            logger.info(f"- 数据目录: {data_dir}")
            logger.info(f"- 自动保存: {auto_save}")
            logger.info(f"- 保存间隔: {save_interval}秒")
        else:
            logger.info("性能监控系统已禁用")
        
        return performance_monitor
        
    except Exception as e:
        logger.error(f"初始化性能监控系统失败: {str(e)}")
        raise


def shutdown_performance_features():
    """关闭性能监控功能"""
    try:
        if performance_monitor._initialized:
            # 保存最终的性能数据
            if performance_monitor.enabled:
                performance_monitor.save_metrics("final_performance_report.json")
            
            # 关闭监控器
            performance_monitor.shutdown()
            logger.info("性能监控系统已关闭")
        else:
            logger.info("性能监控系统未初始化，无需关闭")
            
    except Exception as e:
        logger.error(f"关闭性能监控系统时出错: {str(e)}")


def get_performance_config() -> Dict[str, Any]:
    """
    获取性能监控配置
    
    Returns:
        Dict[str, Any]: 当前配置信息
    """
    return {
        "enabled": performance_monitor.enabled,
        "data_dir": performance_monitor.data_dir,
        "auto_save": performance_monitor.auto_save,
        "save_interval": performance_monitor.save_interval,
        "initialized": performance_monitor._initialized
    }


def load_performance_config_from_file(config_file: str = None) -> Dict[str, Any]:
    """
    从配置文件加载性能监控配置
    
    Args:
        config_file: 配置文件路径，默认使用 backend/config/performance_optimization.json
        
    Returns:
        Dict[str, Any]: 配置数据
    """
    import json
    
    if config_file is None:
        config_file = "backend/config/performance_optimization.json"
    
    try:
        config_path = Path(config_file)
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"已加载性能配置文件: {config_file}")
            return config
        else:
            logger.warning(f"性能配置文件不存在: {config_file}")
            return {}
    except Exception as e:
        logger.error(f"加载性能配置文件失败: {str(e)}")
        return {}


def apply_performance_config(config: Dict[str, Any]):
    """
    应用性能监控配置
    
    Args:
        config: 配置数据
    """
    try:
        monitoring_config = config.get("monitoring", {})
        
        if monitoring_config.get("enable", True):
            # 提取监控配置
            sampling_rate = monitoring_config.get("sampling_rate", 0.1)
            metrics = monitoring_config.get("metrics", [])
            alert_thresholds = monitoring_config.get("alert_thresholds", {})
            
            logger.info(f"应用性能监控配置:")
            logger.info(f"- 采样率: {sampling_rate}")
            logger.info(f"- 监控指标: {metrics}")
            logger.info(f"- 告警阈值: {alert_thresholds}")
            
            # 这里可以根据需要应用具体的配置
            # 例如设置采样率、告警阈值等
            
        else:
            logger.info("根据配置文件，性能监控已禁用")
            
    except Exception as e:
        logger.error(f"应用性能监控配置失败: {str(e)}")


async def start_performance_monitoring_with_config(config_file: str = None):
    """
    使用配置文件启动性能监控
    
    Args:
        config_file: 配置文件路径
    """
    try:
        # 加载配置
        config = load_performance_config_from_file(config_file)
        
        # 提取监控配置
        monitoring_config = config.get("monitoring", {})
        enabled = monitoring_config.get("enable", True)
        
        # 初始化性能监控
        init_performance_monitoring(
            enabled=enabled,
            config=config
        )
        
        # 应用配置
        apply_performance_config(config)
        
        logger.info("基于配置文件的性能监控启动完成")
        
    except Exception as e:
        logger.error(f"启动性能监控失败: {str(e)}")
        raise


def get_performance_status() -> Dict[str, Any]:
    """
    获取性能监控状态
    
    Returns:
        Dict[str, Any]: 状态信息
    """
    return {
        "monitoring_active": performance_monitor._initialized and performance_monitor.enabled,
        "config": get_performance_config(),
        "metrics_count": {
            "api_metrics": len(performance_monitor.api_metrics),
            "llm_metrics": len(performance_monitor.llm_metrics),
            "db_metrics": len(performance_monitor.db_metrics)
        },
        "resource_monitoring": not performance_monitor.stop_monitoring,
        "auto_save_active": performance_monitor.auto_save and performance_monitor.enabled
    }
