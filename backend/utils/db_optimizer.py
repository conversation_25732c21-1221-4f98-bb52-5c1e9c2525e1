"""
数据库性能优化模块

此模块提供数据库查询的性能优化功能，包括：
1. 连接池管理
2. 查询缓存
3. 查询性能监控
4. 慢查询日志
"""

import logging
import time
import json
import hashlib
import sqlite3
import threading
import functools
from typing import Dict, List, Any, Optional, Union, Callable, Tuple
from collections import defaultdict, deque
import asyncio
from datetime import datetime, timedelta
from contextlib import contextmanager

# 设置日志记录器
logger = logging.getLogger(__name__)

class QueryCache:
    """查询缓存"""
    
    def __init__(self, max_size: int = 100, ttl: int = 300):
        """
        初始化查询缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl: 缓存条目生存时间（秒）
        """
        self.cache = {}  # 缓存字典，键为查询哈希，值为(结果, 过期时间)元组
        self.max_size = max_size
        self.ttl = ttl
        self.hits = 0
        self.misses = 0
        self.lock = threading.RLock()
        
    def _generate_key(self, query: str, params: tuple = None) -> str:
        """
        生成缓存键
        
        Args:
            query: SQL查询
            params: 查询参数
            
        Returns:
            str: 缓存键
        """
        # 创建一个包含查询和参数的字符串
        key_str = query
        if params:
            key_str += json.dumps(params, sort_keys=True)
        
        # 计算哈希值
        return hashlib.md5(key_str.encode()).hexdigest()
        
    def get(self, query: str, params: tuple = None) -> Optional[Any]:
        """
        获取缓存的查询结果
        
        Args:
            query: SQL查询
            params: 查询参数
            
        Returns:
            Optional[Any]: 缓存的结果，如果未命中缓存则返回None
        """
        with self.lock:
            # 清理过期缓存
            self._cleanup()
            
            # 生成缓存键
            key = self._generate_key(query, params)
            
            # 检查缓存
            if key in self.cache:
                result, expiry = self.cache[key]
                if time.time() < expiry:
                    self.hits += 1
                    logger.debug(f"查询缓存命中: {query[:50]}...")
                    return result
                else:
                    # 删除过期缓存
                    del self.cache[key]
            
            self.misses += 1
            return None
        
    def set(self, query: str, result: Any, params: tuple = None, ttl: int = None) -> None:
        """
        设置缓存
        
        Args:
            query: SQL查询
            params: 查询参数
            result: 查询结果
            ttl: 缓存生存时间（秒），如果为None则使用默认值
        """
        with self.lock:
            # 如果缓存已满，删除最旧的条目
            if len(self.cache) >= self.max_size:
                oldest_key = min(self.cache.items(), key=lambda x: x[1][1])[0]
                del self.cache[oldest_key]
                
            # 生成缓存键
            key = self._generate_key(query, params)
            
            # 设置缓存
            expiry = time.time() + (ttl if ttl is not None else self.ttl)
            self.cache[key] = (result, expiry)
            logger.debug(f"查询缓存设置: {query[:50]}...")
        
    def _cleanup(self) -> None:
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [k for k, (_, expiry) in self.cache.items() if current_time > expiry]
        for key in expired_keys:
            del self.cache[key]
            
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            logger.info("查询缓存已清空")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        with self.lock:
            total = self.hits + self.misses
            hit_rate = self.hits / total if total > 0 else 0.0
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "ttl": self.ttl,
                "hits": self.hits,
                "misses": self.misses,
                "hit_rate": hit_rate
            }

class ConnectionPool:
    """数据库连接池"""
    
    def __init__(self, db_path: str, min_connections: int = 2, max_connections: int = 10):
        """
        初始化连接池
        
        Args:
            db_path: 数据库路径
            min_connections: 最小连接数
            max_connections: 最大连接数
        """
        self.db_path = db_path
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.connections = []
        self.in_use = set()
        self.lock = threading.RLock()
        
        # 初始化最小连接数
        for _ in range(min_connections):
            conn = sqlite3.connect(db_path, check_same_thread=False)
            conn.row_factory = sqlite3.Row
            self.connections.append(conn)
        
        logger.info(f"数据库连接池初始化完成，路径: {db_path}, 初始连接数: {min_connections}")
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接
        
        Yields:
            sqlite3.Connection: 数据库连接
        """
        conn = None
        try:
            with self.lock:
                # 尝试获取空闲连接
                if self.connections:
                    conn = self.connections.pop()
                elif len(self.in_use) < self.max_connections:
                    # 创建新连接
                    conn = sqlite3.connect(self.db_path, check_same_thread=False)
                    conn.row_factory = sqlite3.Row
                else:
                    # 等待连接释放
                    raise RuntimeError("数据库连接池已满，无法获取连接")
                
                # 标记为使用中
                self.in_use.add(conn)
            
            # 返回连接
            yield conn
        finally:
            # 释放连接
            if conn is not None:
                with self.lock:
                    self.in_use.remove(conn)
                    self.connections.append(conn)
    
    def close_all(self):
        """关闭所有连接"""
        with self.lock:
            # 关闭空闲连接
            for conn in self.connections:
                conn.close()
            
            # 关闭使用中的连接
            for conn in self.in_use:
                conn.close()
            
            self.connections = []
            self.in_use = set()
            
            logger.info("数据库连接池已关闭所有连接")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取连接池统计信息
        
        Returns:
            Dict[str, Any]: 连接池统计信息
        """
        with self.lock:
            return {
                "db_path": self.db_path,
                "min_connections": self.min_connections,
                "max_connections": self.max_connections,
                "idle_connections": len(self.connections),
                "active_connections": len(self.in_use),
                "total_connections": len(self.connections) + len(self.in_use)
            }

class DBOptimizer:
    """数据库优化器"""
    
    def __init__(
        self,
        db_path: str,
        enable_connection_pool: bool = True,
        min_connections: int = 2,
        max_connections: int = 10,
        enable_query_cache: bool = True,
        cache_size: int = 100,
        cache_ttl: int = 300,
        log_slow_queries: bool = True,
        slow_query_threshold: float = 0.1
    ):
        """
        初始化数据库优化器
        
        Args:
            db_path: 数据库路径
            enable_connection_pool: 是否启用连接池
            min_connections: 最小连接数
            max_connections: 最大连接数
            enable_query_cache: 是否启用查询缓存
            cache_size: 缓存大小
            cache_ttl: 缓存TTL（秒）
            log_slow_queries: 是否记录慢查询
            slow_query_threshold: 慢查询阈值（秒）
        """
        self.db_path = db_path
        self.enable_connection_pool = enable_connection_pool
        self.enable_query_cache = enable_query_cache
        self.log_slow_queries = log_slow_queries
        self.slow_query_threshold = slow_query_threshold
        
        # 初始化连接池
        if enable_connection_pool:
            self.connection_pool = ConnectionPool(
                db_path=db_path,
                min_connections=min_connections,
                max_connections=max_connections
            )
        else:
            self.connection_pool = None
        
        # 初始化查询缓存
        if enable_query_cache:
            self.query_cache = QueryCache(
                max_size=cache_size,
                ttl=cache_ttl
            )
        else:
            self.query_cache = None
        
        # 查询统计
        self.query_stats = defaultdict(lambda: {
            "count": 0,
            "total_time": 0.0,
            "min_time": float('inf'),
            "max_time": 0.0,
            "avg_time": 0.0,
            "slow_count": 0
        })
        
        logger.info("数据库优化器初始化完成")
    
    @contextmanager
    def optimized_connection(self):
        """
        获取优化的数据库连接
        
        Yields:
            sqlite3.Connection: 数据库连接
        """
        if self.enable_connection_pool and self.connection_pool:
            with self.connection_pool.get_connection() as conn:
                yield conn
        else:
            # 直接创建连接
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            try:
                yield conn
            finally:
                conn.close()
    
    def execute_query(self, query: str, params: tuple = None, cacheable: bool = True) -> List[Dict[str, Any]]:
        """
        执行查询
        
        Args:
            query: SQL查询
            params: 查询参数
            cacheable: 是否可缓存
            
        Returns:
            List[Dict[str, Any]]: 查询结果
        """
        # 检查缓存
        if self.enable_query_cache and self.query_cache and cacheable and query.strip().upper().startswith("SELECT"):
            cached_result = self.query_cache.get(query, params)
            if cached_result is not None:
                return cached_result
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 执行查询
            with self.optimized_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                # 获取结果
                if query.strip().upper().startswith("SELECT"):
                    result = [dict(row) for row in cursor.fetchall()]
                else:
                    conn.commit()
                    result = {"rowcount": cursor.rowcount, "lastrowid": cursor.lastrowid}
            
            # 记录耗时
            elapsed_time = time.time() - start_time
            
            # 更新统计信息
            self._update_stats(query, elapsed_time)
            
            # 记录慢查询
            if self.log_slow_queries and elapsed_time > self.slow_query_threshold:
                logger.warning(f"慢查询: {query[:100]}... - {elapsed_time:.4f}秒")
            
            # 缓存结果
            if (self.enable_query_cache and self.query_cache and cacheable and 
                query.strip().upper().startswith("SELECT")):
                self.query_cache.set(query, params, result)
            
            return result
        except Exception as e:
            # 记录错误
            elapsed_time = time.time() - start_time
            logger.error(f"查询出错: {query[:100]}... - {str(e)} - {elapsed_time:.4f}秒")
            raise
    
    def _update_stats(self, query: str, elapsed_time: float):
        """
        更新查询统计信息
        
        Args:
            query: SQL查询
            elapsed_time: 查询耗时（秒）
        """
        # 提取查询类型
        query_type = query.strip().split()[0].upper()
        
        # 更新统计信息
        stats = self.query_stats[query_type]
        stats["count"] += 1
        stats["total_time"] += elapsed_time
        stats["min_time"] = min(stats["min_time"], elapsed_time)
        stats["max_time"] = max(stats["max_time"], elapsed_time)
        stats["avg_time"] = stats["total_time"] / stats["count"]
        
        if elapsed_time > self.slow_query_threshold:
            stats["slow_count"] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取数据库优化器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {
            "db_path": self.db_path,
            "query_stats": {k: v for k, v in self.query_stats.items() if v["count"] > 0}
        }
        
        # 添加连接池统计
        if self.enable_connection_pool and self.connection_pool:
            stats["connection_pool"] = self.connection_pool.get_stats()
        
        # 添加查询缓存统计
        if self.enable_query_cache and self.query_cache:
            stats["query_cache"] = self.query_cache.get_stats()
        
        return stats
    
    def clear_cache(self):
        """清空查询缓存"""
        if self.enable_query_cache and self.query_cache:
            self.query_cache.clear()
            logger.info("查询缓存已清空")
    
    def shutdown(self):
        """关闭数据库优化器"""
        if self.enable_connection_pool and self.connection_pool:
            self.connection_pool.close_all()
        logger.info("数据库优化器已关闭")

# 创建全局实例
db_optimizer = None  # 需要在应用启动时初始化
