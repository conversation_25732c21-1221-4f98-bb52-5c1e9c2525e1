"""
基于FastAPI的需求采集系统API

提供与前端交互的接口，用于测试需求采集系统的功能。
集成了性能监控和优化模块，提高系统性能和稳定性。
支持AutoGen框架的会话管理和生命周期控制。
"""

import os
import json
import asyncio
import time
import datetime
import asyncio
from typing import Dict, List, Any, Optional
from contextlib import asynccontextmanager
from pathlib import Path
# IOLogger已被统一日志系统替代
from backend.data.db.database_manager import DatabaseManager
# 先导入logging以便后续使用
import logging

# 导入统一日志配置（确保在导入其他模块前配置）
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from backend.utils.logging_config import configure_logging, get_logger, BusinessLogger, ErrorLogger, SessionLogger, error_handler

# 配置优化的日志系统
configure_logging(
    log_level=logging.DEBUG,  # 开发环境记录所有细节
    enable_console=True,
    enable_file=True,
    max_bytes=10*1024*1024,  # 10MB日志文件
    backup_count=7,  # 保留7份备份
    json_format_enabled=True,  # 启用JSON格式，便于检索
    deduplication_interval=2.0,  # 2秒去重窗口，防止高频日志刷屏
    log_format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    template_logging_enabled=False  # 关闭模版内容日志输出
)

# 获取日志记录器
logger = get_logger(__name__)

# 测试日志
logger.info("=== API服务启动，日志系统初始化 ===")
logger.debug("这是一条调试日志")
logger.warning("这是一条警告日志")
logger.error("这是一条错误日志")

# 确保控制台处理器也能显示DEBUG级别的日志
for handler in logging.getLogger().handlers:
    if isinstance(handler, logging.StreamHandler):
        handler.setLevel(logging.INFO)

# 其他导入
import autogen

# 其他导入
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import autogen

# 导入Agent
from backend.agents.llm_service import AutoGenLLMServiceAgent
from backend.agents.knowledge_base import KnowledgeBaseAgent
from backend.agents.intent_recognition import IntentRecognitionAgent

from backend.agents.conversation_flow import AutoGenConversationFlowAgent
from backend.agents.user_interaction import UserInteractionAgent
from backend.agents.domain_classifier import DomainClassifierAgent
from backend.agents.category_classifier import CategoryClassifierAgent
from backend.agents.information_extractor import InformationExtractorAgent
from backend.agents.document_generator import DocumentGenerator  # 添加导入
from backend.agents.review_and_refine import AutoGenReviewAndRefineAgent  # 添加导入
from backend.agents.llm_config_manager import LLMConfigManager

# 性能监控模块
from backend.utils.performance_monitor import performance_monitor
from backend.utils.performance_middleware import add_performance_middleware
from backend.utils.performance_init import (
     init_performance_monitoring,
     shutdown_performance_features
 )

# 导入配置
from backend.config.settings import LLM_CONFIGS, API_REQUEST_TIMEOUT

# AutoGen配置
AUTOGEN_CONFIG = {
    "work_dir": "/tmp/autogen_work",  # 使用临时目录，避免在logs目录创建文件
    "config_list": [
        {
            "model": LLM_CONFIGS["deepseek-chat"]["model_name"],
            "api_key": LLM_CONFIGS["deepseek-chat"]["api_key"],
            "base_url": LLM_CONFIGS["deepseek-chat"]["api_base"],
            "api_type": "deepseek"
        }
    ]
}

# 初始化AutoGen工作目录
os.makedirs(AUTOGEN_CONFIG["work_dir"], exist_ok=True)

# 存储会话实例
sessions: Dict[str, Any] = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("应用启动，初始化AutoGen环境...")
    from autogen.oai import config_list_from_json
    autogen.oai.config_list = AUTOGEN_CONFIG["config_list"]

    # 初始化性能监控系统
    logger.info("初始化性能监控系统...")
    try:
        init_performance_monitoring(
            enabled=True,
            auto_save=True,
            save_interval=300  # 5分钟保存一次
        )
        logger.info("性能监控系统初始化成功")
    except Exception as e:
        logger.error(f"性能监控系统初始化失败: {str(e)}")

    # 启动定期清理任务
    cleanup_task = asyncio.create_task(run_periodic_cleanup())

    yield

    logger.info("应用关闭，清理资源...")

    # 关闭性能监控系统
    try:
        shutdown_performance_features()
        logger.info("性能监控系统已关闭")
    except Exception as e:
        logger.error(f"关闭性能监控系统时出错: {str(e)}")

    # 取消清理任务
    cleanup_task.cancel()
    try:
        await cleanup_task
    except asyncio.CancelledError:
        logger.info("清理任务已取消")

async def run_periodic_cleanup():
    """运行定期清理任务"""
    while True:
        try:
            await cleanup_expired_sessions()
            await asyncio.sleep(3600)
        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error(f"清理会话时出错: {str(e)}")
            await asyncio.sleep(60)

async def cleanup_expired_sessions(max_age_hours: int = 24):
    """清理过期会话"""
    current_time = time.time()
    for session_id in list(sessions.keys()):
        # 检查会话最后活动时间
        session_data = sessions[session_id]

        # 处理不同类型的会话对象
        if isinstance(session_data, tuple) and len(session_data) == 2:
            # 处理 (groupchat, manager) 类型的会话
            groupchat, _ = session_data
            if not hasattr(groupchat, 'messages') or not groupchat.messages:
                continue
            last_message_time = getattr(groupchat.messages[-1], 'timestamp', 0) or 0
        elif hasattr(session_data, 'get_state'):
            # 处理有 get_state 方法的会话对象
            state = session_data.get_state()
            last_message_time = state.get('last_activity', 0)
        else:
            # 无法确定最后活动时间的会话，保守处理
            continue

        # 检查是否过期
        if current_time - last_message_time > max_age_hours * 3600:
            logger.info(f"清理过期会话: {session_id}")
            del sessions[session_id]

# 定义数据模型
class Message(BaseModel):
    """用户消息模型"""
    message: str = Field(..., min_length=1, max_length=2000, description="用户输入的消息内容")
    session_id: str = Field(default="default_session", description="会话ID")

class SessionInfo(BaseModel):
    """会话信息模型"""
    session_id: str

class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., min_length=1, max_length=2000)
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    """聊天响应模型"""
    response: str
    session_id: str
    state: Optional[Dict[str, Any]] = None
    domain_result: Optional[Dict[str, Any]] = None
    category_result: Optional[Dict[str, Any]] = None
    focus_points_status: Optional[List[Dict[str, Any]]] = None



# 创建FastAPI应用
app = FastAPI(
    title="需求采集系统API(统一入口)",
    description="提供与前端交互的完整API接口，集成AutoGen框架和所有功能模块",
    version="2.0.0",
    openapi_tags=[
        {
            "name": "chat",
            "description": "处理用户对话的核心接口"
        },
        {
            "name": "autogen",
            "description": "AutoGen框架相关接口"
        },
        {
            "name": "session",
            "description": "会话管理相关接口"
        },
        {
            "name": "system",
            "description": "系统管理接口"
        }
    ],
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# 添加性能监控中间件
add_performance_middleware(app, enabled=True)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

# 创建LLM配置管理器(使用settings.py中的默认配置)
llm_config_manager = LLMConfigManager()

# 创建Agent实例
llm_service_agent = AutoGenLLMServiceAgent(
    enable_cache=True,
    cache_size=100,
    cache_ttl=3600
)

# 创建KnowledgeBaseAgent
from backend.config.settings import DATABASE_PATH
knowledge_base_agent = KnowledgeBaseAgent(
    db_path=str(DATABASE_PATH)
)

# 创建DatabaseManager实例
db_manager = DatabaseManager(str(DATABASE_PATH))

# 创建DomainClassifierAgent
domain_classifier_agent = DomainClassifierAgent(
    llm_client=llm_service_agent,
    agent_name="domain_classifier"  # 使用agent_name而非model_name
)
domain_classifier_agent.knowledge_base = knowledge_base_agent

# 创建IntentRecognitionAgent
intent_recognition_agent = IntentRecognitionAgent(
    llm_service=llm_service_agent,
    agent_name="intent_recognition"
)



# 创建CategoryClassifierAgent
category_classifier_agent = CategoryClassifierAgent(
    llm_client=llm_service_agent,
    agent_name="category_classifier"
)

# 创建InformationExtractorAgent
information_extractor_agent = InformationExtractorAgent(
    llm_service=llm_service_agent,
    intent_recognition_agent=intent_recognition_agent,
    knowledge_base_agent=knowledge_base_agent
)

# 创建DocumentGenerator
document_generator_agent = DocumentGenerator(
    llm_client=llm_service_agent,
    db_manager=db_manager
)

# 创建AutoGenReviewAndRefineAgent
review_and_refine_agent = AutoGenReviewAndRefineAgent(
    llm_client=llm_service_agent,
    llm_config={"db_path": str(DATABASE_PATH)}
)

# 创建ConversationFlowAgent
# 重构后的ConversationFlowManager只需要db_path参数
# 所有代理都在内部初始化
conversation_flow_agent = AutoGenConversationFlowAgent(
    llm_client=llm_service_agent,  # 修复：使用llm_service_agent而不是未定义的llm_client
    information_extractor_agent=information_extractor_agent,
    document_generator_agent=document_generator_agent,
    review_and_refine_agent=review_and_refine_agent,
    domain_classifier_agent=domain_classifier_agent,
    knowledge_base_agent=knowledge_base_agent,  # 添加 knowledge_base_agent 参数
    category_classifier_agent=category_classifier_agent,
    db_path=DATABASE_PATH,
    system_message=AUTOGEN_CONFIG.get("system_message", "")
)

# 创建UserInteractionAgent
user_interaction_agent = UserInteractionAgent()

@app.post("/chat", tags=["chat"], summary="处理用户消息(统一入口)", description="接收用户输入并返回系统响应，集成所有功能模块")
@performance_monitor.track_api_call("chat")
async def chat(message: Message):
    """处理用户消息
    - 接收用户文本输入
    - 维护会话状态
    - 返回系统响应
    """
    try:
        # 确保有会话ID
        session_id = message.session_id or f"session_{int(time.time())}"

        # 创建会话日志记录器
        session_logger = SessionLogger(__name__, session_id=session_id, user_id="anonymous")

        # 记录用户输入
        session_logger.log_user_input(
            message=message.message,
            input_type="text",
            request_id=f"req_{int(time.time())}"
        )

        # 记录业务节点 - 开始处理请求
        session_logger.log_business_node(
            node_name="chat_request_processing",
            node_type="request_processing",
            result="started"
        )

        # 构建消息字典
        message_dict = {
            "text": message.message,
            "session_id": session_id,
            "context": {"session_id": session_id}
        }

        # 记录业务节点 - 开始LLM处理
        session_logger.log_business_node(
            node_name="llm_processing",
            node_type="llm_processing",
            result="started"
        )

        # 调用对话流程Agent处理消息，添加超时控制
        start_time = time.time()
        try:
            # 使用配置的API超时时间，确保比LLM超时时间更长
            response_data = await asyncio.wait_for(
                conversation_flow_agent.process_message(message_dict),
                timeout=API_REQUEST_TIMEOUT
            )
        except asyncio.TimeoutError:
            logger.error(f"对话流程处理超时（{API_REQUEST_TIMEOUT}秒），会话: {session_id}")
            return JSONResponse(
                status_code=408,
                content={
                    "response": "抱歉，处理您的请求超时，请稍后再试。",
                    "session_id": session_id,
                    "domain_result": None,
                    "category_result": None,
                    "focus_points_status": []
                }
            )
        processing_duration = time.time() - start_time

        # 记录LLM调用结果
        if isinstance(response_data, dict) and "text_response" in response_data and response_data['text_response'] is not None:
            session_logger.log_llm_call(
                model="conversation_flow",
                scenario="chat_completion",
                prompt_length=len(message.message),
                response_length=len(response_data['text_response']),
                duration=processing_duration
            )
            # 记录AI回复
            session_logger.log_ai_response(
                response=response_data['text_response'],
                model="conversation_flow",
                duration=processing_duration
            )
        elif isinstance(response_data, str):
            session_logger.log_llm_call(
                model="conversation_flow",
                scenario="chat_completion",
                prompt_length=len(message.message),
                response_length=len(response_data),
                duration=processing_duration
            )
            # 记录AI回复
            session_logger.log_ai_response(
                response=response_data,
                model="conversation_flow",
                duration=processing_duration
            )
        else:
            # 当response_data不是预期格式时，仍然记录LLM调用但不包含响应长度
            session_logger.log_llm_call(
                model="conversation_flow",
                scenario="chat_completion",
                prompt_length=len(message.message),
                response_length=0,  # 无法确定响应长度时设为0
                duration=processing_duration
            )


        # 确保response_data有效
        if (response_data is None or
            not isinstance(response_data, dict) or
            "text_response" not in response_data or
            response_data["text_response"] is None):
            logger.error(f"对话流程Agent返回无效响应: {response_data}")
            # 构建符合前端期望的错误响应结构
            return {
                "response": "抱歉，系统未能生成有效响应，请稍后再试。",
                "session_id": session_id,
                "domain_result": None,
                "category_result": None,
                "focus_points_status": []
            }

        # 构建最终返回给前端的响应
        # ChatResponse 模型定义了这些字段: response, session_id, domain_result, category_result, focus_points_status
        # 我们需要确保返回的字典键与前端期望的一致 (frontend/src/pages/ConversationPage.tsx)
        # 前端使用的是: data.response, data.domain_result, data.category_result, data.focus_points_status
        
        api_response = {
            "response": response_data.get("text_response"),
            "session_id": session_id,
            "domain_result": response_data.get("domain_result"),
            "category_result": response_data.get("category_result"),
            "focus_points_status": response_data.get("focus_points_status")
        }

        # 记录业务节点 - 请求处理完成
        session_logger.log_business_node(
            node_name="chat_request_completed",
            node_type="request_completion",
            result="success",
            response_size=len(json.dumps(api_response, ensure_ascii=False))
        )

        return api_response
    except Exception as e:
        # 使用专门的错误日志记录器记录详细错误信息
        error_logger = ErrorLogger(__name__, session_id=session_id)
        error_logger.log_system_error(
            component="chat_api",
            operation="process_chat_request",
            error=e,
            request_data={
                "message_length": len(message.message),
                "session_id": session_id,
                "message_preview": message.message[:100] + "..." if len(message.message) > 100 else message.message
            }
        )

        # 在异常情况下，也尝试返回符合前端期望结构的错误信息
        return JSONResponse(
            status_code=500,
            content={
                "response": f"服务器内部错误: {str(e)}",
                "session_id": message.session_id or "unknown_session", # Try to get session_id if possible
                "domain_result": None,
                "category_result": None,
                "focus_points_status": []
            }
        )

@app.post("/autogen/chat", tags=["autogen"], response_model=ChatResponse,
          summary="AutoGen聊天接口(兼容模式)",
          description="兼容旧版AutoGen框架的聊天接口，建议使用/chat接口")
@performance_monitor.track_api_call("autogen_chat")
async def autogen_chat(request: ChatRequest):
    """处理基于AutoGen框架的聊天消息"""
    try:
        logger.info(f"收到AutoGen聊天请求: {request.model_dump_json()}")

        # 获取或创建会话
        session_id = request.session_id or f"session_{int(time.time())}"
        
        # 创建会话日志记录器
        session_logger = SessionLogger(__name__, session_id=session_id, user_id="anonymous")

        # 记录用户输入
        session_logger.log_user_input(
            message=request.message,
            input_type="text"
        )

        if session_id not in sessions:
            llm_agent = AutoGenLLMServiceAgent()
            user_agent = autogen.UserProxyAgent(
                name="UserProxy",
                human_input_mode="NEVER",
                default_auto_reply="...",
                llm_config={"config_list": AUTOGEN_CONFIG["config_list"]},
                code_execution_config={"use_docker": False}
            )
            groupchat = autogen.GroupChat(
                agents=[user_agent, llm_agent],
                messages=[],
                max_round=10
            )
            manager = autogen.GroupChatManager(
                groupchat=groupchat,
                llm_config={"config_list": AUTOGEN_CONFIG["config_list"]}
            )
            sessions[session_id] = (groupchat, manager)

        # 记录业务节点 - 开始LLM处理
        session_logger.log_business_node(
            node_name="autogen_llm_processing",
            node_type="llm_processing",
            result="started"
        )

        # 直接使用LLMServiceAgent处理消息
        groupchat, manager = sessions[session_id]
        llm_agent = groupchat.agents[1]

        start_time = time.time()
        response = await llm_agent.call_llm(
            messages=[{"role": "user", "content": request.message}],
            scenario="conversation",
            session_id=session_id
        )
        processing_duration = time.time() - start_time

        # 记录LLM调用
        session_logger.log_llm_call(
            model=llm_agent.get_model_name(),
            scenario="autogen_conversation",
            prompt_length=len(request.message),
            response_length=len(str(response)),
            duration=processing_duration
        )

        # 记录AI回复
        session_logger.log_ai_response(
            response=str(response),
            model=llm_agent.get_model_name(),
            duration=processing_duration
        )

        return ChatResponse(
            response=str(response),
            session_id=session_id
        )

    except Exception as e:
        # 使用专门的错误日志记录器
        error_logger = ErrorLogger(__name__, session_id=session_id)
        error_logger.log_system_error(
            component="autogen_chat_api",
            operation="process_autogen_chat",
            error=e,
            request_data={
                "message": request.message[:100] + "..." if len(request.message) > 100 else request.message,
                "session_id": session_id
            }
        )
        raise HTTPException(status_code=500, detail="服务器内部错误")

@app.get("/health", tags=["system"])
async def health_check():
    """健康检查端点"""
    return {
        "status": "健康",
        "timestamp": datetime.datetime.now().isoformat(),
        "message": "系统正常运行"
    }

@app.get("/sessions", tags=["session"])
async def list_sessions():
    """列出所有活跃会话"""
    session_list = []
    for session_id, session_data in sessions.items():
        session_info = {"session_id": session_id}

        # 尝试获取更多会话信息
        try:
            if isinstance(session_data, tuple) and len(session_data) == 2:
                groupchat, _ = session_data
                session_info["type"] = "autogen_groupchat"
                session_info["agent_count"] = len(groupchat.agents) if hasattr(groupchat, 'agents') else 0
            elif hasattr(session_data, 'get_state'):
                state = session_data.get_state()
                session_info["type"] = "user_interaction"
                session_info["state"] = state.get("current_state", "UNKNOWN")
            else:
                session_info["type"] = "unknown"
        except Exception as e:
            logger.error(f"获取会话信息时出错: {str(e)}")
            session_info["type"] = "error"

        session_list.append(session_info)

    return {
        "sessions": session_list,
        "count": len(session_list)
    }

@app.delete("/sessions/{session_id}", tags=["session"])
async def delete_session(session_id: str):
    """删除指定会话"""
    if (session_id in sessions):
        del sessions[session_id]
        return {"status": "success", "message": f"会话 {session_id} 已删除"}
    else:
        raise HTTPException(status_code=404, detail=f"会话 {session_id} 不存在")

@app.get("/performance/stats", tags=["system"])
async def get_performance_stats():
    """获取性能统计信息"""
    try:
        from backend.utils.performance_middleware import get_performance_stats
        stats = get_performance_stats()
        return {
            "status": "success",
            "data": stats,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        error_logger = ErrorLogger(__name__)
        error_logger.log_system_error(
            component="performance_api",
            operation="get_performance_stats",
            error=e
        )
        raise HTTPException(status_code=500, detail="获取性能统计信息失败")

@app.get("/performance/status", tags=["system"])
async def get_performance_status():
    """获取性能监控状态"""
    try:
        from backend.utils.performance_init import get_performance_status
        status = get_performance_status()
        return {
            "status": "success",
            "data": status,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取性能监控状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取性能监控状态失败")

@app.post("/performance/reset", tags=["system"])
async def reset_performance_stats():
    """重置性能统计数据"""
    try:
        from backend.utils.performance_middleware import reset_performance_stats
        reset_performance_stats()
        return {
            "status": "success",
            "message": "性能统计数据已重置",
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"重置性能统计数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="重置性能统计数据失败")

@app.post("/performance/save", tags=["system"])
async def save_performance_report():
    """保存性能报告"""
    try:
        from backend.utils.performance_middleware import save_performance_report
        file_path = save_performance_report()
        return {
            "status": "success",
            "message": "性能报告已保存",
            "file_path": file_path,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"保存性能报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail="保存性能报告失败")


