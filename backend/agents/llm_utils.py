"""
LLM工具类，包含错误处理、断路器、缓存等功能
"""
from enum import Enum
import time
from typing import Dict, Any, Optional
from collections import OrderedDict

class LLMErrorType(Enum):
    """LLM错误类型枚举"""
    CLIENT_ERROR = "client_error"
    SERVER_ERROR = "server_error"
    RATE_LIMIT = "rate_limit"
    TIMEOUT = "timeout"
    PARSE_ERROR = "parse_error"
    INVALID_REQUEST = "invalid_request"
    CIRCUIT_BREAKER = "circuit_breaker"

class LLMError(Exception):
    """LLM错误基类"""
    def __init__(self, message: str, error_type: LLMErrorType):
        super().__init__(message)
        self.error_type = error_type

def classify_error(error: Exception, provider: str) -> LLMError:
    """根据异常和提供商分类错误"""
    error_msg = str(error)
    if "timeout" in error_msg.lower():
        return LLMError(error_msg, LLMErrorType.TIMEOUT)
    elif "rate limit" in error_msg.lower():
        return LLMError(error_msg, LLMErrorType.RATE_LIMIT)
    # 其他错误分类逻辑...
    return LLMError(error_msg, LLMErrorType.SERVER_ERROR)

class CircuitBreaker:
    """断路器实现"""
    def __init__(self, failure_threshold: int = 5, recovery_time: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_time = recovery_time
        self.failure_count = 0
        self.last_failure_time = None
        self.is_open = False

    def record_failure(self):
        """记录失败"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        if self.failure_count >= self.failure_threshold:
            self.is_open = True

    def record_success(self):
        """记录成功"""
        self.failure_count = 0
        self.is_open = False

    def is_open(self) -> bool:
        """检查断路器是否打开"""
        if self.is_open and (time.time() - self.last_failure_time) > self.recovery_time:
            self.is_open = False
            self.failure_count = 0
        return self.is_open

class ResponseCache:
    """响应缓存实现"""
    def __init__(self, max_size: int = 100, ttl: int = 3600):
        self.max_size = max_size
        self.ttl = ttl
        self.cache = OrderedDict()

    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        if key not in self.cache:
            return None
        value, timestamp = self.cache[key]
        if time.time() - timestamp > self.ttl:
            del self.cache[key]
            return None
        # 更新访问顺序
        self.cache.move_to_end(key)
        return value

    def set(self, key: str, value: Any) -> None:
        """设置缓存"""
        if len(self.cache) >= self.max_size:
            self.cache.popitem(last=False)
        self.cache[key] = (value, time.time())

    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
