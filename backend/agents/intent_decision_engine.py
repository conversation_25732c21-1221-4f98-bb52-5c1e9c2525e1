# intent_decision_engine.py

import json
import logging
from typing import Any, Dict, List, Optional
# 确保导入的模块路径正确
from .intent_recognition import IntentRecognitionAgent, IntentResult
from .decision_engine import DecisionEngine 

class IntentDecisionEngine:
    """
    意图识别+决策一体化模块 v2.0
    """
    def __init__(self, llm_service: Any, agent_name: str = "intent_recognition", decision_engine: Optional[Any] = None):
        self.intent_agent = IntentRecognitionAgent(llm_service, agent_name=agent_name)
        # 根据我们的新架构，这里应该实例化重构后的DecisionEngine
        self.decision_engine = decision_engine or DecisionEngine()
        self.logger = logging.getLogger(__name__)

    async def analyze(self, message: str, context: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        一步完成意图识别+决策，返回包含完整策略对象的决策结果。
        """
        try:
            # 1. 提取对话历史
            conversation_history = None
            if context:
                for ctx_item in context:
                    if isinstance(ctx_item, dict) and "history" in ctx_item:
                        conversation_history = ctx_item["history"]
                        break

            # 2. 意图识别
            intent_result = await self.intent_agent.recognize_intent(message, conversation_history)

            # 3. 准备决策上下文
            decision_context = {}
            if context:
                for ctx_item in context:
                    if isinstance(ctx_item, dict):
                        decision_context.update(ctx_item)

            # --- 核心修正 ---
            # 正确做法：调用 get_strategy，它返回完整的策略字典。
            final_strategy = self.decision_engine.get_strategy(
                intent_result.intent,
                intent_result.emotion,
                decision_context
            )

            # 4. 组装返回结果
            return {
                "intent": intent_result.intent,
                "emotion": intent_result.emotion,
                "confidence": intent_result.confidence,
                "entities": intent_result.entities,
                "decision": final_strategy, # <--- 修正：现在返回的是完整的策略字典
                "intent_result": intent_result,
                # 为了向后兼容或方便使用，可以额外保留 prompt_instruction 字段
                "prompt_instruction": final_strategy.get("prompt_instruction")
            }
        except Exception as e:
            self.logger.error(f"IntentDecisionEngine分析失败: {str(e)}", exc_info=True)
            # 确保出错时 decision 也是一个字典，以防万一
            default_strategy_on_error = {"action": "handle_unknown_situation", "prompt_instruction": "抱歉，系统出现内部错误。"}
            return {
                "intent": "error",
                "emotion": "unknown",
                "confidence": 0.0,
                "entities": {},
                "decision": default_strategy_on_error, 
                "prompt_instruction": default_strategy_on_error["prompt_instruction"],
                "error": str(e)
            }
