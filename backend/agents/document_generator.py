# -*- coding: utf-8 -*-
from datetime import datetime
import logging
import json
import os
import re
import uuid
from typing import Dict, Optional, Any, List
from .base import AutoGenBaseAgent
from backend.data.db.database_manager import DatabaseManager
from backend.config.settings import LLM_CONFIGS, DEFAULT_MODEL, SCENARIO_LLM_MAPPING
from backend.utils.prompt_loader import PromptLoader


logger = logging.getLogger(__name__)

# 常量定义
class DocumentConstants:
    """文档生成器常量"""
    DEFAULT_OVERVIEW = "无项目概述"
    DEFAULT_DELIVERY_TIME = "待定"
    DEFAULT_DELIVERY_AMOUNT = "待定"
    DEFAULT_RECOMMENDATIONS = "无项目建议"
    DEFAULT_REQUIREMENTS = "### 待完善的需求信息\n\n暂无具体需求信息，请继续完善需求采集。"
    
    # 正则表达式模式
    JSON_BLOCK_PATTERN = r'```json\s*([\s\S]*?)\s*```'
    JSON_OBJECT_PATTERN = r'\{[\s\S]*\}'
    
    # 字段提取模式
    FIELD_PATTERNS = {
        'overview': r'\*\*\{?overview\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$)',
        'requirements': r'\*\*\{?requirements\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$)',
        'Delivery_time': r'\*\*\{?Delivery_time\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$)',
        'Delivery_Amount': r'\*\*\{?Delivery_Amount\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$)',
        'recommendations': r'\*\*\{?recommendations\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$|\Z)',
        'project_name': r'\*\*\{?project_name\}?\*\*:?\s*(.*?)(?=\n\n|\n\*\*\{?|$)'
    }
    
    # JSON字段提取模式
    JSON_FIELD_PATTERNS = {
        'project_name': r'"project_name"\s*:\s*"([^"]*)"',
        'overview': r'"overview"\s*:\s*"((?:\\"|[^"])*)"',
        'requirements': r'"requirements"\s*:\s*"(.*?)(?="[,}])',
        'Delivery_time': r'"Delivery_time"\s*:\s*"([^"]*)"',
        'Delivery_Amount': r'"Delivery_Amount"\s*:\s*"?(\d+)"?',
        'recommendations': r'"recommendations"\s*:\s*"(.*?)(?="[,}]|\Z)'
    }


class LLMResponseParser:
    """LLM响应解析器"""
    
    def __init__(self, logger):
        self.logger = logger
    
    def parse(self, content: str) -> Dict[str, Any]:
        """解析LLM响应内容"""
        # 尝试JSON解析
        json_result = self._try_json_parse(content)
        if json_result:
            return json_result
        
        # 回退到正则表达式解析
        self.logger.info("JSON解析失败，使用正则表达式解析")
        return self._regex_parse(content)
    
    def _try_json_parse(self, content: str) -> Optional[Dict[str, Any]]:
        """尝试JSON解析"""
        # 查找JSON代码块
        json_match = re.search(DocumentConstants.JSON_BLOCK_PATTERN, content)
        if json_match:
            return self._parse_json_string(json_match.group(1))
        
        # 查找JSON对象
        json_obj_match = re.search(DocumentConstants.JSON_OBJECT_PATTERN, content)
        if json_obj_match:
            return self._parse_json_string(json_obj_match.group(0))
        
        # 尝试解析整个内容
        return self._parse_json_string(content)
    
    def _parse_json_string(self, json_str: str) -> Optional[Dict[str, Any]]:
        """解析JSON字符串"""
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            # 尝试手动解析
            return self._manual_json_parse(json_str)
    
    def _manual_json_parse(self, json_str: str) -> Optional[Dict[str, Any]]:
        """手动解析JSON"""
        try:
            result = {}
            for field, pattern in DocumentConstants.JSON_FIELD_PATTERNS.items():
                match = re.search(pattern, json_str, re.DOTALL)
                if match:
                    value = match.group(1)
                    if field in ['requirements', 'recommendations']:
                        value = value.replace('\\n', '\n').replace('\\"', '"')
                    result[field] = value
            return result if result else None
        except Exception as e:
            self.logger.warning(f"手动JSON解析失败: {e}")
            return None
    
    def _regex_parse(self, content: str) -> Dict[str, Any]:
        """正则表达式解析"""
        result = {}
        for field, pattern in DocumentConstants.FIELD_PATTERNS.items():
            match = re.search(pattern, content, re.DOTALL)
            if match:
                result[field] = match.group(1).strip()
        return result


class DocumentTemplate:
    """文档模板管理器"""
    
    def __init__(self, logger):
        self.logger = logger
        self._template_cache = None
    
    def get_template(self) -> str:
        """获取文档模板"""
        if self._template_cache is None:
            self._template_cache = self._load_template()
        return self._template_cache
    
    def _load_template(self) -> str:
        """加载模板"""
        template_path = self._get_template_path()
        
        if os.path.exists(template_path):
            try:
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return self._extract_template_from_content(content)
            except Exception as e:
                self.logger.error(f"加载模板文件失败: {e}")
        
        self.logger.warning("使用默认模板")
        return self._get_default_template()
    
    def _get_template_path(self) -> str:
        """获取模板文件路径"""
        return os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'prompts',
            'document_template.md'
        )
    
    def _extract_template_from_content(self, content: str) -> str:
        """从内容中提取模板"""
        start_marker = "```template"
        end_marker = "```"
        
        start_pos = content.find(start_marker)
        if start_pos != -1:
            start_pos += len(start_marker)
            end_pos = content.find(end_marker, start_pos)
            if end_pos != -1:
                return content[start_pos:end_pos].strip()
        
        return self._get_default_template()
    
    def _get_default_template(self) -> str:
        """获取默认模板"""
        return """# {project_name} 需求确认文档

**文档日期**: {date}

## 1. 项目概述

{overview}

## 2. 需求描述

{requirements}

## 3. 预估的交付时间和金额

- **预估交付时间:** {Delivery_time}

- **预估交付金额:** {Delivery_Amount} 元

## 4. 项目建议

{recommendations}

---"""
    
    def apply(self, data: Dict[str, Any]) -> str:
        """应用模板"""
        try:
            template = self.get_template()
            return template.format(**data)
        except KeyError as e:
            self.logger.error(f"模板格式化错误: 缺少键 {e}")
            raise Exception(f"模板格式化错误: {e}")
        except Exception as e:
            self.logger.error(f"模板应用失败: {e}")
            raise Exception(f"模板应用失败: {e}")


class DocumentGenerator(AutoGenBaseAgent):
    """优化后的文档生成器"""
    
    def __init__(self, llm_client: Any = None, agent_name: str = "document_generator", db_manager: DatabaseManager = None):
        self.llm_client = llm_client
        self.agent_name = agent_name
        self.prompt_loader = PromptLoader()
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
        self.config = LLM_CONFIGS
        self.DEFAULT_MODEL = DEFAULT_MODEL
        
        # 初始化组件
        self.parser = LLMResponseParser(self.logger)
        self.template_manager = DocumentTemplate(self.logger)
        
        # 验证配置
        if self.DEFAULT_MODEL not in self.config:
            raise ValueError(f"默认模型 {self.DEFAULT_MODEL} 未在配置中定义")
    
    async def get_concern_points(self, conversation_id: str) -> List[Dict[str, Any]]:
        """获取指定会话的所有已完成关注点"""
        self.logger.info(f"查询会话 {conversation_id} 的已完成关注点")
        
        query = """
            SELECT focus_id, extracted_info 
            FROM concern_point_coverage 
            WHERE conversation_id = ? AND status = 'completed'
        """
        results = await self.db_manager.execute_query(query, (conversation_id,))
        self.logger.info(f"查询到 {len(results)} 个已完成的关注点")
        return results
    
    def format_requirements(self, concerns: List[Dict[str, Any]]) -> str:
        """格式化需求部分内容"""
        if not concerns:
            return DocumentConstants.DEFAULT_REQUIREMENTS
        
        return "\n".join(f"### {concern['extracted_info']}" for concern in concerns)
    
    async def generate_document(self, conversation_id: str, project_name: str) -> Optional[str]:
        """生成并保存需求文档"""
        try:
            # 获取关注点数据
            concerns = await self.get_concern_points(conversation_id)
            if not concerns:
                self.logger.warning(f"会话 {conversation_id} 没有已完成的需求关注点，将生成基础文档模板")
            
            # 准备用户信息
            user_info = self._prepare_user_info(project_name, concerns)
            
            # 调用LLM生成内容
            llm_response = await self._call_llm(user_info, conversation_id)
            if not llm_response:
                return None
            
            # 解析LLM响应
            parsed_result = self.parser.parse(llm_response.get('content', ''))
            self.logger.info(f"解析结果: {parsed_result}")
            
            # 准备模板数据
            template_data = self._prepare_template_data(parsed_result, project_name, concerns)
            
            # 生成文档
            document_content = self.template_manager.apply(template_data)
            
            # 保存到数据库
            doc_id = await self._save_document(conversation_id, document_content)
            return doc_id
            
        except Exception as e:
            self.logger.error(f"文档生成失败: {e}", exc_info=True)
            return None
    
    def _prepare_user_info(self, project_name: str, concerns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """准备用户信息"""
        return {
            "project_name": project_name,
            "date": datetime.now().strftime('%Y-%m-%d'),
            "requirements": self.format_requirements(concerns)
        }
    
    async def _call_llm(self, user_info: Dict[str, Any], conversation_id: str) -> Optional[Dict[str, Any]]:
        """调用LLM生成内容"""
        try:
            # 记录模型信息
            self._log_model_info()

            # 使用PromptLoader加载模版并替换占位符
            system_prompt = self.prompt_loader.load_prompt(
                "document_template",
                {"user_focused_info": json.dumps(user_info, ensure_ascii=False)}
            )

            # 调用LLM
            self.logger.info(f"使用agent_name={self.agent_name}调用LLM生成文档")
            response = await self.llm_client.call_llm(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"用户提供的信息:\n{json.dumps(user_info, ensure_ascii=False)}"}
                ],
                session_id=str(conversation_id),
                agent_name=self.agent_name
            )

            self.logger.info(f"LLM响应: {response.get('content', '')[:200]}...")
            return response

        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}", exc_info=True)
            return None
    
    def _log_model_info(self):
        """记录模型信息"""
        model_name = SCENARIO_LLM_MAPPING.get(self.agent_name, "")
        if model_name and model_name in LLM_CONFIGS:
            model_config = LLM_CONFIGS[model_name]
            self.logger.info(
                f"使用模型: {model_name}, 提供商: {model_config.get('provider')}, "
                f"模型名称: {model_config.get('model_name')}"
            )
        else:
            self.logger.warning(f"找不到模型配置: {self.agent_name} -> {model_name}")
    
    def _prepare_template_data(self, parsed_result: Dict[str, Any], project_name: str, concerns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """准备模板数据"""
        return {
            "project_name": parsed_result.get("project_name", project_name),
            "date": datetime.now().strftime('%Y-%m-%d'),
            "overview": parsed_result.get("overview", DocumentConstants.DEFAULT_OVERVIEW),
            "requirements": parsed_result.get("requirements", self.format_requirements(concerns)),
            "Delivery_time": parsed_result.get("Delivery_time", DocumentConstants.DEFAULT_DELIVERY_TIME),
            "Delivery_Amount": parsed_result.get("Delivery_Amount", DocumentConstants.DEFAULT_DELIVERY_AMOUNT),
            "recommendations": parsed_result.get("recommendations", DocumentConstants.DEFAULT_RECOMMENDATIONS)
        }
    

    
    async def _save_document(self, conversation_id: str, content: str) -> Optional[str]:
        """保存文档到数据库"""
        try:
            # 获取当前最大版本号
            query = """
                SELECT MAX(version) as max_version 
                FROM documents 
                WHERE conversation_id = ?
            """
            result = await self.db_manager.execute_query(query, (conversation_id,))
            new_version = 1 if not result or result[0]['max_version'] is None else result[0]['max_version'] + 1
            
            # 生成唯一文档ID
            doc_id = f"doc_{uuid.uuid4().hex}"
            
            # 插入新记录
            insert_query = """
                INSERT INTO documents 
                (document_id, conversation_id, content, status, version, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            """
            await self.db_manager.execute_update(
                insert_query,
                (doc_id, conversation_id, content, "draft", new_version)
            )
            
            self.logger.info(f"文档生成成功! 文档ID: {doc_id}, 版本: {new_version}")
            return doc_id
            
        except Exception as e:
            self.logger.error(f"保存文档失败: {e}", exc_info=True)
            return None
