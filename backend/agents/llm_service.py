"""
AutoGen兼容的LLM服务Agent
"""
import json
import logging
import time
import random
import os
from typing import Dict, Any, List, Optional, Union, AsyncGenerator, Tuple
import asyncio
from datetime import datetime

from .base import AutoGenBaseAgent
from ..config.settings import LLMConfig, CircuitBreakerConfig
from .llm_config_manager import LLMConfigManager
from ..utils.logging_config import get_logger
from backend.agents.llm_utils import (
    LLMError, LLMErrorType, classify_error, CircuitBreaker, ResponseCache
)
from backend.utils.performance_monitor import performance_monitor


class AutoGenLLMServiceAgent(AutoGenBaseAgent):
    """AutoGen兼容的LLM服务Agent，继承自AutoGenBaseAgent"""

    def __init__(self,
                 enable_cache: bool = True,
                 cache_size: int = 100,
                 cache_ttl: int = 3600,
                 enable_circuit_breaker: bool = True,
                 **kwargs):
        """初始化LLM服务Agent"""
        super().__init__(name="AutoGenLLMServiceAgent",
                         human_input_mode="NEVER",
                         llm_config=None,  # 禁用AutoGen的默认LLM配置
                         **kwargs)
        from .llm_config_manager import LLMConfigManager
        self.config_manager = LLMConfigManager()

        # 初始化客户端字典，用于存储不同提供商的客户端
        self.clients = {}
        self.client = None

        # 初始化默认客户端
        default_client = self._init_client(agent_name="llm_service")
        self.client = default_client

        # 初始化缓存和断路器
        self.enable_cache = enable_cache
        self.response_cache = ResponseCache(max_size=cache_size, ttl=cache_ttl) if enable_cache else None
        # 临时禁用断路器
        self.enable_circuit_breaker = False
        self.circuit_breaker = None

        # 初始化统计信息
        self.call_stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "cache_hits": 0,
            "circuit_breaks": 0,
            "total_latency": 0
        }

    def _init_client(self, agent_name: str = None, model_name: str = None):
        """初始化LLM客户端
        参数:
            agent_name: 可选，指定关联的Agent名称
            model_name: 可选，直接指定模型名称
        """
        # 获取模型配置(支持通过agent_name或model_name指定)
        model_config = self.config_manager.get_model_config(agent_name=agent_name, model_name=model_name)

        # 保存当前配置
        self.config = model_config

        # 根据提供商创建对应的客户端
        provider = model_config["provider"]

        # 如果已经有该提供商的客户端，直接返回
        if provider in self.clients:
            return self.clients[provider]

        # 否则创建新的客户端
        if provider == "deepseek":
            import aiohttp

            class DeepSeekClient:
                def __init__(self, api_key: str, api_base: str, timeout: int, max_retries: int):
                    self.api_key = api_key
                    self.api_base = api_base
                    self.timeout = timeout
                    self.max_retries = max_retries
                    # 初始化日志记录器
                    self.logger = get_logger("backend.agents.llm_service.DeepSeekClient")

                async def chat_completion(self, **kwargs):
                    headers = {
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    }
                    url = f"{self.api_base}/chat/completions"

                    # 记录API基础URL和完整URL
                    self.logger.debug(f"API Base URL: {self.api_base}")
                    self.logger.debug(f"Full URL: {url}")

                    # 构建符合DeepSeek API规范的请求体
                    request_body = {
                        "model": kwargs.get("model", "deepseek-chat"),  # 使用官方文档中的模型名称
                        "messages": [{
                            "role": msg.get("role", "user"),
                            "content": msg["content"]
                        } for msg in kwargs["messages"]],
                        "temperature": min(max(kwargs.get("temperature", 0.7), 0.0), 2.0),
                        "max_tokens": min(kwargs.get("max_tokens", 1024), 4096),
                        "top_p": min(max(kwargs.get("top_p", 1.0), 0.0), 1.0),
                        "stream": False
                    }

                    # 记录模型名称
                    self.logger.debug(f"Using model: {request_body['model']}")

                    # 添加调试日志
                    self.logger.debug(f"Sending request to DeepSeek API: {url}")
                    self.logger.debug(f"Request headers: {headers}")
                    self.logger.debug(f"Request body: {json.dumps(request_body, indent=2, ensure_ascii=False)}")

                    for attempt in range(self.max_retries + 1):
                        try:
                            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                                async with session.post(
                                    url,
                                    headers=headers,
                                    json=request_body
                                ) as response:
                                    response_text = await response.text()
                                    self.logger.debug(f"Response status: {response.status}")
                                    self.logger.debug(f"Response body: {response_text}")
                                    response.raise_for_status()
                                    return await response.json()
                        except aiohttp.ClientError as e:
                            self.logger.error(f"Request failed (attempt {attempt + 1}): {str(e)}")
                            if attempt < self.max_retries:
                                await asyncio.sleep(1 * (attempt + 1))
                    raise Exception(f"Max retries ({self.max_retries}) exceeded")

            client = DeepSeekClient(
                api_key=model_config["api_key"],
                api_base=model_config["api_base"],
                timeout=model_config.get("timeout", 30),
                max_retries=model_config.get("max_retries", 3)
            )
            self.clients[provider] = client

        elif provider == "openai":
            from openai import OpenAI
            client = OpenAI(
                api_key=model_config["api_key"],
                base_url=model_config["api_base"],
                timeout=model_config.get("timeout", 30),
                max_retries=model_config.get("max_retries", 3)
            )
            self.clients[provider] = client

        elif provider == "openrouter":
            import aiohttp

            class OpenRouterClient:
                def __init__(self, api_key: str, api_base: str, timeout: int, max_retries: int):
                    self.api_key = api_key
                    self.api_base = api_base
                    self.timeout = timeout
                    self.max_retries = max_retries
                    # 初始化日志记录器
                    self.logger = get_logger("backend.agents.llm_service.OpenRouterClient")

                async def chat_completion(self, **kwargs):
                    headers = {
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json",
                        "HTTP-Referer": "https://autogen.com",  # OpenRouter需要这个头
                        "X-Title": "AutoGen"  # OpenRouter需要这个头
                    }
                    url = f"{self.api_base}/chat/completions"

                    # 记录API基础URL和完整URL
                    self.logger.debug(f"API Base URL: {self.api_base}")
                    self.logger.debug(f"Full URL: {url}")

                    # 构建符合OpenRouter API规范的请求体
                    request_body = {
                        "model": kwargs.get("model", "google/gemini-2.0-flash-001"),
                        "messages": [{
                            "role": msg.get("role", "user"),
                            "content": msg["content"]
                        } for msg in kwargs["messages"]],
                        "temperature": min(max(kwargs.get("temperature", 0.7), 0.0), 2.0),
                        "max_tokens": min(kwargs.get("max_tokens", 1024), 4096),
                        "top_p": min(max(kwargs.get("top_p", 1.0), 0.0), 1.0),
                        "stream": False
                    }

                    # 记录模型名称
                    self.logger.debug(f"Using model: {request_body['model']}")

                    # 添加调试日志
                    self.logger.debug(f"Sending request to OpenRouter API: {url}")
                    self.logger.debug(f"Request headers: {headers}")
                    self.logger.debug(f"Request body: {json.dumps(request_body, indent=2, ensure_ascii=False)}")

                    for attempt in range(self.max_retries + 1):
                        try:
                            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                                async with session.post(
                                    url,
                                    headers=headers,
                                    json=request_body
                                ) as response:
                                    response_text = await response.text()
                                    self.logger.debug(f"Response status: {response.status}")
                                    self.logger.debug(f"Response body: {response_text}")
                                    response.raise_for_status()
                                    return await response.json()
                        except aiohttp.ClientError as e:
                            self.logger.error(f"Request failed (attempt {attempt + 1}): {str(e)}")
                            if attempt < self.max_retries:
                                await asyncio.sleep(1 * (attempt + 1))
                    raise Exception(f"Max retries ({self.max_retries}) exceeded")

            client = OpenRouterClient(
                api_key=model_config["api_key"],
                api_base=model_config["api_base"],
                timeout=model_config.get("timeout", 30),
                max_retries=model_config.get("max_retries", 3)
            )
            self.clients[provider] = client

        elif provider == "qwen":
            import aiohttp

            class QWenClient:
                def __init__(self, api_key: str, api_base: str, timeout: int, max_retries: int):
                    self.api_key = api_key
                    self.api_base = api_base
                    self.timeout = timeout
                    self.max_retries = max_retries
                    # 初始化日志记录器
                    self.logger = get_logger("backend.agents.llm_service.QWenClient")
                    # 记录初始化信息
                    self.logger.info(f"初始化QWenClient - API基础URL: {api_base}, 超时: {timeout}秒, 最大重试次数: {max_retries}")

                async def chat_completion(self, **kwargs):
                    headers = {
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    }
                    url = f"{self.api_base}/chat/completions"

                    # 记录API基础URL和完整URL
                    self.logger.info(f"QWen API调用 - URL: {url}, 模型: {kwargs.get('model', 'unknown')}")
                    self.logger.debug(f"API Headers: {headers}")

                    # 记录消息数量和第一条消息内容摘要
                    if "messages" in kwargs and kwargs["messages"]:
                        first_msg = kwargs["messages"][0]
                        content_preview = first_msg.get("content", "")[:50] + "..." if len(first_msg.get("content", "")) > 50 else first_msg.get("content", "")
                        self.logger.debug(f"消息数量: {len(kwargs['messages'])}, 首条消息: {first_msg.get('role', 'unknown')} - {content_preview}")

                    # 构建符合QWen API规范的请求体
                    request_body = {
                        "model": kwargs.get("model", "qwen-plus"),
                        "messages": [{
                            "role": msg.get("role", "user"),
                            "content": msg["content"]
                        } for msg in kwargs["messages"]],
                        "temperature": min(max(kwargs.get("temperature", 0.7), 0.0), 2.0),
                        "max_tokens": min(kwargs.get("max_tokens", 1024), 8000),
                        "top_p": min(max(kwargs.get("top_p", 1.0), 0.0), 1.0),
                        "stream": False
                    }
                    
                    # 记录请求体
                    self.logger.debug(f"请求体: {json.dumps(request_body, ensure_ascii=False)[:200]}...")

                    async with aiohttp.ClientSession() as session:
                        for attempt in range(self.max_retries):
                            try:
                                self.logger.debug(f"发送请求 (尝试 {attempt+1}/{self.max_retries})")
                                async with session.post(
                                    url,
                                    headers=headers,
                                    json=request_body,
                                    timeout=self.timeout
                                ) as response:
                                    if response.status != 200:
                                        error_text = await response.text()
                                        self.logger.error(f"请求失败 (尝试 {attempt+1}): {response.status}, 消息='{error_text}', URL='{url}'")
                                        if attempt == self.max_retries - 1:
                                            raise Exception(f"API请求失败: {response.status} {error_text}")
                                        await asyncio.sleep(1 * (attempt + 1))
                                        continue

                                    result = await response.json()
                                    self.logger.debug(f"响应状态: {response.status}")
                                    self.logger.debug(f"响应体: {json.dumps(result, ensure_ascii=False)[:500]}...")
                                    return result
                            except aiohttp.ClientError as e:
                                self.logger.error(f"请求错误 (尝试 {attempt+1}): {str(e)}")
                                if attempt == self.max_retries - 1:
                                    raise
                                await asyncio.sleep(1 * (attempt + 1))

                        raise Exception("所有重试都失败了")

            client = QWenClient(
                api_key=model_config["api_key"],
                api_base=model_config["api_base"],
                timeout=model_config.get("timeout", 30),
                max_retries=model_config.get("max_retries", 3)
            )
            self.clients[provider] = client

        elif provider == "qwen-intent":
            import aiohttp

            class QWenIntentClient:
                def __init__(self, api_key: str, api_base: str, timeout: int, max_retries: int):
                    self.api_key = api_key
                    self.api_base = api_base
                    self.timeout = timeout
                    self.max_retries = max_retries
                    # 初始化日志记录器
                    self.logger = get_logger("backend.agents.llm_service.QWenIntentClient")

                async def chat_completion(self, **kwargs):
                    headers = {
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    }
                    
                    # 意图识别API的专用端点
                    url = f"{self.api_base}/services/aigc/text-intent/detection"

                    # 记录API基础URL和完整URL
                    self.logger.debug(f"API Base URL: {self.api_base}")
                    self.logger.debug(f"Full URL: {url}")

                    # 提取用户消息内容
                    user_message = ""
                    for msg in kwargs["messages"]:
                        if msg.get("role") == "user":
                            user_message = msg["content"]
                            break
                    
                    if not user_message:
                        raise ValueError("未找到用户消息")

                    # 构建符合通义千问意图识别API的请求体
                    request_body = {
                        "model": kwargs.get("model", "tongyi-intent-detect-v3"),
                        "input": {
                            "text": user_message
                        },
                        "parameters": {
                            "result_type": "json"
                        }
                    }

                    self.logger.debug(f"请求体: {json.dumps(request_body, ensure_ascii=False)}")

                    async with aiohttp.ClientSession() as session:
                        for attempt in range(self.max_retries):
                            try:
                                async with session.post(
                                    url,
                                    headers=headers,
                                    json=request_body,
                                    timeout=self.timeout
                                ) as response:
                                    if response.status != 200:
                                        error_text = await response.text()
                                        self.logger.error(f"Request failed (attempt {attempt+1}): {response.status}, message='{error_text}', url='{url}'")
                                        if attempt == self.max_retries - 1:
                                            raise Exception(f"API请求失败: {response.status} {error_text}")
                                        await asyncio.sleep(1 * (attempt + 1))
                                        continue

                                    result = await response.json()
                                    
                                    # 将意图识别结果转换为标准格式
                                    intent_result = result.get("output", {}).get("intents", [])
                                    
                                    # 构建符合标准格式的响应
                                    formatted_response = {
                                        "choices": [
                                            {
                                                "message": {
                                                    "role": "assistant",
                                                    "content": json.dumps(intent_result, ensure_ascii=False)
                                                }
                                            }
                                        ],
                                        "usage": {
                                            "prompt_tokens": len(user_message) // 4,  # 粗略估计
                                            "completion_tokens": len(json.dumps(intent_result)) // 4,  # 粗略估计
                                            "total_tokens": len(user_message) // 4 + len(json.dumps(intent_result)) // 4
                                        }
                                    }
                                    
                                    return formatted_response
                            except aiohttp.ClientError as e:
                                self.logger.error(f"Request error (attempt {attempt+1}): {str(e)}")
                                if attempt == self.max_retries - 1:
                                    raise
                                await asyncio.sleep(1 * (attempt + 1))

                    raise Exception("所有重试都失败了")

            client = QWenIntentClient(
                api_key=model_config["api_key"],
                api_base=model_config["api_base"],
                timeout=model_config.get("timeout", 30),
                max_retries=model_config.get("max_retries", 3)
            )
            self.clients[provider] = client

        elif provider == "doubao":
            import aiohttp

            class DoubaoClient:
                def __init__(self, api_key: str, api_base: str, timeout: int, max_retries: int):
                    self.api_key = api_key
                    self.api_base = api_base
                    self.timeout = timeout
                    self.max_retries = max_retries
                    # 初始化日志记录器
                    self.logger = get_logger("backend.agents.llm_service.DoubaoClient")

                async def chat_completion(self, **kwargs):
                    headers = {
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    }
                    url = f"{self.api_base}/chat/completions"

                    # 记录API基础URL和完整URL
                    self.logger.debug(f"API Base URL: {self.api_base}")
                    self.logger.debug(f"Full URL: {url}")

                    # 构建符合豆包API规范的请求体
                    request_body = {
                        "model": kwargs.get("model", "doubao-1-5-thinking-pro-250415"),
                        "messages": [{
                            "role": msg.get("role", "user"),
                            "content": msg["content"]
                        } for msg in kwargs["messages"]],
                        "temperature": min(max(kwargs.get("temperature", 0.7), 0.0), 2.0),
                        "max_tokens": min(kwargs.get("max_tokens", 1024), 4096),
                        "top_p": min(max(kwargs.get("top_p", 1.0), 0.0), 1.0),
                        "stream": False
                    }

                    # 记录模型名称
                    self.logger.debug(f"Using model: {request_body['model']}")

                    # 添加调试日志
                    self.logger.debug(f"Sending request to Doubao API: {url}")
                    self.logger.debug(f"Request headers: {headers}")
                    self.logger.debug(f"Request body: {json.dumps(request_body, indent=2, ensure_ascii=False)}")

                    for attempt in range(self.max_retries + 1):
                        try:
                            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                                async with session.post(
                                    url,
                                    headers=headers,
                                    json=request_body
                                ) as response:
                                    response_text = await response.text()
                                    self.logger.debug(f"Response status: {response.status}")
                                    self.logger.debug(f"Response body: {response_text}")
                                    response.raise_for_status()
                                    return await response.json()
                        except aiohttp.ClientError as e:
                            self.logger.error(f"Request failed (attempt {attempt + 1}): {str(e)}")
                            if attempt < self.max_retries:
                                await asyncio.sleep(1 * (attempt + 1))
                    raise Exception(f"Max retries ({self.max_retries}) exceeded")

            client = DoubaoClient(
                api_key=model_config["api_key"],
                api_base=model_config["api_base"],
                timeout=model_config.get("timeout", 30),
                max_retries=model_config.get("max_retries", 3)
            )
            self.clients[provider] = client

        else:
            raise ValueError(f"不支持的LLM提供商: {provider}")

        # 设置默认客户端
        if agent_name == "llm_service" or not self.client:
            self.client = self.clients[provider]

        return self.clients[provider]

    async def _call_llm_with_retry(self, client: Any, params: Dict[str, Any]) -> Dict[str, Any]:
        """带重试机制的LLM调用
        参数:
            client: LLM客户端实例
            params: 调用参数
        返回:
            LLM响应字典
        """
        max_retries = params.get("max_retries", 3)
        retry_delay = params.get("retry_delay", 1)

        for attempt in range(max_retries + 1):
            try:
                if hasattr(client, "chat_completion"):
                    response = await client.chat_completion(**params)
                else:
                    response = await client.chat.completions.create(**params)

                # 处理DeepSeek/OpenAI不同格式的响应
                if isinstance(response, dict):  # DeepSeek格式
                    return {
                        "content": response["choices"][0]["message"]["content"],
                        "usage": response.get("usage"),
                        "model": response.get("model", "unknown")
                    }
                elif hasattr(response, "choices"):  # OpenAI格式
                    return {
                        "content": response.choices[0].message.content,
                        "usage": response.usage.dict() if hasattr(response, "usage") else None,
                        "model": getattr(response, "model", "unknown")
                    }
                else:
                    return response

            except Exception as e:
                if attempt == max_retries:
                    raise
                await asyncio.sleep(retry_delay * (attempt + 1))

        raise Exception("LLM调用失败")

    async def call_llm(self,
                       messages: List[Dict[str, str]],
                       model_name: str = None,
                       agent_name: str = None,
                       session_id: str = None,
                       scenario: str = None,
                       **kwargs) -> Dict[str, Any]:
        """调用LLM API
        参数:
            messages: 消息列表
            model_name: 直接指定模型名称(可选)
            agent_name: 通过Agent名称获取配置(可选)
            session_id: 会话ID(可选)，用于日志记录
            scenario: 场景描述(可选)，用于日志记录
        """
        # 获取带会话ID的日志记录器
        stage = scenario or agent_name or "llm_call"
        logger = get_logger(
            f"{self.__class__.__module__}.{self.__class__.__name__}",
            session_id=session_id,
            stage=stage
        )

        # 先获取实际要使用的模型配置
        model_config = self.config_manager.get_model_config(
            model_name=model_name,
            agent_name=agent_name
        )

        # 记录调用开始，使用实际的模型名称
        logger.info(f"LLM调用开始 - 模型: {model_config['model_name']}, 场景: {scenario or agent_name or '未指定'}")
        logger.info(f"输入消息: {json.dumps(messages, ensure_ascii=False)}")
        self.call_stats["total_calls"] += 1
        start_time = time.time()

        # 使用性能监控器跟踪LLM调用
        with performance_monitor.track_llm_call(
            provider=model_config["provider"],
            model=model_config["model_name"],
            operation=scenario or agent_name or "chat_completion"
        ):
            try:
                # 为意图识别场景设置专用参数
                intent_params = {
                    "temperature": 0.3,
                    "max_tokens": 300,
                    "stop": ["```", "###", "---", "\n\n"]
                } if scenario == "intent_recognition" else {}

                # 设置合理的超时时间，防止长时间等待
                timeout = kwargs.get("timeout", model_config.get("timeout", 30))
                if timeout > 60:  # 限制最大超时时间为60秒
                    timeout = 60
                    logger.warning(f"超时时间过长，已限制为60秒")

                call_params = {
                    "messages": messages,
                    "model": model_config["model_name"],
                    "timeout": timeout,
                    **model_config,
                    **intent_params,
                    **kwargs
                }

                # 记录实际调用参数
                logger.info(
                    f"调用参数: 模型={call_params['model']}, 温度={call_params.get('temperature', 0.7)}, max_tokens={call_params.get('max_tokens')}")

                # 获取或初始化对应的客户端
                provider = model_config["provider"]
                if provider not in self.clients:
                    client = self._init_client(model_name=model_name, agent_name=agent_name)
                else:
                    client = self.clients[provider]

                response = await self._call_llm_with_retry(client, call_params)

                # 记录调用结果
                elapsed = time.time() - start_time
                logger.info(f"LLM调用成功 - 耗时: {elapsed:.2f}秒")
                logger.info(f"输出内容: {json.dumps(response['content'], ensure_ascii=False)}")

                if 'usage' in response and response['usage']:
                    logger.info(f"Token使用情况: {json.dumps(response['usage'], ensure_ascii=False)}")

                # 添加耗时和token使用情况到响应中
                response["duration"] = elapsed
                response["token_usage"] = response.get("usage", {})

                self.call_stats["successful_calls"] += 1
                self.call_stats["total_latency"] += elapsed
                return response

            except Exception as e:
                error = classify_error(e, model_config["provider"])
                elapsed = time.time() - start_time

                # 记录错误信息
                logger.error(f"LLM调用失败 - 耗时: {elapsed:.2f}秒")
                logger.error(f"错误类型: {error.error_type}, 错误信息: {str(error)}")

                self.call_stats["failed_calls"] += 1
                self.call_stats["total_latency"] += elapsed
                raise error

    # 保留其他原有方法不变...
