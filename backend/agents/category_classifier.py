"""类别分类器Agent"""
import json
import logging
import re  # 添加正则表达式模块
from typing import List, Dict, Any, Optional

# 修复导入路径
from backend.agents.base import AutoGenBaseAgent
# 或者使用
# from backend.agents.base import Agent  # 如果Agent类在agents目录下

from backend.utils.prompt_loader import PromptLoader

# 在确定领域后,进一步细分类用户的具体需求类别
class CategoryClassifierAgent(AutoGenBaseAgent):
    """
    根据用户输入和给定的类别列表，使用LLM智能识别用户需求所属的具体类别
      """
    
   # 初始化类别分类器
    def __init__(self, llm_client: Any, agent_name: str = "category_classifier"):
        """
        Args:
            llm_client: LLM客户端，用于调用大语言模型
            agent_name: Agent名称，默认为"category_classifier"
        """
        super().__init__(name="CategoryClassifier")
        self.llm_client = llm_client
        self.agent_name = agent_name
        self.prompt_loader = PromptLoader()  # 加载提示词模板
        self.logger = logging.getLogger(__name__)
    
    # 基于类别描述和对话历史分类文本
    async def classify(self, text: str, categories: List[Dict[str, Any]], conversation_history: List[Dict[str, str]] = None) -> Dict[str, Any]:
        """对文本进行类别分类

        参数:
            text: 用户输入文本
            categories: 可用类别列表
            conversation_history: 对话历史记录

        返回:
            Dict[str, Any]: 分类结果
        """
        self.logger.info(f"开始类别分类，文本: '{text}'")

        # 获取当前使用的模型信息
        model_config = self.llm_client.config_manager.get_model_config(agent_name=self.agent_name)
        self.logger.info(f"类别分类使用模型: {model_config.get('model_name', 'unknown')}, 提供商: {model_config.get('provider', 'unknown')}")

        # 使用传入的类别列表
        available_categories = categories
        
        # 加载提示词
        try:
            prompt = self.prompt_loader.load_prompt(
                "category_classifier",
                {
                    "user_input": text,
                    "conversation_context": self._format_conversation_history(conversation_history) if conversation_history else "",
                    "categories_section": self._format_available_categories(available_categories),
                    "categories_list": ", ".join([f"{c.get('name', '')} ({c.get('category_id', '')})" for c in available_categories if c.get('name')])
                }
            )
        except Exception as e:
            # 如果加载失败，使用硬编码的备用模板
            self.logger.warning(f"从文件加载Prompt失败 ('{e}'), 将使用硬编码的备用提示词。")
            # 硬编码的备用模板...
        
        # 调用LLM
        self.logger.debug(f"发送分类请求到LLM，提示词长度: {len(prompt)}")
        response = await self.llm_client.call_llm(
            messages=[{"role": "user", "content": prompt}],
            agent_name=self.agent_name,
            scenario="category_classification"
        )
        
        # 记录模型响应信息
        self.logger.info(
            f"LLM响应 - 模型: {response.get('model', 'unknown')}, "
            f"耗时: {response.get('duration', 0):.2f}s, "
            f"Tokens: {response.get('token_usage', {}).get('total_tokens', 'N/A')}"
        )
        
        # 解析响应
        content = response.get("content", "")
        self.logger.info(f"类别分类结果: {content}")
        
        # 解析JSON - 首先尝试提取代码块中的JSON
        try:
            # 先尝试直接解析（如果响应就是纯JSON）
            result = json.loads(content)
            return result
        except json.JSONDecodeError:
            # 如果直接解析失败，尝试从代码块中提取JSON
            self.logger.debug("直接JSON解析失败，尝试从代码块中提取JSON")
            result = self._extract_json(content)
            if result:
                return result

            # 如果JSON提取也失败，记录错误并返回默认值
            self.logger.error(f"无法解析类别分类结果: {content}")
            # 返回默认值
            return {
                "category_id": None,
                "category_name": "未分类",
                "confidence": 0.0,
                "reasoning": "无法解析分类结果",
                "status": "failed"
            }
    
    def _extract_json(self, text: str) -> Dict[str, Any]:
        """
        从LLM响应文本中提取JSON格式的分类结果
        
        支持从代码块和普通文本中提取JSON内容
        """
        try:
            # 方法1：查找Markdown代码块中的JSON
            json_pattern = r'```(?:json)?\s*({.*?})\s*```'
            json_match = re.search(json_pattern, text, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(1)
                self.logger.debug(f"从代码块提取的JSON: {json_str}")
                return json.loads(json_str)
            
            # 方法2：查找普通文本中花括号包围的JSON
            json_match = re.search(r'({[\s\S]*?})', text)
            if json_match:
                json_str = json_match.group(1)
                self.logger.debug(f"从文本提取的JSON: {json_str}")
                return json.loads(json_str)
            
            # 如果无法找到JSON格式内容，记录错误
            self.logger.error(f"无法从文本中提取JSON: {text}")
            return {}
        except Exception as e:
            self.logger.error(f"JSON解析失败: {str(e)}, 文本: {text}")
            return {}

    # 匹配类别名称到数据库中的类别
    def _match_category_from_text(self, text: str, categories: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        当JSON解析失败时的备用方案，通过文本匹配找到对应的类别
        支持精确匹配和模糊匹配两种策略
        """
        # 输入验证
        if not text or not categories:
            self.logger.warning("文本或类别列表为空，无法匹配")
            return None
        
        # 创建类别名称到类别对象的映射（不区分大小写，便于匹配）
        category_name_to_obj = {}
        for c in categories:
            if c.get('name'):
                category_name_to_obj[c.get('name').lower()] = c
        
        self.logger.debug(f"可用类别: {list(category_name_to_obj.keys())}")
        
        try:
            # 策略1：从JSON响应中提取类别名称
            result = self._extract_json(text)
            category_name = result.get('category_id')
            
            if category_name and isinstance(category_name, str):
                self.logger.debug(f"从JSON提取的类别名称: {category_name}")
                category_name = category_name.lower()
                
                # 精确匹配：完全相同的类别名称
                if category_name in category_name_to_obj:
                    matched = category_name_to_obj[category_name]
                    self.logger.info(f"精确匹配到类别: {category_name} -> {matched.get('category_id')}")
                    return matched
                
                # 模糊匹配：部分包含关系
                for db_name, obj in category_name_to_obj.items():
                    if category_name in db_name or db_name in category_name:
                        self.logger.info(f"模糊匹配到类别: {category_name} ~= {db_name} -> {obj.get('category_id')}")
                        return obj
            
            # 策略2：更精确的文本匹配（使用完整词匹配）
            text_lower = text.lower()
            for category_name, obj in category_name_to_obj.items():
                # 使用正则表达式进行完整词匹配，避免部分字符串匹配导致的误判
                
                # 创建词边界匹配模式，确保匹配完整的类别名称
                pattern = r'\b' + re.escape(category_name) + r'\b'
                self.logger.debug(f"尝试匹配模式: '{pattern}' 在文本: '{text_lower}'")
                if re.search(pattern, text_lower):
                    self.logger.info(f"从文本中精确匹配到类别: {category_name} -> {obj.get('category_id')}")
                    return obj
                # 额外尝试：如果类别名称包含特殊字符，也尝试简单的包含匹配
                elif category_name in text_lower and len(category_name) > 2:
                    # 只对长度大于2的类别名称进行包含匹配，避免误匹配短词
                    self.logger.info(f"从文本中包含匹配到类别: {category_name} -> {obj.get('category_id')}")
                    return obj
            
            # 所有匹配策略都失败
            self.logger.warning(f"无法匹配任何类别，文本: {text}")
            return None
        except Exception as e:
            self.logger.error(f"类别匹配失败: {str(e)}")
            return None
        
    # 获取兜底的"其他"类别
    def _get_other_category_by_domain(self, categories: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        根据领域获取对应的"其他"类别
        在无法匹配到具体类别时，返回一个兜底的"其他"类别
        搜索包含"其他"、"other"或"未分类"等关键词的类别
        
        Args:
            categories: 类别列表
            
        Returns:
            Dict: 找到的"其他"类别，如果没有找到则返回None
        """
        if not categories:
            return None
            
        # 搜索关键词列表
        other_keywords = ["其他", "other", "未分类", "其它", "杂项", "miscellaneous"]
        
        # 遍历所有类别，查找包含关键词的类别
        for category in categories:
            name = category.get('name', '').lower()
            for keyword in other_keywords:
                if keyword.lower() in name:
                    self.logger.info(f"找到兜底类别: {name} (ID: {category.get('category_id')})")
                    return category
        
        # 如果没有找到包含关键词的类别，返回列表中的最后一个类别作为兜底
        # 这是基于一种常见的设计模式：将"其他"类别放在列表的最后
        if categories:
            last_category = categories[-1]
            self.logger.info(f"使用最后一个类别作为兜底: {last_category.get('name')} (ID: {last_category.get('category_id')})")
            return last_category
            
        return None
        
    def _format_conversation_history(self, history: List[Dict[str, str]]) -> str:
        """
        格式化对话历史为字符串格式
        
        将对话历史转换为适合LLM理解的文本格式
        """
        if not history:
            return ""
            
        # 只取最近的5条消息，避免上下文过长
        recent_history = history[-5:] if len(history) > 5 else history
        
        formatted_history = "\n对话历史:\n"
        for idx, msg in enumerate(recent_history, 1):
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            formatted_history += f"{idx}. {'用户' if role == 'user' else '系统'}: {content}\n"
            
        return formatted_history

    def _format_available_categories(self, categories: List[Dict[str, Any]]) -> str:
        """
        格式化可用类别列表为字符串格式

        将类别列表转换为适合LLM理解的文本格式
        """
        if not categories:
            return "无可用类别"

        formatted_categories = "\n可用类别:\n"
        for idx, category in enumerate(categories, 1):
            category_id = category.get('category_id', 'unknown')
            name = category.get('name', 'unknown')
            description = category.get('description', '无描述')
            formatted_categories += f"{idx}. {name} ({category_id}): {description}\n"

        return formatted_categories
