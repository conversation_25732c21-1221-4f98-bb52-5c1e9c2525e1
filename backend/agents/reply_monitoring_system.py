# -*- coding: utf-8 -*-
"""
消息回复监控和分析系统
版本: v1.0
作者: AI Assistant
创建时间: 2025-06-20

功能:
1. 实时监控消息回复性能和质量
2. 用户满意度跟踪和分析
3. 回复效果分析和趋势预测
4. 异常检测和告警机制
5. 多维度数据分析和可视化
"""

import logging
import json
from typing import Dict, Any, Optional, List
from enum import Enum
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, deque
import statistics
from backend.data.db.database_manager import DatabaseManager
from backend.utils.performance_monitor import performance_monitor


class SatisfactionLevel(Enum):
    """用户满意度等级"""
    VERY_DISSATISFIED = 1
    DISSATISFIED = 2
    NEUTRAL = 3
    SATISFIED = 4
    VERY_SATISFIED = 5


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class MetricType(Enum):
    """指标类型"""
    RESPONSE_TIME = "response_time"
    SUCCESS_RATE = "success_rate"
    SATISFACTION = "satisfaction"
    ERROR_RATE = "error_rate"
    FALLBACK_RATE = "fallback_rate"
    QUALITY_SCORE = "quality_score"


@dataclass
class ReplyMetric:
    """回复指标数据类"""
    session_id: str
    user_id: str
    reply_key: str
    reply_type: str
    content: str
    response_time: float
    success: bool
    satisfaction_score: Optional[int]
    quality_score: float
    timestamp: datetime
    context: Dict[str, Any]
    error_message: Optional[str] = None
    fallback_used: bool = False
    llm_model: Optional[str] = None
    template_version: Optional[str] = None


@dataclass
class AlertRule:
    """告警规则数据类"""
    rule_id: str
    name: str
    metric_type: MetricType
    threshold: float
    comparison: str  # >, <, >=, <=, ==
    time_window: int  # 时间窗口（分钟）
    alert_level: AlertLevel
    enabled: bool = True
    description: str = ""


@dataclass
class Alert:
    """告警数据类"""
    alert_id: str
    rule_id: str
    metric_type: MetricType
    current_value: float
    threshold: float
    alert_level: AlertLevel
    message: str
    timestamp: datetime
    resolved: bool = False
    resolved_at: Optional[datetime] = None


class ReplyMonitoringSystem:
    """消息回复监控和分析系统"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """
        初始化监控系统
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager or DatabaseManager("data/reply_monitoring.db")
        
        # 实时指标缓存
        self._metrics_cache = deque(maxlen=10000)  # 保留最近10000条记录
        self._satisfaction_cache = deque(maxlen=1000)  # 满意度缓存
        self._response_time_cache = deque(maxlen=1000)  # 响应时间缓存
        
        # 告警规则和状态
        self._alert_rules = {}
        self._active_alerts = {}
        self._alert_history = deque(maxlen=1000)
        
        # 统计数据
        self._stats = {
            "total_replies": 0,
            "successful_replies": 0,
            "failed_replies": 0,
            "fallback_replies": 0,
            "average_response_time": 0.0,
            "average_satisfaction": 0.0,
            "average_quality_score": 0.0,
            "hourly_stats": defaultdict(lambda: {
                "count": 0, "success": 0, "avg_time": 0.0, "avg_satisfaction": 0.0
            }),
            "daily_stats": defaultdict(lambda: {
                "count": 0, "success": 0, "avg_time": 0.0, "avg_satisfaction": 0.0
            })
        }
        
        # 初始化默认告警规则
        self._initialize_default_alert_rules()
        
        self.logger.info("回复监控系统初始化完成")
    
    async def initialize_database(self):
        """异步初始化数据库表"""
        await self._initialize_database()
    
    async def _initialize_database(self):
        """初始化数据库表"""
        try:
            queries = [
                # 回复指标表
                ("""
                    CREATE TABLE IF NOT EXISTS reply_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        reply_key TEXT NOT NULL,
                        reply_type TEXT NOT NULL,
                        content TEXT NOT NULL,
                        response_time REAL NOT NULL,
                        success BOOLEAN NOT NULL,
                        satisfaction_score INTEGER,
                        quality_score REAL NOT NULL,
                        timestamp TIMESTAMP NOT NULL,
                        context TEXT,
                        error_message TEXT,
                        fallback_used BOOLEAN DEFAULT FALSE,
                        llm_model TEXT,
                        template_version TEXT
                    )
                """, None),
                
                # 告警规则表
                ("""
                    CREATE TABLE IF NOT EXISTS alert_rules (
                        rule_id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        metric_type TEXT NOT NULL,
                        threshold REAL NOT NULL,
                        comparison TEXT NOT NULL,
                        time_window INTEGER NOT NULL,
                        alert_level TEXT NOT NULL,
                        enabled BOOLEAN DEFAULT TRUE,
                        description TEXT,
                        created_at TIMESTAMP NOT NULL,
                        updated_at TIMESTAMP NOT NULL
                    )
                """, None),
                
                # 告警历史表
                ("""
                    CREATE TABLE IF NOT EXISTS alert_history (
                        alert_id TEXT PRIMARY KEY,
                        rule_id TEXT NOT NULL,
                        metric_type TEXT NOT NULL,
                        current_value REAL NOT NULL,
                        threshold REAL NOT NULL,
                        alert_level TEXT NOT NULL,
                        message TEXT NOT NULL,
                        timestamp TIMESTAMP NOT NULL,
                        resolved BOOLEAN DEFAULT FALSE,
                        resolved_at TIMESTAMP
                    )
                """, None),
                
                # 用户满意度反馈表
                ("""
                    CREATE TABLE IF NOT EXISTS satisfaction_feedback (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        reply_key TEXT NOT NULL,
                        satisfaction_score INTEGER NOT NULL,
                        feedback_text TEXT,
                        timestamp TIMESTAMP NOT NULL,
                        context TEXT
                    )
                """, None),
                
                # 性能趋势表
                ("""
                    CREATE TABLE IF NOT EXISTS performance_trends (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date DATE NOT NULL,
                        hour INTEGER NOT NULL,
                        metric_type TEXT NOT NULL,
                        value REAL NOT NULL,
                        count INTEGER NOT NULL,
                        created_at TIMESTAMP NOT NULL,
                        UNIQUE(date, hour, metric_type)
                    )
                """, None)
            ]
            
            success = await self.db_manager.execute_transaction(queries)
            
            if success:
                self.logger.info("回复监控数据库表初始化完成")
            else:
                raise Exception("数据库表创建事务失败")
                
        except Exception as e:
            self.logger.error(f"初始化数据库表失败: {e}", exc_info=True)
            raise
    
    def _initialize_default_alert_rules(self):
        """初始化默认告警规则"""
        default_rules = [
            AlertRule(
                rule_id="response_time_high",
                name="响应时间过高",
                metric_type=MetricType.RESPONSE_TIME,
                threshold=5.0,  # 5秒
                comparison=">",
                time_window=5,  # 5分钟
                alert_level=AlertLevel.WARNING,
                description="平均响应时间超过5秒"
            ),
            AlertRule(
                rule_id="success_rate_low",
                name="成功率过低",
                metric_type=MetricType.SUCCESS_RATE,
                threshold=0.9,  # 90%
                comparison="<",
                time_window=10,  # 10分钟
                alert_level=AlertLevel.ERROR,
                description="成功率低于90%"
            ),
            AlertRule(
                rule_id="satisfaction_low",
                name="用户满意度过低",
                metric_type=MetricType.SATISFACTION,
                threshold=3.0,  # 3分
                comparison="<",
                time_window=15,  # 15分钟
                alert_level=AlertLevel.WARNING,
                description="平均用户满意度低于3分"
            ),
            AlertRule(
                rule_id="error_rate_high",
                name="错误率过高",
                metric_type=MetricType.ERROR_RATE,
                threshold=0.1,  # 10%
                comparison=">",
                time_window=5,  # 5分钟
                alert_level=AlertLevel.CRITICAL,
                description="错误率超过10%"
            ),
            AlertRule(
                rule_id="fallback_rate_high",
                name="回退率过高",
                metric_type=MetricType.FALLBACK_RATE,
                threshold=0.2,  # 20%
                comparison=">",
                time_window=10,  # 10分钟
                alert_level=AlertLevel.WARNING,
                description="回退率超过20%"
            )
        ]
        
        for rule in default_rules:
            self._alert_rules[rule.rule_id] = rule
        
        self.logger.info(f"已初始化 {len(default_rules)} 个默认告警规则")
    
    async def record_reply_metric(self, metric: ReplyMetric):
        """
        记录回复指标
        
        Args:
            metric: 回复指标数据
        """
        try:
            # 添加到缓存
            self._metrics_cache.append(metric)
            
            # 更新统计数据
            self._update_stats(metric)
            
            # 保存到数据库
            await self._save_metric_to_db(metric)
            
            # 检查告警
            await self._check_alerts(metric)
            
            # 记录性能监控
            performance_monitor.track_api_call("reply_monitoring")
            
        except Exception as e:
            self.logger.error(f"记录回复指标失败: {e}", exc_info=True)
    
    async def record_satisfaction_feedback(
        self,
        session_id: str,
        user_id: str,
        reply_key: str,
        satisfaction_score: int,
        feedback_text: str = None,
        context: Dict[str, Any] = None
    ):
        """
        记录用户满意度反馈
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            reply_key: 回复键
            satisfaction_score: 满意度评分（1-5）
            feedback_text: 反馈文本
            context: 上下文信息
        """
        try:
            # 验证满意度评分
            if not 1 <= satisfaction_score <= 5:
                raise ValueError("满意度评分必须在1-5之间")
            
            # 添加到缓存
            self._satisfaction_cache.append({
                "score": satisfaction_score,
                "timestamp": datetime.now(),
                "session_id": session_id,
                "reply_key": reply_key
            })
            
            # 保存到数据库
            query = """
                INSERT INTO satisfaction_feedback (
                    session_id, user_id, reply_key, satisfaction_score,
                    feedback_text, timestamp, context
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                session_id, user_id, reply_key, satisfaction_score,
                feedback_text, datetime.now().isoformat(),
                json.dumps(context) if context else None
            )
            
            await self.db_manager.execute_update(query, params)
            
            self.logger.info(f"记录用户满意度反馈: {session_id} - {satisfaction_score}分")
            
        except Exception as e:
            self.logger.error(f"记录满意度反馈失败: {e}", exc_info=True)
    
    def _update_stats(self, metric: ReplyMetric):
        """更新统计数据"""
        self._stats["total_replies"] += 1
        
        if metric.success:
            self._stats["successful_replies"] += 1
        else:
            self._stats["failed_replies"] += 1
        
        if metric.fallback_used:
            self._stats["fallback_replies"] += 1
        
        # 更新平均响应时间
        total = self._stats["total_replies"]
        current_avg = self._stats["average_response_time"]
        self._stats["average_response_time"] = (
            (current_avg * (total - 1) + metric.response_time) / total
        )
        
        # 更新平均质量分数
        current_quality_avg = self._stats["average_quality_score"]
        self._stats["average_quality_score"] = (
            (current_quality_avg * (total - 1) + metric.quality_score) / total
        )
        
        # 更新满意度（如果有）
        if metric.satisfaction_score:
            current_satisfaction_avg = self._stats["average_satisfaction"]
            satisfaction_count = len([m for m in self._metrics_cache if m.satisfaction_score])
            if satisfaction_count > 0:
                self._stats["average_satisfaction"] = (
                    (current_satisfaction_avg * (satisfaction_count - 1) + metric.satisfaction_score) / satisfaction_count
                )
        
        # 更新小时和日期统计
        hour_key = metric.timestamp.strftime("%Y-%m-%d-%H")
        day_key = metric.timestamp.strftime("%Y-%m-%d")
        
        # 小时统计
        hour_stats = self._stats["hourly_stats"][hour_key]
        hour_stats["count"] += 1
        if metric.success:
            hour_stats["success"] += 1
        
        # 更新小时平均值
        hour_count = hour_stats["count"]
        hour_stats["avg_time"] = (
            (hour_stats["avg_time"] * (hour_count - 1) + metric.response_time) / hour_count
        )
        
        if metric.satisfaction_score:
            hour_stats["avg_satisfaction"] = (
                (hour_stats["avg_satisfaction"] * (hour_count - 1) + metric.satisfaction_score) / hour_count
            )
        
        # 日期统计（类似逻辑）
        day_stats = self._stats["daily_stats"][day_key]
        day_stats["count"] += 1
        if metric.success:
            day_stats["success"] += 1
        
        day_count = day_stats["count"]
        day_stats["avg_time"] = (
            (day_stats["avg_time"] * (day_count - 1) + metric.response_time) / day_count
        )
        
        if metric.satisfaction_score:
            day_stats["avg_satisfaction"] = (
                (day_stats["avg_satisfaction"] * (day_count - 1) + metric.satisfaction_score) / day_count
            )

    async def _save_metric_to_db(self, metric: ReplyMetric):
        """保存指标到数据库"""
        query = """
            INSERT INTO reply_metrics (
                session_id, user_id, reply_key, reply_type, content,
                response_time, success, satisfaction_score, quality_score,
                timestamp, context, error_message, fallback_used,
                llm_model, template_version
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        # 处理context中的不可序列化对象
        serializable_context = self._make_context_serializable(metric.context)

        params = (
            metric.session_id, metric.user_id, metric.reply_key,
            metric.reply_type, metric.content, metric.response_time,
            metric.success, metric.satisfaction_score, metric.quality_score,
            metric.timestamp.isoformat(), json.dumps(serializable_context),
            metric.error_message, metric.fallback_used,
            metric.llm_model, metric.template_version
        )

        await self.db_manager.execute_update(query, params)

    def _make_context_serializable(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        将context中的不可序列化对象转换为可序列化的格式

        Args:
            context: 原始context字典

        Returns:
            可序列化的context字典
        """
        if not context:
            return {}

        serializable_context = {}

        for key, value in context.items():
            try:
                # 检查是否是IntentResult对象
                if hasattr(value, '__class__') and value.__class__.__name__ == 'IntentResult':
                    # 将IntentResult转换为字典
                    serializable_context[key] = {
                        'intent': value.intent,
                        'emotion': value.emotion,
                        'confidence': value.confidence,
                        'entities': value.entities,
                        '_type': 'IntentResult'  # 标记类型以便后续识别
                    }
                elif isinstance(value, dict):
                    # 递归处理嵌套字典
                    serializable_context[key] = self._make_context_serializable(value)
                elif isinstance(value, list):
                    # 处理列表中的对象
                    serializable_list = []
                    for item in value:
                        if hasattr(item, '__class__') and item.__class__.__name__ == 'IntentResult':
                            serializable_list.append({
                                'intent': item.intent,
                                'emotion': item.emotion,
                                'confidence': item.confidence,
                                'entities': item.entities,
                                '_type': 'IntentResult'
                            })
                        elif isinstance(item, dict):
                            serializable_list.append(self._make_context_serializable(item))
                        else:
                            # 尝试JSON序列化测试
                            try:
                                json.dumps(item)
                                serializable_list.append(item)
                            except (TypeError, ValueError):
                                # 如果无法序列化，转换为字符串
                                serializable_list.append(str(item))
                    serializable_context[key] = serializable_list
                else:
                    # 尝试JSON序列化测试
                    try:
                        json.dumps(value)
                        serializable_context[key] = value
                    except (TypeError, ValueError):
                        # 如果无法序列化，转换为字符串
                        serializable_context[key] = str(value)

            except Exception as e:
                # 如果处理过程中出现任何错误，记录日志并使用字符串表示
                self.logger.warning(f"序列化context键'{key}'时出错: {e}")
                serializable_context[key] = str(value)

        return serializable_context

    async def _check_alerts(self, metric: ReplyMetric):
        """检查告警条件"""
        current_time = datetime.now()
        self.logger.debug(f"检查告警条件，当前指标: {metric.reply_key}")

        for rule in self._alert_rules.values():
            if not rule.enabled:
                continue

            try:
                # 计算时间窗口内的指标值
                window_start = current_time - timedelta(minutes=rule.time_window)
                window_metrics = [
                    m for m in self._metrics_cache
                    if m.timestamp >= window_start
                ]

                if not window_metrics:
                    continue

                # 根据指标类型计算当前值
                current_value = self._calculate_metric_value(rule.metric_type, window_metrics)

                # 检查是否触发告警
                if self._should_trigger_alert(current_value, rule.threshold, rule.comparison):
                    await self._trigger_alert(rule, current_value, current_time)
                else:
                    # 检查是否可以解决现有告警
                    await self._resolve_alert_if_needed(rule.rule_id, current_value, current_time)

            except Exception as e:
                self.logger.error(f"检查告警规则 {rule.rule_id} 时出错: {e}")

    def _calculate_metric_value(self, metric_type: MetricType, metrics: List[ReplyMetric]) -> float:
        """计算指标值"""
        if not metrics:
            return 0.0

        if metric_type == MetricType.RESPONSE_TIME:
            return statistics.mean([m.response_time for m in metrics])
        elif metric_type == MetricType.SUCCESS_RATE:
            return sum(1 for m in metrics if m.success) / len(metrics)
        elif metric_type == MetricType.ERROR_RATE:
            return sum(1 for m in metrics if not m.success) / len(metrics)
        elif metric_type == MetricType.FALLBACK_RATE:
            return sum(1 for m in metrics if m.fallback_used) / len(metrics)
        elif metric_type == MetricType.SATISFACTION:
            satisfaction_scores = [m.satisfaction_score for m in metrics if m.satisfaction_score]
            return statistics.mean(satisfaction_scores) if satisfaction_scores else 0.0
        elif metric_type == MetricType.QUALITY_SCORE:
            return statistics.mean([m.quality_score for m in metrics])
        else:
            return 0.0

    def _should_trigger_alert(self, current_value: float, threshold: float, comparison: str) -> bool:
        """判断是否应该触发告警"""
        if comparison == ">":
            return current_value > threshold
        elif comparison == "<":
            return current_value < threshold
        elif comparison == ">=":
            return current_value >= threshold
        elif comparison == "<=":
            return current_value <= threshold
        elif comparison == "==":
            return abs(current_value - threshold) < 0.001
        else:
            return False

    async def _trigger_alert(self, rule: AlertRule, current_value: float, timestamp: datetime):
        """触发告警"""
        # 检查是否已有相同的活跃告警
        if rule.rule_id in self._active_alerts:
            return

        alert_id = f"{rule.rule_id}_{int(timestamp.timestamp())}"
        message = f"{rule.name}: 当前值 {current_value:.3f} {rule.comparison} 阈值 {rule.threshold}"

        alert = Alert(
            alert_id=alert_id,
            rule_id=rule.rule_id,
            metric_type=rule.metric_type,
            current_value=current_value,
            threshold=rule.threshold,
            alert_level=rule.alert_level,
            message=message,
            timestamp=timestamp
        )

        # 添加到活跃告警
        self._active_alerts[rule.rule_id] = alert
        self._alert_history.append(alert)

        # 保存到数据库
        await self._save_alert_to_db(alert)

        # 记录日志
        log_level = {
            AlertLevel.INFO: logging.INFO,
            AlertLevel.WARNING: logging.WARNING,
            AlertLevel.ERROR: logging.ERROR,
            AlertLevel.CRITICAL: logging.CRITICAL
        }.get(rule.alert_level, logging.WARNING)

        self.logger.log(log_level, f"触发告警: {message}")

    async def _resolve_alert_if_needed(self, rule_id: str, current_value: float, timestamp: datetime):
        """如果条件满足则解决告警"""
        if rule_id not in self._active_alerts:
            return

        alert = self._active_alerts[rule_id]
        rule = self._alert_rules[rule_id]

        # 检查是否满足解决条件（与触发条件相反）
        should_resolve = False
        if rule.comparison == ">" and current_value <= rule.threshold:
            should_resolve = True
        elif rule.comparison == "<" and current_value >= rule.threshold:
            should_resolve = True
        elif rule.comparison == ">=" and current_value < rule.threshold:
            should_resolve = True
        elif rule.comparison == "<=" and current_value > rule.threshold:
            should_resolve = True

        if should_resolve:
            alert.resolved = True
            alert.resolved_at = timestamp

            # 从活跃告警中移除
            del self._active_alerts[rule_id]

            # 更新数据库
            await self._update_alert_resolved(alert.alert_id, timestamp)

            self.logger.info(f"告警已解决: {rule.name} - 当前值: {current_value:.3f}")

    async def _save_alert_to_db(self, alert: Alert):
        """保存告警到数据库"""
        query = """
            INSERT INTO alert_history (
                alert_id, rule_id, metric_type, current_value, threshold,
                alert_level, message, timestamp, resolved, resolved_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            alert.alert_id, alert.rule_id, alert.metric_type.value,
            alert.current_value, alert.threshold, alert.alert_level.value,
            alert.message, alert.timestamp.isoformat(),
            alert.resolved, alert.resolved_at.isoformat() if alert.resolved_at else None
        )

        await self.db_manager.execute_update(query, params)

    async def _update_alert_resolved(self, alert_id: str, resolved_at: datetime):
        """更新告警为已解决"""
        query = """
            UPDATE alert_history
            SET resolved = TRUE, resolved_at = ?
            WHERE alert_id = ?
        """
        await self.db_manager.execute_update(query, (resolved_at.isoformat(), alert_id))

    async def get_real_time_metrics(self, time_window: int = 5) -> Dict[str, Any]:
        """
        获取实时指标

        Args:
            time_window: 时间窗口（分钟）

        Returns:
            Dict: 实时指标数据
        """
        try:
            current_time = datetime.now()
            window_start = current_time - timedelta(minutes=time_window)

            # 获取时间窗口内的指标
            window_metrics = [
                m for m in self._metrics_cache
                if m.timestamp >= window_start
            ]

            if not window_metrics:
                return {"message": "暂无数据", "time_window": time_window}

            # 计算各项指标
            total_count = len(window_metrics)
            success_count = sum(1 for m in window_metrics if m.success)
            error_count = sum(1 for m in window_metrics if not m.success)
            fallback_count = sum(1 for m in window_metrics if m.fallback_used)

            response_times = [m.response_time for m in window_metrics]
            quality_scores = [m.quality_score for m in window_metrics]
            satisfaction_scores = [m.satisfaction_score for m in window_metrics if m.satisfaction_score]

            return {
                "time_window_minutes": time_window,
                "timestamp": current_time.isoformat(),
                "total_replies": total_count,
                "success_rate": success_count / total_count if total_count > 0 else 0,
                "error_rate": error_count / total_count if total_count > 0 else 0,
                "fallback_rate": fallback_count / total_count if total_count > 0 else 0,
                "response_time": {
                    "average": statistics.mean(response_times) if response_times else 0,
                    "median": statistics.median(response_times) if response_times else 0,
                    "min": min(response_times) if response_times else 0,
                    "max": max(response_times) if response_times else 0,
                    "p95": statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else (max(response_times) if response_times else 0)
                },
                "quality_score": {
                    "average": statistics.mean(quality_scores) if quality_scores else 0,
                    "min": min(quality_scores) if quality_scores else 0,
                    "max": max(quality_scores) if quality_scores else 0
                },
                "satisfaction": {
                    "average": statistics.mean(satisfaction_scores) if satisfaction_scores else 0,
                    "count": len(satisfaction_scores),
                    "distribution": self._get_satisfaction_distribution(satisfaction_scores)
                },
                "active_alerts": len(self._active_alerts),
                "reply_types": self._get_reply_type_distribution(window_metrics)
            }

        except Exception as e:
            self.logger.error(f"获取实时指标失败: {e}", exc_info=True)
            return {"error": str(e)}

    def _get_satisfaction_distribution(self, scores: List[int]) -> Dict[str, int]:
        """获取满意度分布"""
        distribution = {str(i): 0 for i in range(1, 6)}
        for score in scores:
            distribution[str(score)] += 1
        return distribution

    def _get_reply_type_distribution(self, metrics: List[ReplyMetric]) -> Dict[str, int]:
        """获取回复类型分布"""
        distribution = defaultdict(int)
        for metric in metrics:
            distribution[metric.reply_type] += 1
        return dict(distribution)

    async def get_trend_analysis(self, days: int = 7) -> Dict[str, Any]:
        """
        获取趋势分析

        Args:
            days: 分析天数

        Returns:
            Dict: 趋势分析数据
        """
        try:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days)

            # 从数据库查询历史数据
            query = """
                SELECT
                    DATE(timestamp) as date,
                    COUNT(*) as total_count,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_count,
                    AVG(response_time) as avg_response_time,
                    AVG(quality_score) as avg_quality_score,
                    AVG(CASE WHEN satisfaction_score IS NOT NULL THEN satisfaction_score END) as avg_satisfaction
                FROM reply_metrics
                WHERE DATE(timestamp) BETWEEN ? AND ?
                GROUP BY DATE(timestamp)
                ORDER BY date
            """

            results = await self.db_manager.execute_query(
                query, (start_date.isoformat(), end_date.isoformat())
            )

            # 处理结果
            trend_data = []
            for row in results:
                date_str = row.get("date")
                total = row.get("total_count", 0)
                success = row.get("success_count", 0)

                trend_data.append({
                    "date": date_str,
                    "total_replies": total,
                    "success_rate": success / total if total > 0 else 0,
                    "avg_response_time": row.get("avg_response_time", 0) or 0,
                    "avg_quality_score": row.get("avg_quality_score", 0) or 0,
                    "avg_satisfaction": row.get("avg_satisfaction", 0) or 0
                })

            # 计算趋势
            trends = self._calculate_trends(trend_data)

            return {
                "period": f"{start_date} to {end_date}",
                "daily_data": trend_data,
                "trends": trends,
                "summary": self._generate_trend_summary(trend_data, trends)
            }

        except Exception as e:
            self.logger.error(f"获取趋势分析失败: {e}", exc_info=True)
            return {"error": str(e)}

    def _calculate_trends(self, data: List[Dict]) -> Dict[str, str]:
        """计算趋势方向"""
        if len(data) < 2:
            return {}

        trends = {}
        metrics = ["success_rate", "avg_response_time", "avg_quality_score", "avg_satisfaction"]

        for metric in metrics:
            values = [d[metric] for d in data if d[metric] is not None]
            if len(values) >= 2:
                # 简单的趋势计算：比较前半段和后半段的平均值
                mid = len(values) // 2
                first_half = statistics.mean(values[:mid]) if values[:mid] else 0
                second_half = statistics.mean(values[mid:]) if values[mid:] else 0

                if second_half > first_half * 1.05:  # 5%以上增长
                    trends[metric] = "上升"
                elif second_half < first_half * 0.95:  # 5%以上下降
                    trends[metric] = "下降"
                else:
                    trends[metric] = "稳定"

        return trends

    def _generate_trend_summary(self, data: List[Dict], trends: Dict[str, str]) -> str:
        """生成趋势摘要"""
        if not data:
            return "暂无数据"

        latest = data[-1] if data else {}
        summary_parts = []

        if latest.get("success_rate"):
            summary_parts.append(f"成功率 {latest['success_rate']:.1%}")

        if latest.get("avg_response_time"):
            summary_parts.append(f"平均响应时间 {latest['avg_response_time']:.2f}秒")

        if latest.get("avg_satisfaction"):
            summary_parts.append(f"用户满意度 {latest['avg_satisfaction']:.1f}分")

        trend_summary = []
        for metric, trend in trends.items():
            if trend != "稳定":
                metric_name = {
                    "success_rate": "成功率",
                    "avg_response_time": "响应时间",
                    "avg_quality_score": "质量分数",
                    "avg_satisfaction": "满意度"
                }.get(metric, metric)
                trend_summary.append(f"{metric_name}{trend}")

        result = "最新数据: " + ", ".join(summary_parts)
        if trend_summary:
            result += f"; 趋势: {', '.join(trend_summary)}"

        return result

    async def get_active_alerts(self) -> List[Dict[str, Any]]:
        """获取活跃告警"""
        return [
            {
                "alert_id": alert.alert_id,
                "rule_id": alert.rule_id,
                "metric_type": alert.metric_type.value,
                "current_value": alert.current_value,
                "threshold": alert.threshold,
                "alert_level": alert.alert_level.value,
                "message": alert.message,
                "timestamp": alert.timestamp.isoformat(),
                "duration": str(datetime.now() - alert.timestamp)
            }
            for alert in self._active_alerts.values()
        ]

    async def get_alert_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """
        获取告警历史

        Args:
            hours: 查询小时数

        Returns:
            List[Dict]: 告警历史列表
        """
        try:
            start_time = datetime.now() - timedelta(hours=hours)

            query = """
                SELECT * FROM alert_history
                WHERE timestamp >= ?
                ORDER BY timestamp DESC
            """

            results = await self.db_manager.execute_query(
                query, (start_time.isoformat(),)
            )

            return [
                {
                    "alert_id": row.get("alert_id"),
                    "rule_id": row.get("rule_id"),
                    "metric_type": row.get("metric_type"),
                    "current_value": row.get("current_value"),
                    "threshold": row.get("threshold"),
                    "alert_level": row.get("alert_level"),
                    "message": row.get("message"),
                    "timestamp": row.get("timestamp"),
                    "resolved": row.get("resolved"),
                    "resolved_at": row.get("resolved_at"),
                    "duration": self._calculate_alert_duration(
                        row.get("timestamp"), row.get("resolved_at")
                    )
                }
                for row in results
            ]

        except Exception as e:
            self.logger.error(f"获取告警历史失败: {e}", exc_info=True)
            return []

    def _calculate_alert_duration(self, start_time: str, end_time: str = None) -> str:
        """计算告警持续时间"""
        try:
            start = datetime.fromisoformat(start_time)
            end = datetime.fromisoformat(end_time) if end_time else datetime.now()
            duration = end - start

            hours = duration.total_seconds() // 3600
            minutes = (duration.total_seconds() % 3600) // 60

            if hours > 0:
                return f"{int(hours)}小时{int(minutes)}分钟"
            else:
                return f"{int(minutes)}分钟"
        except:
            return "未知"

    async def get_satisfaction_analysis(self, days: int = 7) -> Dict[str, Any]:
        """
        获取用户满意度分析

        Args:
            days: 分析天数

        Returns:
            Dict: 满意度分析数据
        """
        try:
            start_date = datetime.now() - timedelta(days=days)

            # 查询满意度数据
            query = """
                SELECT
                    satisfaction_score,
                    reply_key,
                    DATE(timestamp) as date,
                    COUNT(*) as count
                FROM satisfaction_feedback
                WHERE timestamp >= ?
                GROUP BY satisfaction_score, reply_key, DATE(timestamp)
                ORDER BY date DESC, satisfaction_score
            """

            results = await self.db_manager.execute_query(
                query, (start_date.isoformat(),)
            )

            # 处理数据
            total_feedback = 0
            score_distribution = {str(i): 0 for i in range(1, 6)}
            reply_key_scores = defaultdict(list)
            daily_scores = defaultdict(list)

            for row in results:
                score = row.get("satisfaction_score")
                reply_key = row.get("reply_key")
                date = row.get("date")
                count = row.get("count", 1)

                total_feedback += count
                score_distribution[str(score)] += count

                # 按回复键分组
                reply_key_scores[reply_key].extend([score] * count)

                # 按日期分组
                daily_scores[date].extend([score] * count)

            # 计算统计信息
            all_scores = []
            for reply_key, scores in reply_key_scores.items():
                all_scores.extend(scores)

            avg_satisfaction = statistics.mean(all_scores) if all_scores else 0

            # 按回复键的满意度排名
            reply_rankings = []
            for reply_key, scores in reply_key_scores.items():
                if scores:
                    reply_rankings.append({
                        "reply_key": reply_key,
                        "average_score": statistics.mean(scores),
                        "total_feedback": len(scores),
                        "distribution": {
                            str(i): scores.count(i) for i in range(1, 6)
                        }
                    })

            reply_rankings.sort(key=lambda x: x["average_score"], reverse=True)

            # 每日趋势
            daily_trends = []
            for date in sorted(daily_scores.keys()):
                scores = daily_scores[date]
                daily_trends.append({
                    "date": date,
                    "average_score": statistics.mean(scores),
                    "total_feedback": len(scores),
                    "distribution": {str(i): scores.count(i) for i in range(1, 6)}
                })

            return {
                "period": f"最近{days}天",
                "total_feedback": total_feedback,
                "average_satisfaction": avg_satisfaction,
                "score_distribution": score_distribution,
                "reply_rankings": reply_rankings[:10],  # 前10名
                "daily_trends": daily_trends,
                "insights": self._generate_satisfaction_insights(
                    avg_satisfaction, score_distribution, reply_rankings
                )
            }

        except Exception as e:
            self.logger.error(f"获取满意度分析失败: {e}", exc_info=True)
            return {"error": str(e)}

    def _generate_satisfaction_insights(
        self,
        avg_score: float,
        distribution: Dict[str, int],
        rankings: List[Dict]
    ) -> List[str]:
        """生成满意度洞察"""
        insights = []

        # 整体满意度评价
        if avg_score >= 4.0:
            insights.append("用户满意度整体较高")
        elif avg_score >= 3.0:
            insights.append("用户满意度中等，有提升空间")
        else:
            insights.append("用户满意度较低，需要重点关注")

        # 分布分析
        total = sum(distribution.values())
        if total > 0:
            high_satisfaction = (int(distribution["4"]) + int(distribution["5"])) / total
            low_satisfaction = (int(distribution["1"]) + int(distribution["2"])) / total

            if high_satisfaction > 0.7:
                insights.append(f"高满意度用户占比 {high_satisfaction:.1%}")

            if low_satisfaction > 0.2:
                insights.append(f"低满意度用户占比 {low_satisfaction:.1%}，需要改进")

        # 回复类型分析
        if rankings:
            best_reply = rankings[0]
            worst_reply = rankings[-1]

            if best_reply["average_score"] >= 4.0:
                insights.append(f"表现最佳的回复类型: {best_reply['reply_key']} (平均{best_reply['average_score']:.1f}分)")

            if worst_reply["average_score"] <= 2.5:
                insights.append(f"需要改进的回复类型: {worst_reply['reply_key']} (平均{worst_reply['average_score']:.1f}分)")

        return insights

    async def get_performance_report(self, days: int = 7) -> Dict[str, Any]:
        """
        获取性能报告

        Args:
            days: 报告天数

        Returns:
            Dict: 性能报告数据
        """
        try:
            # 获取各种分析数据
            real_time_metrics = await self.get_real_time_metrics(60)  # 最近1小时
            trend_analysis = await self.get_trend_analysis(days)
            satisfaction_analysis = await self.get_satisfaction_analysis(days)
            active_alerts = await self.get_active_alerts()
            alert_history = await self.get_alert_history(24)  # 最近24小时

            # 生成总体评分
            overall_score = self._calculate_overall_score(
                real_time_metrics, trend_analysis, satisfaction_analysis
            )

            # 生成建议
            recommendations = self._generate_recommendations(
                real_time_metrics, trend_analysis, satisfaction_analysis, active_alerts
            )

            return {
                "report_period": f"最近{days}天",
                "generated_at": datetime.now().isoformat(),
                "overall_score": overall_score,
                "summary": {
                    "total_replies": self._stats["total_replies"],
                    "success_rate": self._stats["successful_replies"] / max(self._stats["total_replies"], 1),
                    "average_response_time": self._stats["average_response_time"],
                    "average_satisfaction": self._stats["average_satisfaction"],
                    "active_alerts": len(active_alerts)
                },
                "real_time_metrics": real_time_metrics,
                "trend_analysis": trend_analysis,
                "satisfaction_analysis": satisfaction_analysis,
                "alerts": {
                    "active": active_alerts,
                    "recent_history": alert_history[:5]  # 最近5个告警
                },
                "recommendations": recommendations,
                "system_health": self._assess_system_health(real_time_metrics, active_alerts)
            }

        except Exception as e:
            self.logger.error(f"生成性能报告失败: {e}", exc_info=True)
            return {"error": str(e)}

    def _calculate_overall_score(
        self,
        real_time: Dict,
        trends: Dict,
        satisfaction: Dict
    ) -> Dict[str, Any]:
        """计算总体评分"""
        try:
            # 记录趋势信息用于调试
            self.logger.debug(f"计算总体评分，趋势数据: {trends.get('trends', {})}")
            # 基础分数
            base_score = 100

            # 成功率影响 (权重: 30%)
            success_rate = real_time.get("success_rate", 0)
            if success_rate < 0.9:
                base_score -= (0.9 - success_rate) * 100 * 0.3

            # 响应时间影响 (权重: 25%)
            avg_response_time = real_time.get("response_time", {}).get("average", 0)
            if avg_response_time > 2.0:  # 超过2秒
                base_score -= min((avg_response_time - 2.0) * 10, 25) * 0.25

            # 满意度影响 (权重: 25%)
            avg_satisfaction = satisfaction.get("average_satisfaction", 0)
            if avg_satisfaction > 0:
                satisfaction_score = (avg_satisfaction - 3.0) * 25  # 3分为中性
                base_score += satisfaction_score * 0.25

            # 错误率影响 (权重: 20%)
            error_rate = real_time.get("error_rate", 0)
            if error_rate > 0.05:  # 超过5%
                base_score -= (error_rate - 0.05) * 100 * 0.2

            # 确保分数在0-100之间
            final_score = max(0, min(100, base_score))

            # 评级
            if final_score >= 90:
                grade = "优秀"
            elif final_score >= 80:
                grade = "良好"
            elif final_score >= 70:
                grade = "一般"
            elif final_score >= 60:
                grade = "较差"
            else:
                grade = "差"

            return {
                "score": round(final_score, 1),
                "grade": grade,
                "components": {
                    "success_rate": success_rate,
                    "response_time": avg_response_time,
                    "satisfaction": avg_satisfaction,
                    "error_rate": error_rate
                }
            }

        except Exception as e:
            self.logger.error(f"计算总体评分失败: {e}")
            return {"score": 0, "grade": "未知", "error": str(e)}

    def _generate_recommendations(
        self,
        real_time: Dict,
        trends: Dict,
        satisfaction: Dict,
        alerts: List
    ) -> List[str]:
        """生成改进建议"""
        recommendations = []

        # 基于实时指标的建议
        if real_time.get("success_rate", 1) < 0.9:
            recommendations.append("成功率较低，建议检查错误日志并优化回复逻辑")

        if real_time.get("response_time", {}).get("average", 0) > 3.0:
            recommendations.append("响应时间较长，建议优化LLM调用或增加缓存")

        if real_time.get("fallback_rate", 0) > 0.2:
            recommendations.append("回退率较高，建议完善模板库或改进动态生成逻辑")

        # 基于趋势的建议
        trend_data = trends.get("trends", {})
        if trend_data.get("success_rate") == "下降":
            recommendations.append("成功率呈下降趋势，需要及时干预")

        if trend_data.get("avg_response_time") == "上升":
            recommendations.append("响应时间呈上升趋势，建议进行性能优化")

        # 基于满意度的建议
        if satisfaction.get("average_satisfaction", 0) < 3.5:
            recommendations.append("用户满意度偏低，建议优化回复内容质量")

        # 基于告警的建议
        if len(alerts) > 0:
            recommendations.append(f"当前有{len(alerts)}个活跃告警，建议及时处理")

        # 基于满意度排名的建议
        rankings = satisfaction.get("reply_rankings", [])
        if rankings:
            worst_replies = [r for r in rankings if r["average_score"] < 3.0]
            if worst_replies:
                recommendations.append(f"以下回复类型满意度较低，建议重点优化: {', '.join([r['reply_key'] for r in worst_replies[:3]])}")

        return recommendations if recommendations else ["系统运行良好，继续保持"]

    def _assess_system_health(self, real_time: Dict, alerts: List) -> str:
        """评估系统健康状态"""
        if len(alerts) > 0:
            critical_alerts = [a for a in alerts if a.get("alert_level") == "critical"]
            if critical_alerts:
                return "严重"
            return "警告"

        success_rate = real_time.get("success_rate", 0)
        response_time = real_time.get("response_time", {}).get("average", 0)

        if success_rate >= 0.95 and response_time <= 2.0:
            return "健康"
        elif success_rate >= 0.9 and response_time <= 5.0:
            return "良好"
        else:
            return "需要关注"

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self._stats,
            "cache_sizes": {
                "metrics": len(self._metrics_cache),
                "satisfaction": len(self._satisfaction_cache),
                "response_time": len(self._response_time_cache)
            },
            "alert_summary": {
                "active_alerts": len(self._active_alerts),
                "total_rules": len(self._alert_rules),
                "enabled_rules": len([r for r in self._alert_rules.values() if r.enabled])
            }
        }

    def reset_stats(self):
        """重置统计信息"""
        self._stats = {
            "total_replies": 0,
            "successful_replies": 0,
            "failed_replies": 0,
            "fallback_replies": 0,
            "average_response_time": 0.0,
            "average_satisfaction": 0.0,
            "average_quality_score": 0.0,
            "hourly_stats": defaultdict(lambda: {
                "count": 0, "success": 0, "avg_time": 0.0, "avg_satisfaction": 0.0
            }),
            "daily_stats": defaultdict(lambda: {
                "count": 0, "success": 0, "avg_time": 0.0, "avg_satisfaction": 0.0
            })
        }

        # 清空缓存
        self._metrics_cache.clear()
        self._satisfaction_cache.clear()
        self._response_time_cache.clear()

        self.logger.info("已重置监控系统统计信息")
