# -*- coding: utf-8 -*-
"""
消息模板版本管理器 - 支持版本控制、A/B测试和效果追踪
版本: v1.0
作者: AI Assistant
创建时间: 2025-06-20

功能:
1. 消息模板版本控制和历史管理
2. A/B测试支持和流量分配
3. 模板效果追踪和分析
4. 模板回滚和发布管理
5. 多语言版本支持
"""

import logging
import json
import hashlib
import uuid
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
from datetime import datetime, timedelta
from dataclasses import dataclass
from backend.data.db.database_manager import DatabaseManager


class TemplateStatus(Enum):
    """模板状态枚举"""
    DRAFT = "draft"                 # 草稿
    TESTING = "testing"             # 测试中
    ACTIVE = "active"               # 激活
    DEPRECATED = "deprecated"       # 已弃用
    ARCHIVED = "archived"           # 已归档


class ABTestStatus(Enum):
    """A/B测试状态枚举"""
    PLANNED = "planned"             # 计划中
    RUNNING = "running"             # 运行中
    PAUSED = "paused"              # 暂停
    COMPLETED = "completed"         # 已完成
    CANCELLED = "cancelled"         # 已取消


@dataclass
class TemplateVersion:
    """模板版本数据类"""
    template_id: str
    version: str
    content: str
    variables: List[str]
    category: str
    language: str
    status: TemplateStatus
    created_by: str
    created_at: datetime
    updated_at: datetime
    description: str = ""
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ABTestConfig:
    """A/B测试配置数据类"""
    test_id: str
    test_name: str
    template_id: str
    variant_a_version: str
    variant_b_version: str
    traffic_split: float  # 0.0-1.0，variant_a的流量比例
    start_time: datetime
    end_time: datetime
    status: ABTestStatus
    target_metrics: List[str]
    created_by: str
    created_at: datetime
    description: str = ""
    
    def get_variant_for_user(self, user_id: str) -> str:
        """根据用户ID确定使用哪个变体"""
        # 使用用户ID的哈希值来确定分组
        hash_value = int(hashlib.md5(f"{self.test_id}_{user_id}".encode()).hexdigest(), 16)
        if (hash_value % 100) / 100 < self.traffic_split:
            return self.variant_a_version
        else:
            return self.variant_b_version


@dataclass
class TemplateUsageMetrics:
    """模板使用指标数据类"""
    template_id: str
    version: str
    usage_count: int
    success_count: int
    error_count: int
    user_satisfaction_score: float
    avg_response_time: float
    last_used: datetime
    date_recorded: datetime


class TemplateVersionManager:
    """消息模板版本管理器"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """
        初始化模板版本管理器

        Args:
            db_manager: 数据库管理器实例
        """
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager or DatabaseManager("data/template_versions.db")

        # 内存缓存
        self._template_cache = {}
        self._ab_test_cache = {}
        self._metrics_cache = {}

        self.logger.info("模板版本管理器初始化完成")

    async def initialize_database(self):
        """异步初始化数据库表"""
        await self._initialize_database()
    
    async def _initialize_database(self):
        """初始化数据库表"""
        try:
            # 使用事务来确保所有表都创建成功
            queries = [
                # 创建模板版本表
                ("""
                    CREATE TABLE IF NOT EXISTS template_versions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        template_id TEXT NOT NULL,
                        version TEXT NOT NULL,
                        content TEXT NOT NULL,
                        variables TEXT,
                        category TEXT NOT NULL,
                        language TEXT DEFAULT 'zh-CN',
                        status TEXT NOT NULL,
                        created_by TEXT NOT NULL,
                        created_at TIMESTAMP NOT NULL,
                        updated_at TIMESTAMP NOT NULL,
                        description TEXT,
                        tags TEXT,
                        metadata TEXT,
                        UNIQUE(template_id, version)
                    )
                """, None),

                # 创建A/B测试配置表
                ("""
                    CREATE TABLE IF NOT EXISTS ab_tests (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        test_id TEXT UNIQUE NOT NULL,
                        test_name TEXT NOT NULL,
                        template_id TEXT NOT NULL,
                        variant_a_version TEXT NOT NULL,
                        variant_b_version TEXT NOT NULL,
                        traffic_split REAL NOT NULL,
                        start_time TIMESTAMP NOT NULL,
                        end_time TIMESTAMP NOT NULL,
                        status TEXT NOT NULL,
                        target_metrics TEXT,
                        created_by TEXT NOT NULL,
                        created_at TIMESTAMP NOT NULL,
                        description TEXT
                    )
                """, None),

                # 创建模板使用指标表
                ("""
                    CREATE TABLE IF NOT EXISTS template_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        template_id TEXT NOT NULL,
                        version TEXT NOT NULL,
                        usage_count INTEGER DEFAULT 0,
                        success_count INTEGER DEFAULT 0,
                        error_count INTEGER DEFAULT 0,
                        user_satisfaction_score REAL DEFAULT 0.0,
                        avg_response_time REAL DEFAULT 0.0,
                        last_used TIMESTAMP,
                        date_recorded DATE NOT NULL,
                        UNIQUE(template_id, version, date_recorded)
                    )
                """, None),

                # 创建A/B测试结果表
                ("""
                    CREATE TABLE IF NOT EXISTS ab_test_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        test_id TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        variant_used TEXT NOT NULL,
                        template_id TEXT NOT NULL,
                        template_version TEXT NOT NULL,
                        success BOOLEAN NOT NULL,
                        response_time REAL,
                        user_satisfaction INTEGER,
                        timestamp TIMESTAMP NOT NULL,
                        session_id TEXT,
                        additional_data TEXT
                    )
                """, None)
            ]

            # 使用事务执行所有创建表的SQL
            success = await self.db_manager.execute_transaction(queries)

            if success:
                self.logger.info("模板版本管理数据库表初始化完成")
            else:
                raise Exception("数据库表创建事务失败")

        except Exception as e:
            self.logger.error(f"初始化数据库表失败: {e}", exc_info=True)
            raise
    
    async def create_template_version(
        self,
        template_id: str,
        content: str,
        variables: List[str] = None,
        category: str = "general",
        language: str = "zh-CN",
        created_by: str = "system",
        description: str = "",
        tags: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> TemplateVersion:
        """
        创建新的模板版本
        
        Args:
            template_id: 模板ID
            content: 模板内容
            variables: 变量列表
            category: 模板类别
            language: 语言
            created_by: 创建者
            description: 描述
            tags: 标签
            metadata: 元数据
            
        Returns:
            TemplateVersion: 创建的模板版本
        """
        try:
            # 生成新版本号
            latest_version = await self._get_latest_version(template_id)
            new_version = self._increment_version(latest_version)
            
            # 创建模板版本对象
            template_version = TemplateVersion(
                template_id=template_id,
                version=new_version,
                content=content,
                variables=variables or [],
                category=category,
                language=language,
                status=TemplateStatus.DRAFT,
                created_by=created_by,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                description=description,
                tags=tags or [],
                metadata=metadata or {}
            )
            
            # 保存到数据库
            await self._save_template_version(template_version)
            
            # 更新缓存
            cache_key = f"{template_id}_{new_version}"
            self._template_cache[cache_key] = template_version
            
            self.logger.info(f"创建模板版本成功: {template_id} v{new_version}")
            return template_version
            
        except Exception as e:
            self.logger.error(f"创建模板版本失败: {e}", exc_info=True)
            raise
    
    async def get_template_version(
        self,
        template_id: str,
        version: str = None,
        language: str = "zh-CN"
    ) -> Optional[TemplateVersion]:
        """
        获取指定版本的模板
        
        Args:
            template_id: 模板ID
            version: 版本号，如果为None则获取最新激活版本
            language: 语言
            
        Returns:
            TemplateVersion: 模板版本对象
        """
        try:
            # 如果没有指定版本，获取最新激活版本
            if version is None:
                version = await self._get_active_version(template_id, language)
                if not version:
                    return None
            
            # 检查缓存
            cache_key = f"{template_id}_{version}"
            if cache_key in self._template_cache:
                return self._template_cache[cache_key]
            
            # 从数据库查询
            query = """
                SELECT * FROM template_versions 
                WHERE template_id = ? AND version = ? AND language = ?
            """
            result = await self.db_manager.get_record(query, (template_id, version, language))
            
            if result:
                template_version = self._row_to_template_version(result)
                self._template_cache[cache_key] = template_version
                return template_version
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取模板版本失败: {e}", exc_info=True)
            return None
    
    async def activate_template_version(
        self,
        template_id: str,
        version: str,
        language: str = "zh-CN"
    ) -> bool:
        """
        激活指定版本的模板
        
        Args:
            template_id: 模板ID
            version: 版本号
            language: 语言
            
        Returns:
            bool: 是否成功
        """
        try:
            # 先将同一模板的其他版本设为deprecated
            update_old_sql = """
                UPDATE template_versions 
                SET status = ?, updated_at = ?
                WHERE template_id = ? AND language = ? AND status = ?
            """
            await self.db_manager.execute_update(
                update_old_sql,
                (TemplateStatus.DEPRECATED.value, datetime.now(), template_id, language, TemplateStatus.ACTIVE.value)
            )
            
            # 激活指定版本
            update_new_sql = """
                UPDATE template_versions 
                SET status = ?, updated_at = ?
                WHERE template_id = ? AND version = ? AND language = ?
            """
            await self.db_manager.execute_update(
                update_new_sql,
                (TemplateStatus.ACTIVE.value, datetime.now(), template_id, version, language)
            )
            
            # 清除相关缓存
            self._clear_template_cache(template_id)
            
            self.logger.info(f"激活模板版本成功: {template_id} v{version}")
            return True
            
        except Exception as e:
            self.logger.error(f"激活模板版本失败: {e}", exc_info=True)
            return False
    
    async def create_ab_test(
        self,
        test_name: str,
        template_id: str,
        variant_a_version: str,
        variant_b_version: str,
        traffic_split: float = 0.5,
        duration_days: int = 7,
        target_metrics: List[str] = None,
        created_by: str = "system",
        description: str = ""
    ) -> ABTestConfig:
        """
        创建A/B测试
        
        Args:
            test_name: 测试名称
            template_id: 模板ID
            variant_a_version: 变体A版本
            variant_b_version: 变体B版本
            traffic_split: 流量分配比例（variant_a的比例）
            duration_days: 测试持续天数
            target_metrics: 目标指标
            created_by: 创建者
            description: 描述
            
        Returns:
            ABTestConfig: A/B测试配置
        """
        try:
            test_id = str(uuid.uuid4())
            start_time = datetime.now()
            end_time = start_time + timedelta(days=duration_days)
            
            ab_test = ABTestConfig(
                test_id=test_id,
                test_name=test_name,
                template_id=template_id,
                variant_a_version=variant_a_version,
                variant_b_version=variant_b_version,
                traffic_split=traffic_split,
                start_time=start_time,
                end_time=end_time,
                status=ABTestStatus.PLANNED,
                target_metrics=target_metrics or ["success_rate", "user_satisfaction"],
                created_by=created_by,
                created_at=datetime.now(),
                description=description
            )
            
            # 保存到数据库
            await self._save_ab_test(ab_test)
            
            # 更新缓存
            self._ab_test_cache[test_id] = ab_test
            
            self.logger.info(f"创建A/B测试成功: {test_name} ({test_id})")
            return ab_test
            
        except Exception as e:
            self.logger.error(f"创建A/B测试失败: {e}", exc_info=True)
            raise
    
    async def get_template_for_user(
        self,
        template_id: str,
        user_id: str,
        language: str = "zh-CN"
    ) -> Tuple[Optional[TemplateVersion], Optional[str]]:
        """
        为用户获取模板（考虑A/B测试）
        
        Args:
            template_id: 模板ID
            user_id: 用户ID
            language: 语言
            
        Returns:
            Tuple[TemplateVersion, test_id]: 模板版本和测试ID（如果参与A/B测试）
        """
        try:
            # 检查是否有正在运行的A/B测试
            active_test = await self._get_active_ab_test(template_id)
            
            if active_test:
                # 参与A/B测试
                variant_version = active_test.get_variant_for_user(user_id)
                template_version = await self.get_template_version(template_id, variant_version, language)
                
                # 记录A/B测试参与
                await self._record_ab_test_participation(active_test.test_id, user_id, variant_version, template_id, variant_version)
                
                return template_version, active_test.test_id
            else:
                # 使用正常的激活版本
                template_version = await self.get_template_version(template_id, None, language)
                return template_version, None
                
        except Exception as e:
            self.logger.error(f"获取用户模板失败: {e}", exc_info=True)
            # 返回默认版本
            template_version = await self.get_template_version(template_id, None, language)
            return template_version, None
    
    async def record_template_usage(
        self,
        template_id: str,
        version: str,
        success: bool,
        response_time: float = None,
        user_satisfaction: int = None,
        user_id: str = None,
        session_id: str = None,
        test_id: str = None
    ):
        """
        记录模板使用情况
        
        Args:
            template_id: 模板ID
            version: 版本号
            success: 是否成功
            response_time: 响应时间
            user_satisfaction: 用户满意度（1-5）
            user_id: 用户ID
            session_id: 会话ID
            test_id: A/B测试ID
        """
        try:
            # 更新模板指标
            await self._update_template_metrics(
                template_id, version, success, response_time, user_satisfaction
            )
            
            # 如果是A/B测试，记录测试结果
            if test_id and user_id:
                await self._record_ab_test_result(
                    test_id, user_id, version, template_id, version,
                    success, response_time, user_satisfaction, session_id
                )
            
        except Exception as e:
            self.logger.error(f"记录模板使用失败: {e}", exc_info=True)
    
    async def get_template_metrics(
        self,
        template_id: str,
        version: str = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        获取模板使用指标
        
        Args:
            template_id: 模板ID
            version: 版本号，如果为None则获取所有版本
            days: 统计天数
            
        Returns:
            Dict: 指标数据
        """
        try:
            start_date = datetime.now() - timedelta(days=days)
            
            if version:
                query = """
                    SELECT 
                        SUM(usage_count) as total_usage,
                        SUM(success_count) as total_success,
                        SUM(error_count) as total_errors,
                        AVG(user_satisfaction_score) as avg_satisfaction,
                        AVG(avg_response_time) as avg_response_time
                    FROM template_metrics 
                    WHERE template_id = ? AND version = ? AND date_recorded >= ?
                """
                params = (template_id, version, start_date.date())
            else:
                query = """
                    SELECT 
                        version,
                        SUM(usage_count) as total_usage,
                        SUM(success_count) as total_success,
                        SUM(error_count) as total_errors,
                        AVG(user_satisfaction_score) as avg_satisfaction,
                        AVG(avg_response_time) as avg_response_time
                    FROM template_metrics 
                    WHERE template_id = ? AND date_recorded >= ?
                    GROUP BY version
                """
                params = (template_id, start_date.date())
            
            results = await self.db_manager.execute_query(query, params)
            
            if version:
                # 单版本指标
                if results:
                    result = results[0]
                    total_usage = result.get("total_usage", 0) or 0
                    total_success = result.get("total_success", 0) or 0
                    return {
                        "template_id": template_id,
                        "version": version,
                        "total_usage": total_usage,
                        "total_success": total_success,
                        "total_errors": result.get("total_errors", 0) or 0,
                        "success_rate": total_success / max(total_usage, 1),
                        "avg_satisfaction": result.get("avg_satisfaction", 0.0) or 0.0,
                        "avg_response_time": result.get("avg_response_time", 0.0) or 0.0,
                        "period_days": days
                    }
                else:
                    return {"template_id": template_id, "version": version, "no_data": True}
            else:
                # 多版本指标
                metrics_by_version = {}
                for result in results:
                    version_key = result.get("version")
                    total_usage = result.get("total_usage", 0) or 0
                    total_success = result.get("total_success", 0) or 0
                    metrics_by_version[version_key] = {
                        "total_usage": total_usage,
                        "total_success": total_success,
                        "total_errors": result.get("total_errors", 0) or 0,
                        "success_rate": total_success / max(total_usage, 1),
                        "avg_satisfaction": result.get("avg_satisfaction", 0.0) or 0.0,
                        "avg_response_time": result.get("avg_response_time", 0.0) or 0.0
                    }
                
                return {
                    "template_id": template_id,
                    "period_days": days,
                    "versions": metrics_by_version
                }
                
        except Exception as e:
            self.logger.error(f"获取模板指标失败: {e}", exc_info=True)
            return {"error": str(e)}

    async def get_ab_test_results(self, test_id: str) -> Dict[str, Any]:
        """
        获取A/B测试结果

        Args:
            test_id: 测试ID

        Returns:
            Dict: 测试结果数据
        """
        try:
            # 获取测试配置
            ab_test = await self._get_ab_test(test_id)
            if not ab_test:
                return {"error": "测试不存在"}

            # 获取测试结果统计
            query = """
                SELECT
                    variant_used,
                    COUNT(*) as total_users,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_count,
                    AVG(response_time) as avg_response_time,
                    AVG(user_satisfaction) as avg_satisfaction
                FROM ab_test_results
                WHERE test_id = ?
                GROUP BY variant_used
            """
            results = await self.db_manager.execute_query(query, (test_id,))

            variant_stats = {}
            for result in results:
                variant = result.get("variant_used")
                total_users = result.get("total_users", 0)
                success_count = result.get("success_count", 0)
                variant_stats[variant] = {
                    "total_users": total_users,
                    "success_count": success_count,
                    "success_rate": success_count / total_users if total_users > 0 else 0,
                    "avg_response_time": result.get("avg_response_time", 0.0) or 0.0,
                    "avg_satisfaction": result.get("avg_satisfaction", 0.0) or 0.0
                }

            # 计算统计显著性（简单版本）
            if len(variant_stats) == 2:
                variants = list(variant_stats.keys())
                variant_a_stats = variant_stats[variants[0]]
                variant_b_stats = variant_stats[variants[1]]

                # 计算置信度（简化版本）
                confidence = self._calculate_confidence(variant_a_stats, variant_b_stats)
            else:
                confidence = None

            return {
                "test_id": test_id,
                "test_name": ab_test.test_name,
                "template_id": ab_test.template_id,
                "status": ab_test.status.value,
                "start_time": ab_test.start_time.isoformat(),
                "end_time": ab_test.end_time.isoformat(),
                "traffic_split": ab_test.traffic_split,
                "variant_stats": variant_stats,
                "confidence": confidence,
                "recommendation": self._get_test_recommendation(variant_stats, confidence)
            }

        except Exception as e:
            self.logger.error(f"获取A/B测试结果失败: {e}", exc_info=True)
            return {"error": str(e)}

    async def list_template_versions(
        self,
        template_id: str = None,
        status: TemplateStatus = None,
        language: str = None
    ) -> List[Dict[str, Any]]:
        """
        列出模板版本

        Args:
            template_id: 模板ID过滤
            status: 状态过滤
            language: 语言过滤

        Returns:
            List[Dict]: 模板版本列表
        """
        try:
            conditions = []
            params = []

            if template_id:
                conditions.append("template_id = ?")
                params.append(template_id)

            if status:
                conditions.append("status = ?")
                params.append(status.value)

            if language:
                conditions.append("language = ?")
                params.append(language)

            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

            query = f"""
                SELECT template_id, version, category, language, status,
                       created_by, created_at, updated_at, description
                FROM template_versions
                {where_clause}
                ORDER BY template_id, created_at DESC
            """

            results = await self.db_manager.execute_query(query, params)

            versions = []
            for result in results:
                versions.append({
                    "template_id": result.get("template_id"),
                    "version": result.get("version"),
                    "category": result.get("category"),
                    "language": result.get("language"),
                    "status": result.get("status"),
                    "created_by": result.get("created_by"),
                    "created_at": result.get("created_at"),
                    "updated_at": result.get("updated_at"),
                    "description": result.get("description")
                })

            return versions

        except Exception as e:
            self.logger.error(f"列出模板版本失败: {e}", exc_info=True)
            return []

    # 私有辅助方法
    async def _get_latest_version(self, template_id: str) -> str:
        """获取最新版本号"""
        query = """
            SELECT version FROM template_versions
            WHERE template_id = ?
            ORDER BY created_at DESC
            LIMIT 1
        """
        result = await self.db_manager.get_record(query, (template_id,))
        return result["version"] if result else "0.0"

    def _increment_version(self, current_version: str) -> str:
        """递增版本号"""
        try:
            parts = current_version.split('.')
            if len(parts) >= 2:
                major, minor = int(parts[0]), int(parts[1])
                return f"{major}.{minor + 1}"
            else:
                return "1.0"
        except:
            return "1.0"

    async def _get_active_version(self, template_id: str, language: str) -> Optional[str]:
        """获取激活版本号"""
        query = """
            SELECT version FROM template_versions
            WHERE template_id = ? AND language = ? AND status = ?
            ORDER BY created_at DESC
            LIMIT 1
        """
        result = await self.db_manager.get_record(query, (template_id, language, TemplateStatus.ACTIVE.value))
        return result["version"] if result else None

    async def _save_template_version(self, template_version: TemplateVersion):
        """保存模板版本到数据库"""
        query = """
            INSERT INTO template_versions (
                template_id, version, content, variables, category, language,
                status, created_by, created_at, updated_at, description, tags, metadata
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            template_version.template_id,
            template_version.version,
            template_version.content,
            json.dumps(template_version.variables),
            template_version.category,
            template_version.language,
            template_version.status.value,
            template_version.created_by,
            template_version.created_at,
            template_version.updated_at,
            template_version.description,
            json.dumps(template_version.tags),
            json.dumps(template_version.metadata)
        )
        await self.db_manager.execute_update(query, params)

    async def _save_ab_test(self, ab_test: ABTestConfig):
        """保存A/B测试配置到数据库"""
        query = """
            INSERT INTO ab_tests (
                test_id, test_name, template_id, variant_a_version, variant_b_version,
                traffic_split, start_time, end_time, status, target_metrics,
                created_by, created_at, description
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            ab_test.test_id,
            ab_test.test_name,
            ab_test.template_id,
            ab_test.variant_a_version,
            ab_test.variant_b_version,
            ab_test.traffic_split,
            ab_test.start_time,
            ab_test.end_time,
            ab_test.status.value,
            json.dumps(ab_test.target_metrics),
            ab_test.created_by,
            ab_test.created_at,
            ab_test.description
        )
        await self.db_manager.execute_update(query, params)

    async def _get_active_ab_test(self, template_id: str) -> Optional[ABTestConfig]:
        """获取正在运行的A/B测试"""
        query = """
            SELECT * FROM ab_tests
            WHERE template_id = ? AND status = ? AND start_time <= ? AND end_time > ?
            ORDER BY created_at DESC
            LIMIT 1
        """
        now = datetime.now()
        result = await self.db_manager.get_record(query, (template_id, ABTestStatus.RUNNING.value, now, now))

        if result:
            return self._row_to_ab_test(result)
        return None

    async def _get_ab_test(self, test_id: str) -> Optional[ABTestConfig]:
        """获取A/B测试配置"""
        if test_id in self._ab_test_cache:
            return self._ab_test_cache[test_id]

        query = "SELECT * FROM ab_tests WHERE test_id = ?"
        result = await self.db_manager.get_record(query, (test_id,))

        if result:
            ab_test = self._row_to_ab_test(result)
            self._ab_test_cache[test_id] = ab_test
            return ab_test
        return None

    async def _record_ab_test_participation(self, test_id: str, user_id: str, variant_used: str, template_id: str, template_version: str):
        """记录A/B测试参与"""
        # 记录用户参与测试的信息，用于后续分析
        self.logger.debug(f"用户 {user_id} 参与A/B测试 {test_id}，使用变体 {variant_used}，模板 {template_id} v{template_version}")

    async def _record_ab_test_result(self, test_id: str, user_id: str, variant_used: str, template_id: str, template_version: str, success: bool, response_time: float, user_satisfaction: int, session_id: str):
        """记录A/B测试结果"""
        query = """
            INSERT INTO ab_test_results (
                test_id, user_id, variant_used, template_id, template_version,
                success, response_time, user_satisfaction, timestamp, session_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            test_id, user_id, variant_used, template_id, template_version,
            success, response_time, user_satisfaction, datetime.now(), session_id
        )
        await self.db_manager.execute_update(query, params)

    async def _update_template_metrics(self, template_id: str, version: str, success: bool, response_time: float, user_satisfaction: int):
        """更新模板指标"""
        today = datetime.now().date()

        # 尝试更新现有记录
        update_query = """
            UPDATE template_metrics
            SET usage_count = usage_count + 1,
                success_count = success_count + ?,
                error_count = error_count + ?,
                user_satisfaction_score = (user_satisfaction_score * (usage_count - 1) + ?) / usage_count,
                avg_response_time = (avg_response_time * (usage_count - 1) + ?) / usage_count,
                last_used = ?
            WHERE template_id = ? AND version = ? AND date_recorded = ?
        """

        success_increment = 1 if success else 0
        error_increment = 0 if success else 1
        satisfaction_score = user_satisfaction or 0
        response_time_value = response_time or 0

        params = (
            success_increment, error_increment, satisfaction_score, response_time_value,
            datetime.now(), template_id, version, today
        )

        rows_affected = await self.db_manager.execute_update(update_query, params)

        # 如果没有更新任何行，插入新记录
        if rows_affected == 0:
            insert_query = """
                INSERT INTO template_metrics (
                    template_id, version, usage_count, success_count, error_count,
                    user_satisfaction_score, avg_response_time, last_used, date_recorded
                ) VALUES (?, ?, 1, ?, ?, ?, ?, ?, ?)
            """
            insert_params = (
                template_id, version, success_increment, error_increment,
                satisfaction_score, response_time_value, datetime.now(), today
            )
            await self.db_manager.execute_update(insert_query, insert_params)

    def _row_to_template_version(self, row) -> TemplateVersion:
        """将数据库行转换为TemplateVersion对象"""
        return TemplateVersion(
            template_id=row["template_id"],
            version=row["version"],
            content=row["content"],
            variables=json.loads(row["variables"]) if row["variables"] else [],
            category=row["category"],
            language=row["language"],
            status=TemplateStatus(row["status"]),
            created_by=row["created_by"],
            created_at=datetime.fromisoformat(row["created_at"]),
            updated_at=datetime.fromisoformat(row["updated_at"]),
            description=row["description"] or "",
            tags=json.loads(row["tags"]) if row["tags"] else [],
            metadata=json.loads(row["metadata"]) if row["metadata"] else {}
        )

    def _row_to_ab_test(self, row) -> ABTestConfig:
        """将数据库行转换为ABTestConfig对象"""
        return ABTestConfig(
            test_id=row["test_id"],
            test_name=row["test_name"],
            template_id=row["template_id"],
            variant_a_version=row["variant_a_version"],
            variant_b_version=row["variant_b_version"],
            traffic_split=row["traffic_split"],
            start_time=datetime.fromisoformat(row["start_time"]),
            end_time=datetime.fromisoformat(row["end_time"]),
            status=ABTestStatus(row["status"]),
            target_metrics=json.loads(row["target_metrics"]) if row["target_metrics"] else [],
            created_by=row["created_by"],
            created_at=datetime.fromisoformat(row["created_at"]),
            description=row["description"] or ""
        )

    def _clear_template_cache(self, template_id: str):
        """清除模板缓存"""
        keys_to_remove = [key for key in self._template_cache.keys() if key.startswith(f"{template_id}_")]
        for key in keys_to_remove:
            del self._template_cache[key]

    def _calculate_confidence(self, variant_a_stats: Dict, variant_b_stats: Dict) -> float:
        """计算A/B测试置信度（简化版本）"""
        # 这里使用简化的置信度计算，实际应用中可能需要更复杂的统计方法
        a_rate = variant_a_stats["success_rate"]
        b_rate = variant_b_stats["success_rate"]
        a_count = variant_a_stats["total_users"]
        b_count = variant_b_stats["total_users"]

        if a_count < 30 or b_count < 30:
            return 0.0  # 样本量太小

        # 简化的置信度计算
        rate_diff = abs(a_rate - b_rate)
        if rate_diff > 0.1:  # 10%以上差异
            return 0.95
        elif rate_diff > 0.05:  # 5%以上差异
            return 0.80
        else:
            return 0.50

    def _get_test_recommendation(self, variant_stats: Dict, confidence: float) -> str:
        """获取测试建议"""
        if not variant_stats or len(variant_stats) != 2:
            return "数据不足，无法给出建议"

        variants = list(variant_stats.keys())
        variant_a_stats = variant_stats[variants[0]]
        variant_b_stats = variant_stats[variants[1]]

        if confidence and confidence > 0.8:
            if variant_a_stats["success_rate"] > variant_b_stats["success_rate"]:
                return f"建议采用变体 {variants[0]}，成功率更高（置信度: {confidence:.1%}）"
            else:
                return f"建议采用变体 {variants[1]}，成功率更高（置信度: {confidence:.1%}）"
        else:
            return "测试结果不够显著，建议继续测试或增加样本量"
