"""
LLM配置集中管理器

功能：
1. 集中管理所有LLM模型配置
2. 为各Agent提供模型配置查询服务
3. 管理Agent与模型的映射关系
4. 确保模型配置的一致性和可维护性

使用方法:
1. 在settings.py中添加模型配置
2. 使用set_model_for_agent()设置Agent模型映射
3. 通过get_model_config()获取配置
4. 配置参数会传递给LLM调用接口

配置示例(在settings.py中):
LLM_CONFIGS = {
    "deepseek-chat": {
        "provider": "deepseek",
        "api_key": "your_api_key",
        "api_base": "https://api.deepseek.com/v1",
        "model_name": "deepseek-chat",
        "temperature": 0.7,
        "max_tokens": 2000,
        "top_p": 1.0
    },
    "gpt-3.5-turbo": {
        "provider": "openai",
        "api_key": "your_api_key",
        "api_base": "https://api.openai.com/v1",
        "model_name": "gpt-3.5-turbo",
        "temperature": 0.7,
        "max_tokens": 2000,
        "top_p": 1.0
    }
}
"""

from backend.config.settings import LLM_CONFIGS

class LLMConfigManager:
    def __init__(self):
        from ..config.settings import DEFAULT_MODEL
        self.DEFAULT_MODEL = DEFAULT_MODEL  # 使用settings中的默认配置
        from ..config.settings import LLM_CONFIGS, SCENARIO_LLM_MAPPING
        self.config = LLM_CONFIGS
        self.agent_model_mapping = SCENARIO_LLM_MAPPING  # 使用settings中的映射关系
        
        # 确保默认模型已配置
        if self.DEFAULT_MODEL not in self.config:
            raise ValueError(f"默认模型 {self.DEFAULT_MODEL} 未在配置中定义")

    def set_model_for_agent(self, agent_name: str, model_name: str):
        """为指定Agent设置模型
        参数:
            agent_name: Agent名称(如"domain_classifier"、"intent_recognition"等)
            model_name: 要使用的模型名称
        
        当前系统中已定义的Agent名称:
        - domain_classifier: 领域分类器
        - intent_recognition: 意图识别器

        - information_extractor: 信息提取器
        - conversation_flow: 对话流程管理器
        - user_interaction: 用户交互处理器
        - value_extractor: 值提取器
        - question_polisher: 问题润色器
        """
        if agent_name not in ["domain_classifier", "intent_recognition", "information_extractor", "conversation_flow", "user_interaction", "value_extractor", "question_polisher"]:
            raise ValueError(f"未知的Agent名称: {agent_name}")
        if model_name not in self.config:
            raise ValueError(f"未配置的模型: {model_name}")
        self.agent_model_mapping[agent_name] = model_name

    def get_model_config(self, model_name: str = None, agent_name: str = None) -> dict:
        """获取模型配置
        参数:
            model_name: 直接指定模型名称(可选)
            agent_name: 通过Agent名称获取配置(可选)
        返回:
            模型配置字典
            
        当找不到指定模型时，将返回默认模型配置
        """
        # 优先使用agent_name获取配置
        if agent_name and agent_name in self.agent_model_mapping:
            model_name = self.agent_model_mapping[agent_name]
        
        # 如果没有提供model_name且agent_name也没有映射到model_name
        if not model_name:
            model_name = self.DEFAULT_MODEL
            
        # 如果模型未配置，使用默认模型
        if model_name not in self.config:
            model_name = self.DEFAULT_MODEL
            
        return self.config[model_name]

    def get_model_info(self) -> list:
        """获取所有可用模型名称列表
        
        返回:
            list: 可用模型名称列表
        """
        return list(self.config.keys())

    def create_for_model(self, model_name: str) -> dict:
        """根据模型名称创建配置(兼容LLMFactory接口)
        
        参数:
            model_name: 模型名称
            
        返回:
            dict: 模型配置字典
            
        抛出:
            ValueError: 当模型未找到时
        """
        if model_name not in self.config:
            raise ValueError(f"未找到模型 '{model_name}' 的配置")
        return self.config[model_name]
