"""
AutoGen兼容的基础Agent类
"""
from typing import Dict, Any, Optional, List, Union, Tuple
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from autogen import ConversableAgent
from backend.utils.logging_config import get_logger

class AutoGenBaseAgent(ConversableAgent):
    """
    所有AutoGen Agent的基类，继承自autogen.AgentBase
    保留现有BaseAgent的核心功能并集成AutoGen特性
    """
    def __init__(self, name: str, **kwargs):
        """
        初始化Agent实例
        """
        # 如果没有提供llm_config，则禁用AutoGen的默认LLM配置
        if 'llm_config' not in kwargs or kwargs['llm_config'] is None:
            kwargs['llm_config'] = False  # 使用False而不是None来完全禁用LLM
        super().__init__(name=name, **kwargs)
        self.state: Dict[str, Any] = {}
        self._oai_client = None  # 禁用内置的OpenAI客户端

        # 初始化日志记录器
        self.logger = get_logger(f"autogen_agent.{self.name}")
        self.logger.info(f"AutoGen {self.name} Agent已初始化")

    def ensure_response_format(func):
        """
        装饰器：确保响应消息格式正确
        功能：
        1. 自动添加"正在处理您的请求"前缀
        2. 捕获异常并返回标准化错误格式
        3. 记录错误日志
        """
        async def wrapper(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
            # 调用原始方法
            try:
                response = await func(self, message, context)
                if not response.startswith("正在处理您的请求"):
                    response = "正在处理您的请求。\n" + response
                return response
            except Exception as e:
                error_msg = f"处理消息时出错: {str(e)}"
                self.logger.error(error_msg)
                return f"正在处理您的请求。\n抱歉，{error_msg}"
        return wrapper

    def update_state(self, key: str, value: Any) -> None:
        """
        更新Agent内部状态存储

        Args:
            key: 状态项的标识符
            value: 要存储的值
        """
        self.logger.debug(f"更新状态: {key} = {value}")
        self.state[key] = value

    def get_state(self, key: str, default: Any = None) -> Any:
        """
        获取Agent内部状态值

        Args:
            key: 状态项的标识符
            default: 当键不存在时的默认值

        Returns:
            存储在状态中的值
        """
        value = self.state.get(key, default)
        self.logger.debug(f"获取状态: {key} = {value}")
        return value

    async def process_message(self, message: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        处理AutoGen格式的消息（需由子类实现）

        标准消息格式:
        {
            "content": "消息内容",
            "context": {},  # 上下文信息
            "type": "message_type",  # 消息类型
            "sender": "agent_name"   # 发送者
        }

        标准响应格式:
        {
            "status": "success|error",
            "content": "响应内容",
            "context": {},  # 更新后的上下文
            "type": "response_type"
        }

        注意事项:
        1. 子类实现必须处理所有可能的异常情况
        2. 应使用create_response方法创建标准响应
        3. 重要状态变更应通过update_state方法记录
        4. 所有关键操作都应记录日志
        """
        raise NotImplementedError("子类必须实现process_message方法")

    def create_message(self, content: str, context: Optional[Dict[str, Any]] = None,
                     msg_type: str = "default", sender: str = None) -> Dict[str, Any]:
        """
        创建标准格式的消息

        Args:
            content: 消息内容
            context: 上下文信息
            msg_type: 消息类型
            sender: 发送者名称

        Returns:
            标准消息字典
        """
        return {
            "content": content,
            "context": context or {},
            "type": msg_type,
            "sender": sender or self.name
        }

    def create_response(self, content: str, context: Optional[Dict[str, Any]] = None,
                      status: str = "success", resp_type: str = "default") -> Dict[str, Any]:
        """
        创建标准格式的响应

        Args:
            content: 响应内容
            context: 上下文信息
            status: 状态(success/error)
            resp_type: 响应类型

        Returns:
            标准响应字典
        """
        return {
            "status": status,
            "content": content,
            "context": context or {},
            "type": resp_type
        }
