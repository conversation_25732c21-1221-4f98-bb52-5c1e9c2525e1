"""负责知识库的查询"""
import json
import logging
from typing import Any, Dict, List, Optional
import autogen
from ..utils.db_optimizer import DBOptimizer
from ..config import settings

# 负责知识库查询
class KnowledgeBaseAgent(autogen.ConversableAgent):
    
    # 初始化KnowledgeBaseAgent
    def __init__(self, db_path: str = None):
        """
            Args:
            db_path (str, optional): 数据库路径。默认为 settings.DATABASE_PATH。
        """
        super().__init__(
            name="KnowledgeBase",
            human_input_mode="NEVER"  # 不需要人工输入
        )
        # 数据库优化器，用于执行SQL查询
        self.db_optimizer = DBOptimizer(
            db_path=db_path or str(settings.DATABASE_PATH)
        )
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.DEBUG)  # 临时设置为DEBUG级别
        self.register_reply([autogen.Agent, None], self.process_query)

    # 处理知识库查询请求。
    def process_query(
        self,
        recipient: autogen.Agent,
        messages: Optional[List[Dict]] = None,
        sender: Optional[autogen.Agent] = None,
        config: Optional[Any] = None,
    ) -> Dict[str, Any]:
        """
         Args:
            recipient (autogen.Agent): 接收消息的Agent。
            messages (Optional[List[Dict]], optional): 消息列表。默认为 None。
            sender (Optional[autogen.Agent], optional): 发送消息的Agent。默认为 None。
            config (Optional[Any], optional): 配置信息。默认为 None。

        Returns:
            Dict[str, Any]: 查询结果。
        """
        if not messages or len(messages) == 0:
            raise ValueError("查询消息不能为空")
            
        query = messages[-1]["content"]
        # 查询知识库
        try:
            # 解析查询参数
            params = self._parse_query(query)
            
            # 执行知识库查询
            query = """
                SELECT fp.focus_id, fp.name, fp.description, fp.priority, fp.example,
                       c.name as category_name, d.name as domain_name
                FROM focus_point_definitions fp
                JOIN categories c ON fp.category_id = c.category_id
                JOIN domains d ON c.domain_id = d.domain_id
                WHERE d.name LIKE :domain
                AND c.name LIKE :category
                AND (fp.name LIKE :keywords OR fp.description LIKE :keywords)
                LIMIT :limit
            """
            results = self.db_optimizer.execute_query(
                query,
                {
                    "domain": f"%{params.get('domain', '')}%",
                    "category": f"%{params.get('category', '')}%",
                    "keywords": f"%{params.get('keywords', [''])[0]}%",
                    "limit": params.get("limit", 5)
                }
            )

            return {
                "content": self._format_results(results),
                "role": "assistant"
            }
        except Exception as e:
            self.logger.error(f"知识库查询失败: {str(e)}")
            return {
                "content": f"知识库查询失败: {str(e)}",
                "role": "error"
            }

    # 解析查询参数
    def _parse_query(self, query: str) -> Dict[str, Any]:
        """
        Args:
            query (str): 查询字符串。
        Returns:
            Dict[str, Any]: 解析后的查询参数。
        """
        return {"keywords": query.split()}

    # 格式化查询结果
    def _format_results(self, results: List[Dict]) -> str:
        """
        Args:
            results (List[Dict]): 查询结果列表。
        Returns:
            str: 格式化后的查询结果。
        """
        if not results:
            return "没有找到匹配的知识库记录"
            
        formatted = []
        for idx, result in enumerate(results, 1):
            formatted.append(
                f"{idx}. {result.get('name', '无标题')}\n"
                f"   领域: {result.get('domain_name', '未知')}\n"
                f"   分类: {result.get('category_name', '未知')}\n"
                f"   描述: {result.get('description', '无描述')}\n"
                f"   优先级: {result.get('priority', 'P2')}\n"
                f"   示例: {result.get('example', '无示例')}"
            )
        return "\n\n".join(formatted)

    # 获取所有领域
    def get_domains(self) -> List[Dict[str, Any]]:
        """
         Returns:
            List[Dict[str, Any]]: 领域列表。
        """
        query = """
            SELECT domain_id, name, description
            FROM domains
            ORDER BY name
        """
        
        try:
            return self.db_optimizer.execute_query(query)
        except Exception as e:
            self.logger.error(f"获取领域列表失败: {str(e)}")
            return []

    # 获取指定领域的所有类别
    def get_categories(self, domain_id: str) -> List[Dict[str, Any]]:
        """
        Args:
            domain_id (str): 领域ID。
        Returns:
            List[Dict[str, Any]]: 类别列表。
        """

        query = """
            SELECT category_id, name, description
            FROM categories
            WHERE domain_id = :domain_id
            ORDER BY name
        """
        
        try:
            return self.db_optimizer.execute_query(query, {"domain_id": domain_id})
        except Exception as e:
            self.logger.error(f"获取类别列表失败: {str(e)}")
            return []

    # 获取指定类别的所有关注点
    def get_concern_points(self, category_id: str) -> List[Dict[str, Any]]:
        """
        Args:
            category_id (str): 类别ID。

        Returns:
            List[Dict[str, Any]]: 关注点列表。
        """
        query = """
            SELECT 
                fp.focus_id as id,
                fp.name,
                fp.description,
                fp.priority,
                fp.example,
                fp.required,
                c.name as category_name,
                d.name as domain_name
            FROM focus_point_definitions fp
            JOIN categories c ON fp.category_id = c.category_id
            JOIN domains d ON c.domain_id = d.domain_id
            WHERE fp.category_id = :category_id
            ORDER BY 
                CASE fp.priority
                    WHEN 'P0' THEN 1
                    WHEN 'P1' THEN 2
                    WHEN 'P2' THEN 3
                    ELSE 4
                END,
                fp.name
        """
        
        try:
            self.logger.debug(f"开始获取关注点 - category_id: {category_id}")
            self.logger.debug(f"完整SQL查询:\n{query}")
            
            results = self.db_optimizer.execute_query(query, {"category_id": category_id})
            
            self.logger.debug(f"查询结果数量: {len(results)}")
            if results:
                self.logger.debug(f"第一个关注点示例: {json.dumps(results[0], ensure_ascii=False)}")
            
            return results
        except Exception as e:
            self.logger.error(f"获取关注点列表失败 - category_id: {category_id}, 错误: {str(e)}", exc_info=True)
            return []
