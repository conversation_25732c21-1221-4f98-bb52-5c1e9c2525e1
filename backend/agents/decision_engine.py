import yaml
import logging
from typing import Dict, Any, Optional

class DecisionEngine:
    """
    决策引擎 v2.0 - 根据用户意图、情感状态和对话上下文选择最佳响应策略。
    从外部YAML文件加载策略，并采用级联回退逻辑进行决策。
    """

    def __init__(self, strategies_path: str = 'backend/config/strategies.yaml'):
        """
        初始化决策引擎并从外部文件加载策略。
        """
        self.logger = logging.getLogger(__name__)
        self.strategies_config = {}
        self.DEFAULT_STRATEGY = {
            "action": "handle_unknown_situation",
            "priority": 0,
            "prompt_instruction": "保持中性、专业的语气进行回应。"
        }
        try:
            with open(strategies_path, 'r', encoding='utf-8') as f:
                self.strategies_config = yaml.safe_load(f)
            # 加载后，将默认策略也设置为配置文件中定义的，如果存在的话
            self.DEFAULT_STRATEGY = self.strategies_config.get('DEFAULT_STRATEGY', self.DEFAULT_STRATEGY)
            self.logger.info(f"成功从 {strategies_path} 加载决策策略。")
        except FileNotFoundError:
            self.logger.error(f"策略文件 {strategies_path} 未找到！将使用内置的默认策略。")
        except Exception as e:
            self.logger.error(f"加载或解析策略文件时出错: {e}")

    def get_strategy(self, intent: str, emotion: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        获取最适合当前对话状态的策略，采用级联回退逻辑。

        查找顺序:
        1. 状态特定策略: (state, intent, emotion)
        2. 状态特定回退至neutral情感: (state, intent, 'neutral')
        3. 全局策略: (GLOBAL, intent, emotion)
        4. 全局回退至neutral情感: (GLOBAL, intent, 'neutral')
        5. 最终默认策略
        """
        context = context or {}
        state = context.get("current_state", "GLOBAL")

        keys_to_try = [
            (state, intent, emotion),
            (state, intent, 'neutral'),
            ("GLOBAL", intent, emotion),
            ("GLOBAL", intent, 'neutral')
        ]

        for s, i, e in keys_to_try:
            try:
                strategy = self.strategies_config.get(s, {}).get(i, {}).get(e)
                if strategy:
                    self.logger.debug(f"策略匹配成功: ({s}, {i}, {e}) -> action: {strategy.get('action')}")
                    return strategy
            except AttributeError: # 如果配置的层级不存在，跳过
                continue
        
        self.logger.debug(f"所有查找均未命中，返回默认策略。")
        return self.DEFAULT_STRATEGY

    def get_prompt_instruction(self, intent: str, emotion: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        辅助函数，直接获取最终策略中的 prompt_instruction。
        """
        strategy = self.get_strategy(intent, emotion, context)
        return strategy["prompt_instruction"]
