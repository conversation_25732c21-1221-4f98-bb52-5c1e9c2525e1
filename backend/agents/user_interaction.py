"""AutoGen兼容版本的UserInteractionAgent"""
import logging
from typing import Any, Dict, Optional, List
import autogen
from enum import Enum, auto

class ConversationState(Enum):
    """对话状态枚举"""
    INITIAL = auto()
    COLLECTING = auto()
    GENERATING_DOCUMENT = auto()
    REVIEWING = auto()
    COMPLETED = auto()

class UserInteractionAgent(autogen.ConversableAgent):
    """负责处理用户交互的AutoGen Agent"""
    
    def __init__(self):
        super().__init__(
            name="UserInteraction",
            human_input_mode="ALWAYS"  # 总是需要人工输入
        )
        self.logger = logging.getLogger(__name__)
        self.current_state = ConversationState.INITIAL
        self.current_document = None
        self.register_reply([autogen.Agent, None], self.process_message)

    async def process_message(
        self,
        recipient: autogen.Agent,
        messages: Optional[List[Dict]] = None,
        sender: Optional[autogen.Agent] = None,
        config: Optional[Any] = None,
    ) -> Dict[str, Any]:
        """处理用户输入消息"""
        if not messages or len(messages) == 0:
            raise ValueError("消息不能为空")
            
        message = messages[-1]["content"]
        
        try:
            # 根据当前状态处理消息
            if self.current_state == ConversationState.INITIAL:
                response = "欢迎使用需求采集系统！请描述您的需求。"
                self.current_state = ConversationState.COLLECTING
            elif self.current_state == ConversationState.COLLECTING:
                response = "已记录您的需求。正在处理中..."
                self.current_state = ConversationState.GENERATING_DOCUMENT
            elif self.current_state == ConversationState.GENERATING_DOCUMENT:
                response = "这是根据您的需求生成的文档草案:\n\n[示例文档内容]"
                self.current_document = "[示例文档内容]"
                self.current_state = ConversationState.REVIEWING
            elif self.current_state == ConversationState.REVIEWING:
                response = "已收到您的反馈。正在更新文档..."
                self.current_state = ConversationState.COMPLETED
            else:
                response = "需求采集已完成。感谢您的参与！"

            return {
                "content": response,
                "role": "assistant"
            }
        except Exception as e:
            self.logger.error(f"处理消息时发生错误: {str(e)}")
            return {
                "content": f"处理消息时发生错误: {str(e)}",
                "role": "error"
            }

    async def send_response(self, message: str) -> None:
        """向用户发送响应"""
        await self.send(message, self, request_reply=True)

    def get_state(self) -> Dict[str, Any]:
        """获取当前会话状态"""
        return {
            "current_state": self.current_state.name,
            "current_document": self.current_document
        }

    async def display_document(self) -> str:
        """展示当前文档"""
        if not self.current_document:
            return "当前没有可展示的文档。"
        return f"{self.current_document}\n\n---\n\n请对文档进行审核：\n\n* 如果文档内容准确，请回复\"确认\"或\"没有问题\"\n* 如果需要修改，请明确指出要修改的部分和新内容"

    async def process_feedback(self, feedback: str) -> str:
        """处理用户对文档的反馈"""
        if not self.current_document:
            return "当前没有文档需要处理反馈。"
            
        if feedback.lower() in ["确认", "没有问题"]:
            self.current_state = ConversationState.COMPLETED
            return "文档已确认。需求采集流程已完成。"
        else:
            self.current_document = f"修改后的文档:\n\n{feedback}"
            return "已根据您的反馈更新文档。"
