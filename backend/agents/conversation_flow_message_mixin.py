#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ConversationFlow消息方法混入类
将所有_get_*_message方法从主类中分离出来

本模块包含了ConversationFlow中所有的消息获取方法，
这些方法原本分散在主类中，现在统一管理以提高代码组织性。

主要功能：
- 提供18个标准化的消息获取方法
- 统一使用新的回复系统接口
- 保持向后兼容性
- 支持上下文相关的动态消息生成
"""

from typing import Dict, Any


class ConversationFlowMessageMixin:
    """
    ConversationFlow消息方法混入类

    这个混入类包含了ConversationFlow中所有的消息获取方法，
    将原本分散在主类中的18个_get_*_message方法集中管理。

    核心特点：
    - 统一接口：所有方法都使用_get_reply()统一接口
    - 异步支持：所有方法都是异步的，支持动态回复生成
    - 上下文感知：支持传递上下文信息进行个性化回复
    - 向后兼容：保持原有方法签名和行为

    包含的消息类型：
    - 系统状态消息：重置确认、错误提示等
    - 用户交互消息：问候、澄清请求等
    - 文档相关消息：生成完成、修改错误等
    - 领域相关消息：领域错误、分类错误等

    使用方式：
    通过多重继承混入到ConversationFlow主类中：
    ```python
    class AutoGenConversationFlowAgent(AutoGenBaseAgent, ConversationFlowMessageMixin):
        # 可以直接调用所有_get_*_message方法
        msg = await self._get_greeting_message()
    ```

    设计优势：
    - 代码分离：将消息方法从主类中分离，减少主类复杂度
    - 易于维护：消息相关的修改只需在此文件中进行
    - 可复用：其他需要类似消息功能的类也可以使用此混入
    - 清晰结构：消息方法集中管理，便于查找和修改

    依赖：
    - 需要主类实现_get_reply()方法（由ConversationFlowReplyMixin提供）
    - 需要访问self.latest_domain_result和self.latest_category_result属性
    """

    # ==================== 系统状态相关消息 ====================

    async def _get_reset_confirmation_message(self) -> str:
        """
        获取重置确认消息

        用于确认会话已成功重置，提示用户可以开始新的对话。

        返回:
            str: 重置确认消息
        """
        return await self._get_reply("reset_confirmation")

    async def _get_system_error_message(self) -> str:
        """
        获取系统错误消息

        当系统遇到内部错误时显示的通用错误提示。

        返回:
            str: 系统错误消息
        """
        return await self._get_reply("system_error")

    async def _get_modification_error_message(self) -> str:
        """
        获取修改错误消息

        当文档修改操作失败时显示的错误提示。

        返回:
            str: 修改错误消息
        """
        return await self._get_reply("modification_error")

    # ==================== 文档相关消息 ====================

    async def _get_document_finalized_message(self) -> str:
        """
        获取文档最终确认消息

        当用户确认文档内容，文档被最终确定时的提示消息。

        返回:
            str: 文档确认消息
        """
        return await self._get_reply("document_finalized")

    async def _get_document_generated_message(self) -> str:
        """
        获取文档生成完成消息

        当需求文档生成完成，等待用户确认时的提示消息。

        返回:
            str: 文档生成完成消息
        """
        return await self._get_reply("document_generated")

    async def _get_document_generation_failed_message(self) -> str:
        """
        获取文档生成失败消息

        当文档生成过程失败时的错误提示和后续操作建议。

        返回:
            str: 文档生成失败消息
        """
        return await self._get_reply("document_generation_failed")

    async def _get_document_generator_not_initialized_message(self) -> str:
        """
        获取文档生成器未初始化消息

        当文档生成器组件未正确初始化时的错误提示。

        返回:
            str: 生成器未初始化消息
        """
        return await self._get_reply("document_generator_not_initialized")

    async def _get_document_not_found_message(self) -> str:
        """
        获取文档未找到消息

        当无法找到生成的文档时的错误提示。

        返回:
            str: 文档未找到消息
        """
        return await self._get_reply("document_not_found")

    # ==================== 用户交互相关消息 ====================

    async def _get_greeting_message(self) -> str:
        """
        获取问候消息

        用于欢迎用户并询问如何提供帮助的标准问候语。

        返回:
            str: 问候消息
        """
        return await self._get_reply("greeting")

    async def _get_default_requirement_prompt(self) -> str:
        """
        获取默认需求提示消息

        当需要用户提供更多需求信息时的标准提示。

        返回:
            str: 需求提示消息
        """
        return await self._get_reply("default_requirement_prompt")

    async def _get_initial_guidance_message(self) -> str:
        """
        获取初始引导消息

        为新用户提供的初始使用指导和示例。

        返回:
            str: 初始引导消息
        """
        return await self._get_reply("initial_guidance")
    
    async def _get_clarification_request_message(self) -> str:
        """
        获取澄清请求消息

        当需要用户进一步澄清或详细说明需求时的提示消息。

        返回:
            str: 澄清请求消息
        """
        return await self._get_reply("clarification_request")

    async def _get_document_refinement_message(self) -> str:
        """
        获取文档细化消息

        当文档需要进一步细化或修改时的提示消息。

        返回:
            str: 文档细化消息
        """
        return await self._get_reply("document_refinement")

    async def _get_specific_requirement_help_message(self) -> str:
        """
        获取具体需求帮助消息

        引导用户提供更具体、详细需求信息的帮助提示。

        返回:
            str: 具体需求帮助消息
        """
        return await self._get_reply("specific_requirement_help")

    # ==================== 领域和分类相关消息 ====================

    async def _get_domain_category_error_message(self) -> str:
        """
        获取领域分类错误消息

        当无法为特定领域和分类提供指引时的错误提示。
        会动态包含当前的领域名称和分类名称。

        返回:
            str: 包含具体领域和分类信息的错误消息
        """
        domain_name = self.latest_domain_result.get('name', self.current_domain) if self.latest_domain_result else self.current_domain
        category_name = self.latest_category_result.get('name', self.current_category) if self.latest_category_result else self.current_category
        context = {
            "domain_name": domain_name,
            "category_name": category_name
        }
        return await self._get_reply("domain_category_error", context)

    async def _get_domain_info_error_message(self) -> str:
        """
        获取领域信息错误消息

        当无法获取领域相关信息时的错误提示。

        返回:
            str: 领域信息错误消息
        """
        return await self._get_reply("domain_info_error")

    async def _get_domain_info_fetch_error_message(self) -> str:
        """
        获取领域信息获取错误消息

        当获取领域信息的过程中发生错误时的提示消息。

        返回:
            str: 领域信息获取错误消息
        """
        return await self._get_reply("domain_info_fetch_error")

    # ==================== 通用错误和处理消息 ====================

    async def _get_processing_error_message(self, error_msg: str) -> str:
        """
        获取处理错误消息

        当处理用户请求时发生错误，需要显示具体错误信息的消息。

        参数:
            error_msg (str): 具体的错误信息

        返回:
            str: 包含错误详情的处理错误消息
        """
        return await self._get_reply("processing_error", {"error_msg": error_msg})

    async def _get_unknown_action_message(self) -> str:
        """
        获取未知操作消息

        当系统遇到未知或未定义的操作指令时的提示消息。

        返回:
            str: 未知操作消息
        """
        return await self._get_reply("unknown_action")
