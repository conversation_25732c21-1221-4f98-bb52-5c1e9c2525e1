"""意图识别Agent - v2.0 (修复和优化版)"""
import logging
import json
import re
from typing import Any, Dict, List, Optional
import autogen
from dataclasses import dataclass

@dataclass
class IntentResult:
    """包含了意图和情感的意图识别结果。"""
    intent: str
    emotion: str
    confidence: float
    entities: Dict[str, Any]

class IntentRecognitionAgent(autogen.ConversableAgent):
    """
    一个负责意图识别的AutoGen Agent。
    它接收用户消息和上下文，并返回一个结构化的意图结果。
    """

    def __init__(self, llm_service: Any, agent_name: str = "intent_recognition_agent"):
        super().__init__(
            name="IntentRecognitionAgent",
            human_input_mode="NEVER"
        )
        self.llm_service = llm_service
        self.llm_client = llm_service  # 添加llm_client别名以保持兼容性
        self.agent_name = agent_name
        self.logger = logging.getLogger(__name__)

        # 初始化提示词加载器
        from backend.utils.prompt_loader import PromptLoader
        self.prompt_loader = PromptLoader()

        # 注意: 注册 process_message 作为回复处理函数
        self.register_reply([autogen.Agent, None], self.process_message)

    async def process_message(
        self,
        recipient: autogen.Agent,
        messages: Optional[List[Dict]] = None,
        sender: Optional[autogen.Agent] = None,
        config: Optional[Any] = None,
    ) -> Dict[str, Any]:
        """处理来自其他Agent的意图识别请求。"""
        if not messages:
            raise ValueError("意图识别消息不能为空")

        # 假设调度器发送的内容是一个包含'message'和'context'的字典
        last_message_content = messages[-1].get("content", {})
        if not isinstance(last_message_content, dict):
            # 兼容纯字符串消息
             user_message = str(last_message_content)
             context_data = []
        else:
             user_message = last_message_content.get("message", "")
             context_data = last_message_content.get("context", [])


        try:
            intent_result = await self.recognize_intent(user_message, context_data)
            # 返回一个符合AutoGen标准的回复字典
            return {
                "content": json.dumps({
                    "intent": intent_result.intent,
                    "emotion": intent_result.emotion,
                    "confidence": intent_result.confidence,
                    "entities": intent_result.entities
                }),
                "role": "assistant" # 使用 'assistant' 或其他合适的角色
            }
        except Exception as e:
            self.logger.error(f"在 process_message 中意图识别失败: {str(e)}", exc_info=True)
            return {"content": json.dumps({"error": f"意图识别失败: {str(e)}"}), "role": "assistant"}

    def _extract_json(self, text: str) -> Optional[dict]:
        """
        从可能包含额外文本的字符串中稳健地提取第一个有效的JSON对象。
        """
        if not text or not isinstance(text, str):
            return None
        
        # 匹配被```json ... ```包裹的代码块
        match = re.search(r'```json\s*(\{.*?\})\s*```', text, re.DOTALL)
        if match:
            try:
                return json.loads(match.group(1))
            except json.JSONDecodeError:
                pass # 如果解析失败，继续尝试其他方法

        # 寻找第一个 '{' 和最后一个 '}'
        start_index = text.find('{')
        end_index = text.rfind('}')
        if start_index != -1 and end_index > start_index:
            json_str = text[start_index:end_index+1]
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                self.logger.warning(f"无法从文本中提取有效JSON: {text[:200]}...")
                return None
        
        return None


    async def recognize_intent(self, text: str, conversation_history: List[Dict[str, str]] = None) -> IntentResult:
        """识别用户输入的意图
        
        参数:
            text: 用户输入文本
            conversation_history: 对话历史记录
        
        返回:
            Dict[str, Any]: 意图识别结果
        """
        self.logger.info(f"开始识别意图: '{text}'")
        
        # 获取当前使用的模型信息
        model_config = self.llm_client.config_manager.get_model_config(agent_name=self.agent_name)
        self.logger.info(f"意图识别使用模型: {model_config.get('model_name', 'unknown')}, 提供商: {model_config.get('provider', 'unknown')}")
        
        # 准备对话历史
        formatted_history = self._format_conversation_history(conversation_history) if conversation_history else ""
        
        # 加载提示词
        try:
            prompt = self.prompt_loader.load_prompt(
                "intent_recognition", 
                {
                    "user_input": text,
                    "full_conversation": formatted_history
                }
            )
        except Exception as e:
            # 如果加载失败，使用硬编码的备用模板
            self.logger.warning(f"从文件加载Prompt失败 ('{e}'), 将使用硬编码的备用提示词。")
            prompt = f"""
请分析以下用户输入的意图和情感状态，并以JSON格式返回结果。

用户输入: {text}

对话历史:
{formatted_history}

请返回JSON格式的结果，包含以下字段：
- intent: 用户意图类型
- emotion: 情感状态
- confidence: 置信度(0-1)
- entities: 提取的实体信息

JSON格式示例：
{{
    "intent": "需求描述",
    "emotion": "neutral",
    "confidence": 0.8,
    "entities": {{}}
}}
"""
            
        # 调用LLM
        self.logger.debug(f"发送意图识别请求到LLM，提示词长度: {len(prompt)}")
        response = await self.llm_client.call_llm(
            messages=[{"role": "user", "content": prompt}],
            agent_name=self.agent_name,
            scenario="intent_recognition"
        )
        
        # 记录模型响应信息
        self.logger.info(
            f"LLM响应 - 模型: {response.get('model', 'unknown')}, "
            f"耗时: {response.get('duration', 0):.2f}s, "
            f"Tokens: {response.get('token_usage', {}).get('total_tokens', 'N/A')}"
        )
        
        # 解析响应
        content = response.get("content", "")
        
        # 检查是否使用的是qwen-intent模型
        if "qwen-intent" in model_config.get("model_name", ""):
            self.logger.info("使用通义千问意图识别模型解析结果")
            try:
                # 尝试解析JSON
                intent_data = json.loads(content)
                
                # 转换为标准格式
                if isinstance(intent_data, list) and len(intent_data) > 0:
                    top_intent = intent_data[0]
                    result = IntentResult(
                        intent=top_intent.get("intent_type", "unknown"),
                        confidence=top_intent.get("confidence", 0.0),
                        emotion=top_intent.get("emotion", "neutral"),
                        entities=top_intent.get("entities", {})
                    )
                    self.logger.info(f"意图识别结果: {json.dumps({'intent': result.intent, 'emotion': result.emotion, 'confidence': result.confidence, 'entities': result.entities}, ensure_ascii=False)}")
                    return result
                else:
                    # 没有识别到意图
                    self.logger.warning("意图识别模型未返回有效结果")
                    return IntentResult(
                        intent="unknown",
                        confidence=0.0,
                        emotion="neutral",
                        entities={}
                    )
            except json.JSONDecodeError:
                self.logger.error(f"无法解析意图识别结果: {content}")
                # 返回默认值
                return IntentResult(
                    intent="unknown",
                    confidence=0.0,
                    emotion="neutral",
                    entities={}
                )
        else:
            # 原有的解析逻辑
            try:
                # 尝试从响应中提取JSON
                json_data = self._extract_json(content)
                
                if json_data:
                    # 验证JSON结构
                    if all(k in json_data for k in ["intent", "emotion", "confidence"]):
                        # 构建IntentResult对象
                        result = IntentResult(
                            intent=json_data.get("intent", "unknown"),
                            emotion=json_data.get("emotion", "neutral"),
                            confidence=float(json_data.get("confidence", 0.0)),
                            entities=json_data.get("entities", {})
                        )
                        self.logger.info(f"意图识别结果: {json.dumps({'intent': result.intent, 'emotion': result.emotion, 'confidence': result.confidence, 'entities': result.entities}, ensure_ascii=False)}")
                        return result
                    else:
                        self.logger.warning(f"意图识别结果缺少必要字段: {json_data}")
                
                # 如果无法提取有效JSON或JSON结构不正确
                self.logger.warning(f"无法从响应中提取有效的意图识别结果")
                return IntentResult(
                    intent="unknown",
                    confidence=0.0,
                    emotion="neutral",
                    entities={}
                )
            except Exception as e:
                self.logger.error(f"解析意图识别结果时出错: {str(e)}")
                return IntentResult(
                    intent="unknown",
                    confidence=0.0,
                    emotion="neutral",
                    entities={}
                )

    def _format_conversation_history(self, conversation_history: List[Dict[str, str]]) -> str:
        """格式化对话历史为字符串"""
        if not conversation_history:
            return ""

        formatted = []
        for item in conversation_history:
            role = item.get("role", "unknown")
            content = item.get("content", "")
            formatted.append(f"{role}: {content}")

        return "\n".join(formatted)

    def _extract_json(self, text: str) -> Dict[str, Any]:
        """从文本中提取JSON对象"""
        try:
            # 尝试直接解析整个文本
            return json.loads(text)
        except json.JSONDecodeError:
            # 如果失败，尝试从文本中找到JSON部分
            import re
            json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
            matches = re.findall(json_pattern, text, re.DOTALL)

            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue

            return None
