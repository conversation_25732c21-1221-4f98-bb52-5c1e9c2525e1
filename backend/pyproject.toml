[build-system]
requires = ["setuptools>=42"]
build-backend = "setuptools.build_meta"

[project]
name = "backend"
version = "0.1.0"
description = "AutoGen backend for requirements collection system"
authors = [{name = "Your Name", email = "<EMAIL>"}]
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

[project.dependencies]
pyautogen = ">=0.2.0"

[project.optional-dependencies]
test = ["pytest", "pytest-asyncio", "pytest-cov"]
