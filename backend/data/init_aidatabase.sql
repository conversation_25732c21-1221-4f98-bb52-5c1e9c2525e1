-- 初始化aidatabase.db数据库的完整SQL脚本
-- 此脚本创建所有必要的表、约束和索引
PRAGMA foreign_keys = ON;
-- 删除已存在的表（如果需要重新创建）
DROP TABLE IF EXISTS documents;
DROP TABLE IF EXISTS requirements;
DROP TABLE IF EXISTS concern_point_coverage;
DROP TABLE IF EXISTS messages;
DROP TABLE IF EXISTS conversations;
DROP TABLE IF EXISTS focus_point_definitions;
DROP TABLE IF EXISTS categories;
DROP TABLE IF EXISTS domains;
DROP TABLE IF EXISTS domain_classification_mapping;
DROP TABLE IF EXISTS category_recognition_results;
-- 1. domains（领域表）
CREATE TABLE domains (
    domain_id TEXT PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT NOT NULL,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now'))
);
-- 2. categories（类别表）
CREATE TABLE categories (
    category_id TEXT PRIMARY KEY,
    domain_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (domain_id) REFERENCES domains(domain_id),
    UNIQUE (domain_id, name)
);
-- 3. focus_point_definitions（关注点定义表）
CREATE TABLE focus_point_definitions (
    focus_id TEXT PRIMARY KEY,
    category_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    priority TEXT NOT NULL CHECK (priority IN ('P0', 'P1', 'P2')),
    example TEXT,
    required INTEGER NOT NULL DEFAULT 0,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (category_id) REFERENCES categories(category_id)
);
-- 4. conversations（会话表）
CREATE TABLE conversations (
    conversation_id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain_id TEXT,
    category_id TEXT,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (domain_id) REFERENCES domains(domain_id),
    FOREIGN KEY (category_id) REFERENCES categories(category_id)
);
-- 5. messages（消息表）
CREATE TABLE messages (
    message_id INTEGER PRIMARY KEY AUTOINCREMENT,
    conversation_id INTEGER NOT NULL,
    sender_type TEXT NOT NULL CHECK (sender_type IN ('user', 'system', 'ai')),
    content TEXT NOT NULL,
    created_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE
);
-- 6. concern_point_coverage（关注点覆盖表）
CREATE TABLE concern_point_coverage (
    coverage_id INTEGER PRIMARY KEY AUTOINCREMENT,
    conversation_id INTEGER NOT NULL,
    focus_id TEXT NOT NULL,
    is_covered INTEGER NOT NULL DEFAULT 0,
    extracted_info TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    FOREIGN KEY (focus_id) REFERENCES focus_point_definitions(focus_id) ON DELETE CASCADE,
    CONSTRAINT unique_conversation_focus UNIQUE (conversation_id, focus_id)
);
-- 7. requirements（需求表）
CREATE TABLE requirements (
    requirement_id INTEGER PRIMARY KEY AUTOINCREMENT,
    conversation_id INTEGER NOT NULL,
    content TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'confirmed', 'rejected')),
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE
);
-- 8. documents（文档表）
CREATE TABLE documents (
    document_id TEXT PRIMARY KEY,
    conversation_id INTEGER NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    content TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'confirmed', 'rejected')),
    feedback TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    CONSTRAINT unique_conversation_version UNIQUE (conversation_id, version)
);
-- 9. domain_classification_mapping（领域分类映射表）
CREATE TABLE domain_classification_mapping (
    mapping_id INTEGER PRIMARY KEY AUTOINCREMENT,
    classifier_domain_id TEXT NOT NULL,
    actual_domain_id TEXT NOT NULL,
    classifier_category TEXT,
    actual_category_id TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (actual_domain_id) REFERENCES domains(domain_id),
    FOREIGN KEY (actual_category_id) REFERENCES categories(category_id),
    UNIQUE (classifier_domain_id, classifier_category)
);
-- 创建索引以优化查询性能
CREATE INDEX idx_categories_domain_id ON categories(domain_id);
CREATE INDEX idx_focus_point_definitions_category_id ON focus_point_definitions(category_id);
CREATE INDEX idx_conversations_domain_id ON conversations(domain_id);
CREATE INDEX idx_conversations_category_id ON conversations(category_id);
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_concern_point_coverage_conversation_id ON concern_point_coverage(conversation_id);
CREATE INDEX idx_concern_point_coverage_focus_id ON concern_point_coverage(focus_id);
CREATE INDEX idx_requirements_conversation_id ON requirements(conversation_id);
CREATE INDEX idx_documents_conversation_id ON documents(conversation_id);
-- 插入一些基础领域数据（可选）
INSERT INTO domains (domain_id, name, description)
VALUES ('LY_001', '软件开发', '包括各类软件系统的开发需求'),
    ('LY_002', '企业管理', '企业内部管理系统和流程的需求'),
    ('LY_003', '电子商务', '电商平台和在线交易系统的需求'),
    ('LY_004', '人工智能', 'AI应用和智能系统的需求');
-- 提交事务
PRAGMA foreign_keys = ON;