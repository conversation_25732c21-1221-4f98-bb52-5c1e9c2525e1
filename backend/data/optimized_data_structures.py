# -*- coding: utf-8 -*-
"""
优化的数据结构定义

本模块定义了优化后的数据结构，用于减少冗余字段和提升性能。
主要包括：
1. 精简的提取结果结构
2. 关注点定义缓存管理
3. 优化的API响应格式
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
import json


@dataclass
class OptimizedExtractedPoint:
    """优化的已提取关注点结构"""
    focus_id: str
    value: str
    completeness: float
    extracted_at: str = None
    
    def __post_init__(self):
        if self.extracted_at is None:
            self.extracted_at = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


@dataclass
class OptimizedMissingPoint:
    """优化的缺失关注点结构"""
    focus_id: str
    completeness: float
    attempts: int
    last_attempt_at: str = None
    reason: str = "未提取到相关信息"
    
    def __post_init__(self):
        if self.last_attempt_at is None:
            self.last_attempt_at = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


@dataclass
class FocusPointDefinition:
    """关注点定义结构"""
    focus_id: str
    name: str
    description: str
    priority: str
    example: str = ""
    required: bool = False
    category_id: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


class OptimizedExtractionResult:
    """优化的信息提取结果"""
    
    def __init__(self):
        self.extracted_points: List[OptimizedExtractedPoint] = []
        self.missing_points: List[OptimizedMissingPoint] = []
        self.summary: str = ""
        self.total_points: int = 0
        self.extraction_time: str = datetime.now().isoformat()
    
    def add_extracted_point(self, focus_id: str, value: str, completeness: float) -> None:
        """添加已提取的关注点"""
        point = OptimizedExtractedPoint(
            focus_id=focus_id,
            value=value,
            completeness=completeness
        )
        self.extracted_points.append(point)
    
    def add_missing_point(self, focus_id: str, completeness: float, attempts: int, reason: str = "未提取到相关信息") -> None:
        """添加缺失的关注点"""
        point = OptimizedMissingPoint(
            focus_id=focus_id,
            completeness=completeness,
            attempts=attempts,
            reason=reason
        )
        self.missing_points.append(point)
    
    def generate_summary(self) -> str:
        """生成提取摘要"""
        extracted_count = len(self.extracted_points)
        missing_count = len(self.missing_points)
        total = extracted_count + missing_count
        
        if total == 0:
            self.summary = "未处理任何关注点"
        elif extracted_count == 0:
            self.summary = f"未成功提取任何信息，共{missing_count}个关注点待处理"
        elif missing_count == 0:
            self.summary = f"成功提取所有信息，共完成{extracted_count}个关注点"
        else:
            self.summary = f"成功提取{extracted_count}个关注点，{missing_count}个关注点待处理"
        
        return self.summary
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "extracted_points": [point.to_dict() for point in self.extracted_points],
            "missing_points": [point.to_dict() for point in self.missing_points],
            "summary": self.summary or self.generate_summary(),
            "total_points": len(self.extracted_points) + len(self.missing_points),
            "extraction_time": self.extraction_time,
            "statistics": {
                "extracted_count": len(self.extracted_points),
                "missing_count": len(self.missing_points),
                "success_rate": len(self.extracted_points) / max(1, len(self.extracted_points) + len(self.missing_points))
            }
        }


class FocusPointDefinitionCache:
    """关注点定义缓存管理器"""
    
    def __init__(self):
        self._cache: Dict[str, FocusPointDefinition] = {}
        self._last_update: Optional[datetime] = None
        self._cache_ttl: int = 3600  # 缓存1小时
    
    def add_definition(self, definition: FocusPointDefinition) -> None:
        """添加关注点定义到缓存"""
        self._cache[definition.focus_id] = definition
        self._last_update = datetime.now()
    
    def add_definitions(self, definitions: List[FocusPointDefinition]) -> None:
        """批量添加关注点定义"""
        for definition in definitions:
            self._cache[definition.focus_id] = definition
        self._last_update = datetime.now()
    
    def get_definition(self, focus_id: str) -> Optional[FocusPointDefinition]:
        """获取单个关注点定义"""
        return self._cache.get(focus_id)
    
    def get_definitions(self, focus_ids: List[str]) -> Dict[str, FocusPointDefinition]:
        """批量获取关注点定义"""
        result = {}
        for focus_id in focus_ids:
            if focus_id in self._cache:
                result[focus_id] = self._cache[focus_id]
        return result
    
    def get_all_definitions(self) -> Dict[str, FocusPointDefinition]:
        """获取所有缓存的定义"""
        return self._cache.copy()
    
    def is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if self._last_update is None:
            return False
        
        elapsed = (datetime.now() - self._last_update).total_seconds()
        return elapsed < self._cache_ttl
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self._cache.clear()
        self._last_update = None
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "cache_size": len(self._cache),
            "last_update": self._last_update.isoformat() if self._last_update else None,
            "is_valid": self.is_cache_valid(),
            "ttl_seconds": self._cache_ttl
        }


class OptimizedAPIResponse:
    """优化的API响应格式"""
    
    def __init__(self, extraction_result: OptimizedExtractionResult, definition_cache: FocusPointDefinitionCache):
        self.extraction_result = extraction_result
        self.definition_cache = definition_cache
    
    def build_response(self) -> Dict[str, Any]:
        """构建优化的API响应"""
        # 收集所有需要的关注点ID
        focus_ids = set()
        for point in self.extraction_result.extracted_points:
            focus_ids.add(point.focus_id)
        for point in self.extraction_result.missing_points:
            focus_ids.add(point.focus_id)
        
        # 获取关注点定义
        definitions = self.definition_cache.get_definitions(list(focus_ids))
        
        # 构建响应
        response = {
            "extraction_results": self.extraction_result.to_dict(),
            "focus_point_definitions": {
                focus_id: definition.to_dict() 
                for focus_id, definition in definitions.items()
            },
            "metadata": {
                "response_time": datetime.now().isoformat(),
                "cache_stats": self.definition_cache.get_cache_stats(),
                "data_version": "optimized_v1.0"
            }
        }
        
        return response
    
    def build_legacy_response(self) -> Dict[str, Any]:
        """构建兼容旧格式的响应（用于向后兼容）"""
        # 获取关注点定义
        focus_ids = set()
        for point in self.extraction_result.extracted_points:
            focus_ids.add(point.focus_id)
        for point in self.extraction_result.missing_points:
            focus_ids.add(point.focus_id)
        
        definitions = self.definition_cache.get_definitions(list(focus_ids))
        
        # 转换为旧格式
        legacy_extracted = []
        for point in self.extraction_result.extracted_points:
            definition = definitions.get(point.focus_id)
            if definition:
                legacy_point = {
                    "focus_point": definition.to_dict(),
                    "name": definition.name,
                    "value": point.value,
                    "extracted_value": point.value,
                    "completeness": point.completeness,
                    "id": point.focus_id
                }
                legacy_extracted.append(legacy_point)
        
        legacy_missing = []
        for point in self.extraction_result.missing_points:
            definition = definitions.get(point.focus_id)
            if definition:
                legacy_point = {
                    "focus_point": definition.to_dict(),
                    "name": definition.name,
                    "description": definition.description,
                    "completeness": point.completeness,
                    "attempts": point.attempts,
                    "example": definition.example,
                    "priority": definition.priority,
                    "id": point.focus_id
                }
                legacy_missing.append(legacy_point)
        
        return {
            "extracted_points": legacy_extracted,
            "missing_points": legacy_missing,
            "summary": self.extraction_result.summary or self.extraction_result.generate_summary()
        }


def convert_legacy_to_optimized(legacy_result: Dict[str, Any]) -> OptimizedExtractionResult:
    """将旧格式的提取结果转换为优化格式"""
    result = OptimizedExtractionResult()
    
    # 转换已提取的点
    for point in legacy_result.get("extracted_points", []):
        focus_id = point.get("id") or point.get("focus_point", {}).get("focus_id", "")
        value = point.get("value") or point.get("extracted_value", "")
        completeness = point.get("completeness", 1.0)
        
        result.add_extracted_point(focus_id, value, completeness)
    
    # 转换缺失的点
    for point in legacy_result.get("missing_points", []):
        focus_id = point.get("id") or point.get("focus_point", {}).get("focus_id", "")
        completeness = point.get("completeness", 0.0)
        attempts = point.get("attempts", 0)
        
        result.add_missing_point(focus_id, completeness, attempts)
    
    # 设置摘要
    result.summary = legacy_result.get("summary", "")
    
    return result


def convert_legacy_focus_points_to_definitions(legacy_points: List[Dict[str, Any]]) -> List[FocusPointDefinition]:
    """将旧格式的关注点转换为定义格式"""
    definitions = []
    
    for point in legacy_points:
        definition = FocusPointDefinition(
            focus_id=point.get("id", ""),
            name=point.get("name", ""),
            description=point.get("description", ""),
            priority=point.get("priority", "P1"),
            example=point.get("example", ""),
            required=point.get("required", False),
            category_id=point.get("category_id", "")
        )
        definitions.append(definition)
    
    return definitions