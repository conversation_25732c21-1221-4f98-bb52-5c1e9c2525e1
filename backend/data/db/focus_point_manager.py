"""
关注点状态管理类，用于处理关注点状态的持久化和加载
"""
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime
from .database_manager import DatabaseManager

class FocusPointManager:
    """关注点状态管理类"""

    def __init__(self, db_manager: DatabaseManager):
        """
        初始化关注点状态管理器

        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)

    async def ensure_conversation_exists(self, conversation_id: str) -> bool:
        """
        确保会话存在，如果不存在则创建

        Args:
            conversation_id: 会话ID

        Returns:
            bool: 操作是否成功
        """
        try:
            # 检查会话是否存在
            conversation_exists = await self.db_manager.record_exists(
                "SELECT 1 FROM conversations WHERE conversation_id = ?",
                (conversation_id,)
            )

            if not conversation_exists:
                # 创建新会话
                now = datetime.now().isoformat()
                try:
                    await self.db_manager.execute_update(
                        """
                        INSERT INTO conversations
                        (conversation_id, user_id, status, created_at, updated_at, last_activity_at)
                        VALUES (?, ?, ?, ?, ?, ?)
                        """,
                        (conversation_id, "default_user", "active", now, now, now)
                    )
                    self.logger.info(f"创建新会话 - conversation_id: {conversation_id}")
                except Exception as e:
                    # 检查是否是唯一约束错误
                    if "UNIQUE constraint failed" in str(e):
                        self.logger.info(f"会话 {conversation_id} 已存在，忽略此错误")
                    else:
                        raise

            return True
        except Exception as e:
            self.logger.error(f"确保会话存在失败: {str(e)}")
            return False

    async def initialize_focus_points(self, conversation_id: str, focus_points: List[Dict[str, Any]]) -> bool:
        """
        初始化关注点状态

        Args:
            conversation_id: 会话ID
            focus_points: 关注点列表

        Returns:
            bool: 初始化是否成功
        """
        try:
            # 确保会话存在
            if not await self.ensure_conversation_exists(conversation_id):
                return False

            # 准备批量插入数据
            params_list = []
            for point in focus_points:
                # 检查关注点是否已存在
                exists = await self.db_manager.record_exists(
                    "SELECT 1 FROM concern_point_coverage WHERE conversation_id = ? AND focus_id = ?",
                    (conversation_id, point["id"])
                )

                if not exists:
                    params_list.append((
                        conversation_id,
                        point["id"],
                        "pending",  # 初始状态
                        0,          # 尝试次数
                        0,          # 是否已覆盖
                        "",         # 提取的信息
                        datetime.now().isoformat()  # 更新时间
                    ))

            # 如果有需要插入的数据
            if params_list:
                await self.db_manager.execute_batch(
                    """
                    INSERT INTO concern_point_coverage
                    (conversation_id, focus_id, status, attempts, is_covered, extracted_info, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    """,
                    params_list
                )

                self.logger.debug(f"初始化关注点状态完成 - conversation_id: {conversation_id}, 关注点数量: {len(params_list)}")
            else:
                self.logger.debug(f"关注点状态已存在，无需初始化 - conversation_id: {conversation_id}")

            return True
        except Exception as e:
            self.logger.error(f"初始化关注点状态失败: {str(e)}")
            return False

    async def load_focus_points_status(self, conversation_id: str) -> Dict[str, Dict[str, Any]]:
        """
        加载关注点状态

        Args:
            conversation_id: 会话ID

        Returns:
            Dict[str, Dict[str, Any]]: 关注点状态字典
        """
        try:
            # 确保会话存在
            await self.ensure_conversation_exists(conversation_id)

            results = await self.db_manager.execute_query(
                """
                SELECT focus_id, status, attempts, extracted_info, is_covered, updated_at
                FROM concern_point_coverage
                WHERE conversation_id = ?
                """,
                (conversation_id,)
            )

            status = {}
            for row in results:
                status[row["focus_id"]] = {
                    "status": row["status"],
                    "attempts": row["attempts"],
                    "value": row["extracted_info"],
                    "is_covered": bool(row["is_covered"]),
                    "updated_at": row["updated_at"]
                }

            self.logger.debug(f"加载关注点状态 - conversation_id: {conversation_id}, 状态数量: {len(status)}")
            return status
        except Exception as e:
            self.logger.error(f"加载关注点状态失败: {str(e)}")
            return {}

    async def update_focus_point_status(self, conversation_id: str, focus_id: str, status: str, value: Optional[str] = None) -> bool:
        """
        更新关注点状态

        Args:
            conversation_id: 会话ID
            focus_id: 关注点ID
            status: 新状态
            value: 提取的值

        Returns:
            bool: 更新是否成功
        """
        try:
            # 确保会话存在
            if not await self.ensure_conversation_exists(conversation_id):
                return False

            # 检查记录是否存在
            exists = await self.db_manager.record_exists(
                "SELECT 1 FROM concern_point_coverage WHERE conversation_id = ? AND focus_id = ?",
                (conversation_id, focus_id)
            )

            if exists:
                # 更新现有记录
                await self.db_manager.execute_update(
                    """
                    UPDATE concern_point_coverage
                    SET status = ?,
                        extracted_info = CASE WHEN ? IS NOT NULL THEN ? ELSE extracted_info END,
                        attempts = CASE WHEN ? = 'processing' THEN attempts + 1 ELSE attempts END,
                        is_covered = CASE WHEN ? = 'completed' THEN 1 ELSE 0 END,
                        updated_at = ?
                    WHERE conversation_id = ? AND focus_id = ?
                    """,
                    (status, value is not None, value or "", status, status, datetime.now().isoformat(), conversation_id, focus_id)
                )
            else:
                # 插入新记录
                await self.db_manager.execute_update(
                    """
                    INSERT INTO concern_point_coverage
                    (conversation_id, focus_id, status, attempts, is_covered, extracted_info, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        conversation_id,
                        focus_id,
                        status,
                        1 if status == "processing" else 0,
                        1 if status == "completed" else 0,
                        value or "",
                        datetime.now().isoformat()
                    )
                )

            self.logger.debug(f"更新关注点状态 - conversation_id: {conversation_id}, focus_id: {focus_id}, status: {status}")
            return True
        except Exception as e:
            self.logger.error(f"更新关注点状态失败: {str(e)}")
            return False

    async def get_focus_point_status(self, conversation_id: str, focus_id: str) -> Optional[Dict[str, Any]]:
        """
        获取关注点状态

        Args:
            conversation_id: 会话ID
            focus_id: 关注点ID

        Returns:
            Optional[Dict[str, Any]]: 关注点状态
        """
        try:
            result = await self.db_manager.get_record(
                """
                SELECT status, attempts, extracted_info, is_covered, updated_at
                FROM concern_point_coverage
                WHERE conversation_id = ? AND focus_id = ?
                """,
                (conversation_id, focus_id)
            )

            if result:
                return {
                    "status": result["status"],
                    "attempts": result["attempts"],
                    "value": result["extracted_info"],
                    "is_covered": bool(result["is_covered"]),
                    "updated_at": result["updated_at"]
                }

            return None
        except Exception as e:
            self.logger.error(f"获取关注点状态失败: {str(e)}")
            return None

    async def get_next_pending_focus_point(self, conversation_id: str, focus_points: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        获取下一个待处理的关注点

        Args:
            conversation_id: 会话ID
            focus_points: 关注点列表

        Returns:
            Optional[Dict[str, Any]]: 下一个待处理的关注点
        """
        try:
            # 加载关注点状态
            status_dict = await self.load_focus_points_status(conversation_id)

            # 首先查找P0或P1优先级的待处理关注点
            for point in focus_points:
                point_id = point["id"]
                point_status = status_dict.get(point_id, {}).get("status", "pending")

                if point_status == "pending" and point["priority"] in ["P0", "P1"]:
                    return point

            # 如果没有P0/P1关注点，查找任何待处理的关注点
            for point in focus_points:
                point_id = point["id"]
                point_status = status_dict.get(point_id, {}).get("status", "pending")

                if point_status == "pending":
                    return point

            # 没有待处理的关注点
            return None
        except Exception as e:
            self.logger.error(f"获取下一个待处理关注点失败: {str(e)}")
            return None
