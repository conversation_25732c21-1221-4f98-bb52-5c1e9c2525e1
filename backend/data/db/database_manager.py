"""
数据库管理类，用于处理数据库连接和操作
"""
import sqlite3
import logging
import json
import asyncio
from typing import Any, Dict, List, Optional, Tuple, Union
from contextlib import contextmanager, asynccontextmanager
from pathlib import Path
from backend.utils.performance_monitor import performance_monitor

class DatabaseManager:
    """数据库连接管理类"""
    
    def __init__(self, db_path: str):
        """
        初始化数据库连接管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.lock = asyncio.Lock()
        
        # 确保数据库目录存在
        db_dir = Path(db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    @asynccontextmanager
    async def get_connection(self):
        """
        获取数据库连接的异步上下文管理器
        
        Yields:
            sqlite3.Connection: 数据库连接
        """
        async with self.lock:  # 确保同一时间只有一个连接在操作数据库
            conn = None
            try:
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = sqlite3.Row  # 使查询结果可以通过列名访问
                yield conn
            except sqlite3.Error as e:
                self.logger.error(f"数据库连接错误: {e}")
                if conn:
                    conn.rollback()
                raise
            finally:
                if conn:
                    conn.close()

    async def execute_query(self, query: str, params: Optional[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]] = None) -> List[Dict[str, Any]]:
        """执行SQL查询并返回结果

        Args:
            query (str): SQL查询语句
            params (tuple, optional): 查询参数. Defaults to None.

        Returns:
            list: 查询结果列表，每个元素是一个字典
        """
        self.logger.debug(f"执行SQL: {query}")
        self.logger.debug(f"参数: {params}")

        # 确定查询类型
        query_type = query.strip().upper().split()[0] if query.strip() else "UNKNOWN"

        # 使用性能监控器跟踪数据库查询
        with performance_monitor.track_db_query(query_type):
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                if query.strip().upper().startswith("SELECT"):
                    column_names = [description[0] for description in cursor.description] if cursor.description else []
                    rows = cursor.fetchall()
                    return [{column_names[i]: row[i] for i in range(len(column_names))} for row in rows]
                else:
                    conn.commit()
                    return []

    async def execute_update(self, query: str, params: Optional[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]] = None) -> int:
        """
        执行更新操作并返回受影响的行数

        Args:
            query: SQL更新语句
            params: 更新参数

        Returns:
            int: 受影响的行数
        """
        # 确定查询类型
        query_type = query.strip().upper().split()[0] if query.strip() else "UNKNOWN"

        # 使用性能监控器跟踪数据库更新
        with performance_monitor.track_db_query(query_type):
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                try:
                    if params:
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query)
                    conn.commit()
                    return cursor.rowcount
                except sqlite3.Error as e:
                    self.logger.error(f"执行更新失败: {query}, 错误: {e}")
                    conn.rollback()
                    raise

    async def execute_batch(self, query: str, params_list: List[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]]) -> int:
        """
        执行批量更新操作并返回受影响的行数

        Args:
            query: SQL更新语句
            params_list: 参数列表

        Returns:
            int: 受影响的行数
        """
        # 确定查询类型
        query_type = f"BATCH_{query.strip().upper().split()[0]}" if query.strip() else "BATCH_UNKNOWN"

        # 使用性能监控器跟踪批量数据库操作
        with performance_monitor.track_db_query(query_type):
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                try:
                    cursor.executemany(query, params_list)
                    conn.commit()
                    return cursor.rowcount
                except sqlite3.Error as e:
                    self.logger.error(f"执行批量更新失败: {query}, 错误: {e}")
                    conn.rollback()
                    raise

    async def execute_transaction(self, queries: List[Tuple[str, Optional[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]]]]) -> bool:
        """
        在一个事务中执行多个查询

        Args:
            queries: 查询列表，每个元素是(query, params)元组

        Returns:
            bool: 事务是否成功
        """
        # 使用性能监控器跟踪事务操作
        with performance_monitor.track_db_query("TRANSACTION"):
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                try:
                    for query, params in queries:
                        if params:
                            cursor.execute(query, params)
                        else:
                            cursor.execute(query)
                    conn.commit()
                    return True
                except sqlite3.Error as e:
                    self.logger.error(f"执行事务失败: {e}")
                    conn.rollback()
                    return False
    
    async def get_record(self, query: str, params: Optional[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]] = None) -> Optional[Dict[str, Any]]:
        """
        获取单条记录
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            Optional[Dict[str, Any]]: 查询结果，如果没有找到则返回None
        """
        results = await self.execute_query(query, params)
        return results[0] if results else None
    
    async def record_exists(self, query: str, params: Optional[Union[Dict[str, Any], Tuple[Any, ...], List[Any]]] = None) -> bool:
        """
        检查记录是否存在
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            bool: 记录是否存在
        """
        return bool(await self.get_record(query, params))
    
    async def get_document(self, document_id: str) -> Optional[Dict[str, Any]]:
        """
        根据文档ID获取文档内容
        
        Args:
            document_id: 文档ID
        
        Returns:
            Dict: 包含文档信息的字典，如果未找到则返回None
        """
        query = """
            SELECT document_id, conversation_id, content, status, version, created_at, updated_at
            FROM documents
            WHERE document_id = ?
        """
        result = await self.execute_query(query, (document_id,))
        return result[0] if result else None
    
    async def get_documents_by_conversation_id(self, conversation_id: int) -> List[Dict[str, Any]]:
        """
        根据会话ID获取所有相关文档
        
        Args:
            conversation_id: 会话ID
        
        Returns:
            List[Dict[str, Any]]: 包含文档信息的字典列表
        """
        query = """
            SELECT document_id, conversation_id, content, status, version, created_at, updated_at
            FROM documents
            WHERE conversation_id = ?
            ORDER BY version DESC
        """
        return await self.execute_query(query, (conversation_id,))
    
    async def insert_document(self, document_id: str, conversation_id: int, version: int, content: str, created_at: str, updated_at: str):
        """
        插入文档记录
        
        Args:
            document_id: 文档ID
            conversation_id: 会话ID
            version: 文档版本
            content: 文档内容
            created_at: 创建时间
            updated_at: 更新时间
        """
        try:
            self.logger.debug(f"准备插入数据: document_id={document_id}, conversation_id={conversation_id}, "
                              f"version={version}, content={content}, created_at={created_at}, updated_at={updated_at}")
            
            query = """
                INSERT INTO documents (
                    document_id, conversation_id, version, content, status, 
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, 'draft', ?, ?)
            """
            params = (document_id, conversation_id, version, content, created_at, updated_at)
            
            # 确保参数数量与列定义一致
            if len(params) != 7:
                raise ValueError(f"参数数量错误: 需要 7 个参数，但提供了 {len(params)} 个参数: {params}")
            
            async with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
        except Exception as e:
            self.logger.error(f"执行更新失败: {query}, 错误: {str(e)}")
            raise
