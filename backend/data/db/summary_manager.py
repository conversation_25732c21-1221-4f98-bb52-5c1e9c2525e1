# backend/data/db/summary_manager.py

import json
from .database_manager import DatabaseManager
import logging

class SummaryManager:
    """
    管理 conversation_summaries 表的 CRUD 操作。
    这个类负责处理每个对话的最新状态摘要。
    """
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化摘要管理器。

        Args:
            db_manager: 数据库管理器实例。
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)

    async def get_summary(self, conversation_id: str) -> str:
        """
        根据 conversation_id 获取最新的对话摘要。

        Args:
            conversation_id: 对话的唯一标识符。

        Returns:
            一个JSON字符串格式的摘要。如果不存在，则返回一个空的JSON对象字符串。
        """
        query = "SELECT summary_json FROM conversation_summaries WHERE conversation_id = ?"
        try:
            result = await self.db_manager.get_record(query, (conversation_id,))
            if result and result.get('summary_json'):
                self.logger.debug(f"成功获取到 conversation_id '{conversation_id}' 的摘要。")
                return result['summary_json']
            else:
                self.logger.debug(f"未找到 conversation_id '{conversation_id}' 的摘要，返回空对象。")
                return "{}"
        except Exception as e:
            self.logger.error(f"获取摘要失败 (conversation_id: {conversation_id}): {e}", exc_info=True)
            return "{}" # 出错时也返回安全的空JSON

    async def update_summary(self, conversation_id: str, new_summary_json: str):
        """
        使用 UPSERT 逻辑，更新或插入一个对话的摘要。

        Args:
            conversation_id: 对话的唯一标识符。
            new_summary_json: 最新的、完整的JSON摘要字符串。
        """
        query = """
        INSERT INTO conversation_summaries (conversation_id, summary_json, updated_at)
        VALUES (?, ?, datetime('now'))
        ON CONFLICT(conversation_id) DO UPDATE SET
          summary_json = excluded.summary_json,
          updated_at = excluded.updated_at;
        """
        try:
            # 验证传入的是否为合法的JSON字符串
            json.loads(new_summary_json)
            
            await self.db_manager.execute_update(query, (conversation_id, new_summary_json))
            self.logger.info(f"成功更新了 conversation_id '{conversation_id}' 的摘要。")
        except json.JSONDecodeError as e:
            self.logger.error(f"更新摘要失败：传入的字符串不是有效的JSON (conversation_id: {conversation_id}): {e}")
        except Exception as e:
            self.logger.error(f"更新摘要失败 (conversation_id: {conversation_id}): {e}", exc_info=True)
