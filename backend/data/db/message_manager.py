"""
消息管理类，用于处理会话历史记录的持久化和加载
"""
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime
from .database_manager import DatabaseManager

class MessageManager:
    """消息管理类"""

    def __init__(self, db_manager: DatabaseManager):
        """
        初始化消息管理器

        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)

    async def ensure_conversation_exists(self, conversation_id: str) -> bool:
        """
        确保会话存在，如果不存在则创建

        Args:
            conversation_id: 会话ID

        Returns:
            bool: 操作是否成功
        """
        try:
            # 检查会话是否存在
            conversation_exists = await self.db_manager.record_exists(
                "SELECT 1 FROM conversations WHERE conversation_id = ?",
                (conversation_id,)
            )

            if not conversation_exists:
                # 创建新会话
                now = datetime.now().isoformat()
                try:
                    await self.db_manager.execute_update(
                        """
                        INSERT INTO conversations
                        (conversation_id, user_id, status, created_at, updated_at, last_activity_at)
                        VALUES (?, ?, ?, ?, ?, ?)
                        """,
                        (conversation_id, "default_user", "active", now, now, now)
                    )
                    self.logger.info(f"创建新会话 - conversation_id: {conversation_id}")
                except Exception as e:
                    # 检查是否是唯一约束错误
                    if "UNIQUE constraint failed" in str(e):
                        self.logger.info(f"会话 {conversation_id} 已存在，忽略此错误")
                    else:
                        raise

            return True
        except Exception as e:
            self.logger.error(f"确保会话存在失败: {str(e)}")
            return False

    async def save_message(self, conversation_id: str, sender_type: str, content: str, focus_id: Optional[str] = None, message_type: Optional[str] = None) -> bool:
        """
        保存消息

        Args:
            conversation_id: 会话ID
            sender_type: 发送者类型（user, ai, system）
            content: 消息内容
            focus_id: 关联的关注点ID
            message_type: 消息类型

        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保会话存在
            if not await self.ensure_conversation_exists(conversation_id):
                return False

            # 插入消息
            await self.db_manager.execute_update(
                """
                INSERT INTO messages
                (conversation_id, sender_type, content, focus_id, message_type, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                (conversation_id, sender_type, content, focus_id, message_type, datetime.now().isoformat())
            )

            # 更新会话的最后活动时间
            await self.db_manager.execute_update(
                """
                UPDATE conversations
                SET last_activity_at = ?, updated_at = ?
                WHERE conversation_id = ?
                """,
                (datetime.now().isoformat(), datetime.now().isoformat(), conversation_id)
            )

            self.logger.debug(f"保存消息 - conversation_id: {conversation_id}, sender_type: {sender_type}, focus_id: {focus_id}")
            return True
        except Exception as e:
            self.logger.error(f"保存消息失败: {str(e)}")
            return False

    async def load_conversation_history(self, conversation_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        加载会话历史 (已修复)

        Args:
            conversation_id: 会话ID
            limit: 最大消息数量

        Returns:
            List[Dict[str, Any]]: 经过排序和过滤的干净会话历史
        """
        try:
            # --- 关键修复点 ---
            # 1. 在WHERE子句中增加了过滤条件，排除了所有 message_type 为 'summary' 的系统噪音消息。
            # 2. 明确使用 ORDER BY created_at ASC 来确保时间升序。
            results = await self.db_manager.execute_query(
                """
                SELECT sender_type, content, focus_id, message_type, created_at as timestamp
                FROM messages
                WHERE 
                    conversation_id = ? AND 
                    (message_type IS NULL OR message_type NOT IN ('summary'))
                ORDER BY timestamp ASC
                LIMIT ?
                """,
                (conversation_id, limit)
            )

            history = []
            for row in results:
                # 兼容 autogen 的 'role' 格式
                role = "user" if row["sender_type"] == "user" else "assistant"
                history.append({
                    "role": role,
                    "content": row["content"],
                    "focus_id": row["focus_id"],
                    "message_type": row["message_type"],
                    "timestamp": row["timestamp"] # 直接使用查询中别名 'timestamp'
                })

            self.logger.debug(f"加载过滤后的会话历史 - conversation_id: {conversation_id}, 消息数量: {len(history)}")
            return history
        except Exception as e:
            self.logger.error(f"加载会话历史失败: {str(e)}", exc_info=True)
            return []

    async def get_focus_point_messages(self, conversation_id: str, focus_id: str) -> List[Dict[str, Any]]:
        """
        获取与特定关注点相关的消息

        Args:
            conversation_id: 会话ID
            focus_id: 关注点ID

        Returns:
            List[Dict[str, Any]]: 消息列表
        """
        try:
            results = await self.db_manager.execute_query(
                """
                SELECT sender_type, content, message_type, created_at
                FROM messages
                WHERE conversation_id = ? AND focus_id = ?
                ORDER BY created_at ASC
                """,
                (conversation_id, focus_id)
            )

            messages = []
            for row in results:
                role = "user" if row["sender_type"] == "user" else "assistant"
                messages.append({
                    "role": role,
                    "content": row["content"],
                    "message_type": row["message_type"],
                    "timestamp": row["created_at"]
                })

            self.logger.debug(f"获取关注点消息 - conversation_id: {conversation_id}, focus_id: {focus_id}, 消息数量: {len(messages)}")
            return messages
        except Exception as e:
            self.logger.error(f"获取关注点消息失败: {str(e)}")
            return []

    async def get_first_user_message(self, conversation_id: str) -> Optional[str]:
        """
        获取会话中的第一条用户消息

        Args:
            conversation_id: 会话ID

        Returns:
            Optional[str]: 第一条用户消息内容，如果没有则返回None
        """
        try:
            result = await self.db_manager.get_record(
                """
                SELECT content FROM messages
                WHERE conversation_id = ? AND sender_type = 'user'
                ORDER BY created_at ASC
                LIMIT 1
                """,
                (conversation_id,)
            )
            return result["content"] if result else None
        except Exception as e:
            self.logger.error(f"获取第一条用户消息失败: {str(e)}")
            return None

    async def get_recent_messages(self, conversation_id: str, count: int = 5) -> List[Dict[str, Any]]:
        """
        获取最近的消息

        Args:
            conversation_id: 会话ID
            count: 消息数量

        Returns:
            List[Dict[str, Any]]: 消息列表
        """
        try:
            results = await self.db_manager.execute_query(
                """
                SELECT sender_type, content, focus_id, message_type, created_at
                FROM messages
                WHERE conversation_id = ?
                ORDER BY created_at DESC
                LIMIT ?
                """,
                (conversation_id, count)
            )

            # 反转结果，使其按时间顺序排列
            results.reverse()

            messages = []
            for row in results:
                role = "user" if row["sender_type"] == "user" else "assistant"
                messages.append({
                    "role": role,
                    "content": row["content"],
                    "focus_id": row["focus_id"],
                    "message_type": row["message_type"],
                    "timestamp": row["created_at"]
                })

            return messages
        except Exception as e:
            self.logger.error(f"获取最近消息失败: {str(e)}")
            return []
