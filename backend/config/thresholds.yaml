# 阈值配置文件
# 用于管理系统中所有的数值阈值和参数

# LLM相关阈值
llm:
  # 超时配置
  max_timeout: 60              # 最大超时时间（秒）
  default_timeout: 45          # 默认超时时间（秒）
  min_timeout: 5               # 最小超时时间（秒）
  
  # 温度参数配置
  temperature:
    default: 0.7               # 默认温度
    conservative: 0.3          # 保守温度（用于需要一致性的场景）
    creative: 0.9              # 创造性温度（用于需要多样性的场景）
    
  # Token限制
  tokens:
    max_tokens: 4000           # 最大token数
    default_tokens: 2000       # 默认token数
    min_tokens: 100            # 最小token数
    
  # 意图识别专用参数
  intent_recognition:
    temperature: 0.3           # 意图识别温度
    max_tokens: 300            # 意图识别最大token数
    stop_sequences:            # 停止序列
      - "```"
      - "###"
      - "---"
      - "\n\n"
      
  # 重试配置
  retry:
    max_retries: 3             # 最大重试次数
    retry_delay: 1             # 重试延迟（秒）
    backoff_factor: 2          # 退避因子
    
  # 并发控制
  concurrency:
    max_concurrent_calls: 10   # 最大并发调用数
    rate_limit_per_minute: 60  # 每分钟最大请求数

# 信息提取相关阈值
extraction:
  # 完整性阈值
  completeness_threshold: 0.7  # 信息完整性阈值
  confidence_threshold: 0.7    # 置信度阈值
  quality_threshold: 0.5       # 质量评估阈值
  
  # 批处理配置
  batch:
    max_points_per_batch: 5    # 每批次最大关注点数量
    min_points_per_batch: 1    # 每批次最小关注点数量
    
  # 上下文配置
  context:
    window_size: 2000          # 上下文窗口大小（字符数）
    max_history_turns: 5       # 最大历史对话轮数
    
  # 提取重试配置
  retry:
    max_attempts: 3            # 最大提取尝试次数
    timeout_seconds: 30        # 提取超时时间

# 性能相关阈值
performance:
  # 响应时间阈值
  response_time:
    excellent: 1.0             # 优秀响应时间（秒）
    good: 3.0                  # 良好响应时间（秒）
    acceptable: 5.0            # 可接受响应时间（秒）
    poor: 10.0                 # 较差响应时间（秒）
    
  # 内存使用阈值
  memory:
    warning_threshold: 0.8     # 内存使用警告阈值（80%）
    critical_threshold: 0.9    # 内存使用严重阈值（90%）
    
  # CPU使用阈值
  cpu:
    warning_threshold: 0.7     # CPU使用警告阈值（70%）
    critical_threshold: 0.9    # CPU使用严重阈值（90%）
    
  # 并发处理阈值
  concurrency:
    max_concurrent_sessions: 100    # 最大并发会话数
    max_pending_requests: 50        # 最大待处理请求数
    queue_timeout: 30               # 队列超时时间（秒）

# 缓存相关阈值
cache:
  # 缓存大小限制
  size:
    max_cache_size: 1000       # 最大缓存条目数
    cleanup_threshold: 0.8     # 清理阈值（80%满时开始清理）
    
  # 缓存时间配置
  ttl:
    default_ttl: 3600          # 默认TTL（秒）
    short_ttl: 300             # 短期TTL（秒）
    long_ttl: 86400            # 长期TTL（秒）
    
  # 缓存命中率阈值
  hit_rate:
    excellent: 0.9             # 优秀命中率
    good: 0.7                  # 良好命中率
    poor: 0.5                  # 较差命中率

# 数据库相关阈值
database:
  # 连接配置
  connection:
    max_connections: 50        # 最大连接数
    connection_timeout: 30     # 连接超时（秒）
    idle_timeout: 300          # 空闲超时（秒）
    
  # 查询性能阈值
  query:
    slow_query_threshold: 2.0  # 慢查询阈值（秒）
    very_slow_threshold: 5.0   # 非常慢查询阈值（秒）
    
  # 批处理配置
  batch:
    max_batch_size: 100        # 最大批处理大小
    batch_timeout: 10          # 批处理超时（秒）
    
  # 事务配置
  transaction:
    max_transaction_time: 30   # 最大事务时间（秒）
    deadlock_retry_count: 3    # 死锁重试次数

# 会话管理阈值
session:
  # 会话生命周期
  lifecycle:
    session_timeout: 3600      # 会话超时时间（秒）
    idle_timeout: 1800         # 空闲超时时间（秒）
    max_session_duration: 7200 # 最大会话持续时间（秒）
    
  # 会话清理配置
  cleanup:
    cleanup_interval: 300      # 清理间隔（秒）
    expired_session_grace: 600 # 过期会话宽限期（秒）
    
  # 会话限制
  limits:
    max_messages_per_session: 1000  # 每个会话最大消息数
    max_session_size_mb: 10         # 每个会话最大大小（MB）

# 安全相关阈值
security:
  # 输入验证阈值
  input:
    max_input_length: 10000    # 最大输入长度
    max_file_size_mb: 50       # 最大文件大小（MB）
    
  # 速率限制
  rate_limit:
    requests_per_minute: 60    # 每分钟最大请求数
    requests_per_hour: 1000    # 每小时最大请求数
    burst_capacity: 10         # 突发容量
    
  # 安全检查阈值
  security_check:
    max_failed_attempts: 5     # 最大失败尝试次数
    lockout_duration: 300      # 锁定持续时间（秒）

# 监控和告警阈值
monitoring:
  # 错误率阈值
  error_rate:
    warning: 0.05              # 错误率警告阈值（5%）
    critical: 0.1              # 错误率严重阈值（10%）
    
  # 可用性阈值
  availability:
    target: 0.99               # 目标可用性（99%）
    warning: 0.95              # 可用性警告阈值（95%）
    
  # 资源使用告警
  resource_alerts:
    disk_usage_warning: 0.8    # 磁盘使用警告阈值（80%）
    disk_usage_critical: 0.9   # 磁盘使用严重阈值（90%）

# 业务逻辑阈值
business:
  # 需求收集阈值
  requirement_gathering:
    min_required_points: 3     # 最少必需关注点数量
    completion_threshold: 0.8  # 完成阈值
    
  # 文档生成阈值
  document_generation:
    min_content_length: 100    # 最小内容长度
    max_content_length: 50000  # 最大内容长度
    
  # 质量评估阈值
  quality:
    min_quality_score: 0.6     # 最低质量分数
    excellent_quality: 0.9     # 优秀质量分数

# 开发和调试阈值
development:
  # 日志级别阈值
  logging:
    debug_enabled: false       # 是否启用调试日志
    verbose_logging: false     # 是否启用详细日志
    
  # 测试配置
  testing:
    test_timeout: 60           # 测试超时时间（秒）
    max_test_iterations: 100   # 最大测试迭代次数
    
  # 性能分析
  profiling:
    enable_profiling: false    # 是否启用性能分析
    sampling_rate: 0.01        # 采样率（1%）

# 环境特定阈值
environment:
  # 开发环境
  development:
    relaxed_timeouts: true     # 是否使用宽松的超时设置
    debug_mode: true           # 是否启用调试模式
    
  # 生产环境
  production:
    strict_validation: true    # 是否启用严格验证
    performance_monitoring: true # 是否启用性能监控
    
  # 测试环境
  testing:
    mock_external_services: true # 是否模拟外部服务
    fast_timeouts: true           # 是否使用快速超时
