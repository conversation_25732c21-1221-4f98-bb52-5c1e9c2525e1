# 数据库查询模板配置文件
# 用于管理系统中所有的SQL查询模板

# 关注点相关查询
focus_points:
  # 检查关注点是否存在
  check_exists: |
    SELECT 1 FROM concern_point_coverage 
    WHERE conversation_id = ? AND focus_id = ?
    
  # 重置关注点状态
  reset_status: |
    UPDATE concern_point_coverage 
    SET status = 'pending' 
    WHERE conversation_id = ?
    
  # 更新关注点状态
  update_status: |
    UPDATE concern_point_coverage 
    SET status = ?, updated_at = ?
    WHERE conversation_id = ? AND focus_id = ?
    
  # 更新关注点状态和值
  update_status_with_value: |
    UPDATE concern_point_coverage 
    SET status = ?, extracted_info = ?, updated_at = ?
    WHERE conversation_id = ? AND focus_id = ?
    
  # 更新关注点完整状态
  update_complete_status: |
    UPDATE concern_point_coverage 
    SET status = ?, extracted_info = ?, is_covered = ?, attempts = attempts + 1, updated_at = ?
    WHERE conversation_id = ? AND focus_id = ?
    
  # 插入新的关注点记录
  insert_new: |
    INSERT INTO concern_point_coverage
    (conversation_id, focus_id, status, attempts, is_covered, extracted_info, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?)
    
  # 获取关注点状态
  get_status: |
    SELECT focus_id, status, attempts, extracted_info, is_covered, updated_at
    FROM concern_point_coverage
    WHERE conversation_id = ?
    
  # 获取特定关注点状态
  get_single_status: |
    SELECT status, extracted_info, is_covered, attempts, updated_at
    FROM concern_point_coverage
    WHERE conversation_id = ? AND focus_id = ?
    
  # 获取正在处理的关注点
  get_processing: |
    SELECT focus_id FROM concern_point_coverage
    WHERE conversation_id = ? AND status = 'processing'
    
  # 获取待处理的关注点
  get_pending: |
    SELECT focus_id FROM concern_point_coverage
    WHERE conversation_id = ? AND status = 'pending'
    ORDER BY updated_at ASC
    
  # 获取已完成的关注点
  get_completed: |
    SELECT focus_id, extracted_info FROM concern_point_coverage
    WHERE conversation_id = ? AND status = 'completed'
    
  # 清理处理中状态
  clear_processing: |
    UPDATE concern_point_coverage 
    SET status = 'pending'
    WHERE conversation_id = ? AND status = 'processing'
    
  # 批量插入关注点
  batch_insert: |
    INSERT INTO concern_point_coverage
    (conversation_id, focus_id, status, attempts, is_covered, extracted_info, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?)

# 会话相关查询
conversations:
  # 检查会话是否存在
  check_exists: |
    SELECT 1 FROM conversations 
    WHERE conversation_id = ?
    
  # 创建新会话
  create_new: |
    INSERT INTO conversations
    (conversation_id, user_id, status, created_at, updated_at, last_activity_at)
    VALUES (?, ?, ?, ?, ?, ?)
    
  # 更新会话的领域和类别
  update_domain_category: |
    UPDATE conversations 
    SET domain_id = ?, category_id = ?, updated_at = ?
    WHERE conversation_id = ?
    
  # 获取会话信息
  get_info: |
    SELECT conversation_id, user_id, domain_id, category_id, status, 
           created_at, updated_at, last_activity_at
    FROM conversations
    WHERE conversation_id = ?
    
  # 获取会话的领域和类别
  get_domain_category: |
    SELECT domain_id, category_id 
    FROM conversations 
    WHERE conversation_id = ?
    
  # 更新会话状态
  update_status: |
    UPDATE conversations 
    SET status = ?, updated_at = ?, last_activity_at = ?
    WHERE conversation_id = ?
    
  # 清除会话的领域和类别信息
  clear_domain_category: |
    UPDATE conversations 
    SET domain_id = NULL, category_id = NULL, updated_at = ?
    WHERE conversation_id = ?
    
  # 获取活跃会话
  get_active: |
    SELECT conversation_id, last_activity_at
    FROM conversations
    WHERE status = 'active' AND last_activity_at > ?
    
  # 获取过期会话
  get_expired: |
    SELECT conversation_id
    FROM conversations
    WHERE status = 'active' AND last_activity_at < ?
    
  # 删除过期会话
  delete_expired: |
    DELETE FROM conversations
    WHERE conversation_id IN (
      SELECT conversation_id FROM conversations
      WHERE status = 'active' AND last_activity_at < ?
    )

# 消息相关查询
messages:
  # 保存消息
  save_message: |
    INSERT INTO messages
    (conversation_id, role, content, timestamp, metadata)
    VALUES (?, ?, ?, ?, ?)
    
  # 获取会话历史
  get_history: |
    SELECT role, content, timestamp, metadata
    FROM messages
    WHERE conversation_id = ?
    ORDER BY timestamp ASC
    LIMIT ?
    
  # 获取最近的消息
  get_recent: |
    SELECT role, content, timestamp
    FROM messages
    WHERE conversation_id = ?
    ORDER BY timestamp DESC
    LIMIT ?
    
  # 获取第一条用户消息
  get_first_user_message: |
    SELECT content
    FROM messages
    WHERE conversation_id = ? AND role = 'user'
    ORDER BY timestamp ASC
    LIMIT 1
    
  # 获取最后一条消息
  get_last_message: |
    SELECT role, content, timestamp
    FROM messages
    WHERE conversation_id = ?
    ORDER BY timestamp DESC
    LIMIT 1
    
  # 删除会话的所有消息
  delete_by_conversation: |
    DELETE FROM messages
    WHERE conversation_id = ?
    
  # 获取消息统计
  get_stats: |
    SELECT 
      COUNT(*) as total_messages,
      COUNT(CASE WHEN role = 'user' THEN 1 END) as user_messages,
      COUNT(CASE WHEN role = 'assistant' THEN 1 END) as assistant_messages
    FROM messages
    WHERE conversation_id = ?

# 文档相关查询
documents:
  # 保存文档
  save_document: |
    INSERT INTO documents
    (document_id, conversation_id, project_name, content, status, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?)
    
  # 获取文档内容
  get_content: |
    SELECT content, status, created_at, updated_at
    FROM documents
    WHERE document_id = ?
    
  # 根据会话ID获取文档
  get_by_conversation: |
    SELECT document_id, project_name, content, status, created_at, updated_at
    FROM documents
    WHERE conversation_id = ?
    ORDER BY created_at DESC
    
  # 更新文档内容
  update_content: |
    UPDATE documents
    SET content = ?, status = ?, updated_at = ?
    WHERE document_id = ?
    
  # 更新文档状态
  update_status: |
    UPDATE documents
    SET status = ?, updated_at = ?
    WHERE document_id = ?
    
  # 检查文档是否存在
  check_exists: |
    SELECT 1 FROM documents
    WHERE conversation_id = ?
    
  # 删除文档
  delete_document: |
    DELETE FROM documents
    WHERE document_id = ?
    
  # 获取文档列表
  get_list: |
    SELECT document_id, conversation_id, project_name, status, created_at
    FROM documents
    WHERE conversation_id = ?
    ORDER BY created_at DESC

# 摘要相关查询
summaries:
  # 保存摘要
  save_summary: |
    INSERT OR REPLACE INTO conversation_summaries
    (conversation_id, summary_json, updated_at)
    VALUES (?, ?, ?)
    
  # 获取摘要
  get_summary: |
    SELECT summary_json, updated_at
    FROM conversation_summaries
    WHERE conversation_id = ?
    
  # 更新摘要
  update_summary: |
    UPDATE conversation_summaries
    SET summary_json = ?, updated_at = ?
    WHERE conversation_id = ?
    
  # 删除摘要
  delete_summary: |
    DELETE FROM conversation_summaries
    WHERE conversation_id = ?
    
  # 检查摘要是否存在
  check_exists: |
    SELECT 1 FROM conversation_summaries
    WHERE conversation_id = ?

# 系统维护查询
maintenance:
  # 清理过期数据
  cleanup_expired_sessions: |
    DELETE FROM conversations
    WHERE status = 'expired' AND updated_at < ?
    
  # 清理孤立的关注点记录
  cleanup_orphaned_focus_points: |
    DELETE FROM concern_point_coverage
    WHERE conversation_id NOT IN (
      SELECT conversation_id FROM conversations
    )
    
  # 清理孤立的消息记录
  cleanup_orphaned_messages: |
    DELETE FROM messages
    WHERE conversation_id NOT IN (
      SELECT conversation_id FROM conversations
    )
    
  # 获取数据库统计信息
  get_stats: |
    SELECT 
      (SELECT COUNT(*) FROM conversations) as total_conversations,
      (SELECT COUNT(*) FROM messages) as total_messages,
      (SELECT COUNT(*) FROM documents) as total_documents,
      (SELECT COUNT(*) FROM concern_point_coverage) as total_focus_points
    
  # 获取表大小信息
  get_table_sizes: |
    SELECT 
      name as table_name,
      COUNT(*) as row_count
    FROM sqlite_master 
    WHERE type = 'table' AND name NOT LIKE 'sqlite_%'
    GROUP BY name

# 性能优化查询
performance:
  # 创建索引（如果不存在）
  create_indexes: |
    CREATE INDEX IF NOT EXISTS idx_conversations_id ON conversations(conversation_id);
    CREATE INDEX IF NOT EXISTS idx_messages_conversation ON messages(conversation_id);
    CREATE INDEX IF NOT EXISTS idx_focus_points_conversation ON concern_point_coverage(conversation_id);
    CREATE INDEX IF NOT EXISTS idx_documents_conversation ON documents(conversation_id);
    
  # 分析表统计信息
  analyze_tables: |
    ANALYZE conversations;
    ANALYZE messages;
    ANALYZE concern_point_coverage;
    ANALYZE documents;
    
  # 清理数据库
  vacuum_database: |
    VACUUM;

# 备份和恢复查询
backup:
  # 导出会话数据
  export_conversation: |
    SELECT 
      c.conversation_id,
      c.user_id,
      c.domain_id,
      c.category_id,
      c.status,
      c.created_at,
      c.updated_at,
      GROUP_CONCAT(m.role || ':' || m.content, '|||') as messages
    FROM conversations c
    LEFT JOIN messages m ON c.conversation_id = m.conversation_id
    WHERE c.conversation_id = ?
    GROUP BY c.conversation_id
    
  # 导出所有数据
  export_all: |
    SELECT 'conversations' as table_name, COUNT(*) as count FROM conversations
    UNION ALL
    SELECT 'messages' as table_name, COUNT(*) as count FROM messages
    UNION ALL
    SELECT 'documents' as table_name, COUNT(*) as count FROM documents
    UNION ALL
    SELECT 'concern_point_coverage' as table_name, COUNT(*) as count FROM concern_point_coverage
