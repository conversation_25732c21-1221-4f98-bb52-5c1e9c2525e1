"""信息提取代理配置管理

这个模块定义了信息提取代理使用的所有配置参数，
包括LLM参数、阈值设置、重试策略等。
"""

from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class InformationExtractorConfig:
    """信息提取代理配置类
    
    集中管理所有信息提取相关的配置参数，
    便于统一调整和维护。
    """
    
    # LLM调用参数
    extraction_temperature: float = 0.3  # 信息提取时的温度参数，较低确保一致性
    question_generation_temperature: float = 0.7  # 问题生成时的温度参数，较高增加多样性
    clarification_temperature: float = 0.5  # 澄清回答时的温度参数
    
    # 完整性和质量阈值
    completeness_threshold: float = 0.6  # 信息完整性阈值，超过此值认为提取成功
    confidence_threshold: float = 0.7  # 置信度阈值
    quality_threshold: float = 0.5  # 质量评估阈值
    
    # 重试和错误处理
    max_retry_attempts: int = 3  # 最大重试次数
    timeout_seconds: int = 30  # 请求超时时间
    
    # 提示词相关配置
    max_focus_points_per_batch: int = 5  # 每批次处理的最大关注点数量
    context_window_size: int = 2000  # 上下文窗口大小（字符数）
    
    # 日志和调试
    enable_debug_logging: bool = False  # 是否启用详细日志
    log_llm_requests: bool = True  # 是否记录LLM请求
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式
        
        Returns:
            Dict[str, Any]: 配置参数字典
        """
        return {
            'extraction_temperature': self.extraction_temperature,
            'question_generation_temperature': self.question_generation_temperature,
            'clarification_temperature': self.clarification_temperature,
            'completeness_threshold': self.completeness_threshold,
            'confidence_threshold': self.confidence_threshold,
            'quality_threshold': self.quality_threshold,
            'max_retry_attempts': self.max_retry_attempts,
            'timeout_seconds': self.timeout_seconds,
            'max_focus_points_per_batch': self.max_focus_points_per_batch,
            'context_window_size': self.context_window_size,
            'enable_debug_logging': self.enable_debug_logging,
            'log_llm_requests': self.log_llm_requests
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'InformationExtractorConfig':
        """从字典创建配置实例
        
        Args:
            config_dict: 配置参数字典
            
        Returns:
            InformationExtractorConfig: 配置实例
        """
        return cls(**config_dict)
    
    def get_llm_params(self, operation_type: str = 'extraction') -> Dict[str, Any]:
        """获取特定操作的LLM参数
        
        Args:
            operation_type: 操作类型 ('extraction', 'question_generation', 'clarification')
            
        Returns:
            Dict[str, Any]: LLM调用参数
        """
        temperature_map = {
            'extraction': self.extraction_temperature,
            'question_generation': self.question_generation_temperature,
            'clarification': self.clarification_temperature
        }
        
        return {
            'temperature': temperature_map.get(operation_type, self.extraction_temperature),
            'timeout': self.timeout_seconds
        }


# 默认配置实例
DEFAULT_CONFIG = InformationExtractorConfig()