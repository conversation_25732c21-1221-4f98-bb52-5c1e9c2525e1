"""
基础配置设置
"""
from pathlib import Path
from typing import Dict, Any
import os
from dotenv import load_dotenv
from pydantic import BaseModel, Field
import logging

# Agent 类型枚举
# 系统中所有Agent类型的常量定义，用于在代码中统一引用
# Key: 大写的常量名称
# Value: 对应的小写标识符
AGENT_TYPES = {
    "DOMAIN_CLASSIFIER": "domain_classifier",      # 领域分类器：负责识别和分类用户输入的领域
    "INTENT_RECOGNITION": "intent_recognition",    # 意图识别器：分析用户意图

    "INFORMATION_EXTRACTOR": "information_extractor", # 信息提取器：从对话中提取关键信息
    "CONVERSATION_FLOW": "conversation_flow",      # 对话流程管理器：控制整体对话流程
    "USER_INTERACTION": "user_interaction",        # 用户交互处理器：处理与用户的直接交互
    "VALUE_EXTRACTOR": "value_extractor",         # 值提取器：提取对话中的具体数值或属性
    "QUESTION_POLISHER": "question_polisher",      # 问题润色器：优化和改进问题表述
    "DOCUMENT_GENERATOR": "document_generator",    # 文档生成器：生成最终的需求文档
    "REVIEW_AND_REFINE": "review_and_refine",     # 审核与优化器：审查和完善生成的内容
    "CATEGORY_CLASSIFIER": "category_classifier",  # 类别分类器：对需求进行分类
    "KNOWLEDGE_BASE": "knowledge_base"            # 知识库：管理和访问系统知识库
}

# Agent 实例名称配置  
AGENT_NAMES = {
    "domain_classifier": "AutoGenDomainClassifierAgent",
    "intent_recognition": "IntentRecognitionAgent",

    "information_extractor": "InformationExtractorAgent",
    "conversation_flow": "AutoGenConversationFlowAgent",
    "user_interaction": "UserInteractionAgent",
    "value_extractor": "ValueExtractorAgent",
    "question_polisher": "QuestionPolisherAgent",
    "document_generator": "DocumentGenerator",
    "review_and_refine": "AutoGenReviewAndRefineAgent",
    "category_classifier": "CategoryClassifierAgent",
    "knowledge_base": "KnowledgeBaseAgent"
}

# 加载环境变量
env_path = Path(__file__).resolve().parent.parent.parent / '.env'
load_dotenv(env_path)

# 项目根目录
BASE_DIR = Path(__file__).parent.parent

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# 确保日志目录存在并有写入权限
LOG_DIR = BASE_DIR.parent / "logs"
LOG_DIR.mkdir(exist_ok=True)
LOG_LEVEL = "DEBUG"  # 改为DEBUG级别记录更详细信息
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
LOG_BACKUP_COUNT = 5  # 保留5个日志文件

# 测试日志目录权限
try:
    test_log_file = LOG_DIR / "test_write.log"
    with open(test_log_file, "w") as f:
        f.write("测试写入权限\n")
    os.remove(test_log_file)
    logger.info(f"日志目录 {LOG_DIR} 可写")
except Exception as e:
    logger.error(f"日志目录 {LOG_DIR} 写入测试失败: {str(e)}")
    # 尝试使用当前目录
    LOG_DIR = BASE_DIR / "logs"
    LOG_DIR.mkdir(exist_ok=True)
    logger.info(f"改用备用日志目录: {LOG_DIR}")

# 文件日志处理器已由统一日志系统管理
logger.info("使用统一日志系统，无需单独配置文件处理器")

class CircuitBreakerConfig(BaseModel):
    failure_threshold: int = Field(default=8, env="LLM_CIRCUIT_BREAKER_FAILURE_THRESHOLD")
    recovery_time: int = Field(default=60, env="LLM_CIRCUIT_BREAKER_RECOVERY_TIME")

class LLMConfig(BaseModel):
    provider: str
    api_key: str = Field(env="DEEPSEEK_API_KEY")
    timeout: float = Field(default=45.0, env="LLM_TIMEOUT")
    max_retries: int = Field(default=5, env="LLM_MAX_RETRIES")
    circuit_breaker: CircuitBreakerConfig = CircuitBreakerConfig()

    class Config:
        env_prefix = "LLM_"

# 检查必要的环境变量
def get_required_env(key: str, default: str = "") -> str:
    """获取必要的环境变量，如果不存在则使用默认值"""
    value = os.getenv(key, default)
    return value

# LLM模型配置
LLM_CONFIGS = {
    "deepseek-chat": {
        "provider": "deepseek",
        "api_key": get_required_env("DEEPSEEK_API_KEY"),
        "api_base": "https://api.deepseek.com",
        "model_name": "deepseek-chat",  # 使用官方文档中的模型名称
        "temperature": 0.7,
        "max_tokens": 4000,
        "top_p": 1,
        "timeout": 45,  # 45秒超时
        "max_retries": 3,
    },
    "doubao-pro-32k": {
        "provider": "doubao",
        "api_key": get_required_env("DOUBAO_API_KEY"),
        "api_base": "https://ark.cn-beijing.volces.com/api/v3",
        "model_name": "doubao-pro-32k-241215",
        "temperature": 0.7,
        "max_tokens": 8000,
        "top_p": 1.0,
        "timeout": 45,  # 45秒超时
        "max_retries": 3,
    },
    "doubao-1.5-Lite": { # 用于领域、类别分类
        "provider": "doubao",
        "api_key": get_required_env("DOUBAO_API_KEY"),
        "api_base": "https://ark.cn-beijing.volces.com/api/v3",
        "model_name": "doubao-1-5-lite-32k-250115",
        "temperature": 0.7,
        "max_tokens": 8000,
        "top_p": 1.0,
        "timeout": 45,  # 45秒超时
        "max_retries": 3,
    },

    "qwen-plus": {
        "provider": "qwen",
        "api_key": get_required_env("QWEN_API_KEY"),
        "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "model_name": "qwen-plus",
        "temperature": 0.7,
        "max_tokens": 8000,
        "top_p": 1.0,
        "timeout": 45,  # 45秒超时
        "max_retries": 3,
    },
    "qwen-max": {
        "provider": "qwen",
        "api_key": get_required_env("QWEN_API_KEY"),
        "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "model_name": "qwen-max",
        "temperature": 0.7,
        "max_tokens": 8000,
        "top_p": 1.0,
        "timeout": 50,  # 文档生成任务需要更长时间，设置50秒超时
        "max_retries": 3,
    },
    "qwen-turbo-latest": {
        "provider": "qwen",
        "api_key": get_required_env("QWEN_API_KEY"),
        "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "model_name": "qwen-turbo-latest",
        "temperature": 0.5,
        "max_tokens": 8000,
        "top_p": 1.0,
        "timeout": 45,  # 45秒超时
        "max_retries": 3,
    },
    "qwen-intent": {        # 用于意图识别的专用模型
        "provider": "qwen",
        "api_key": get_required_env("QWEN_API_KEY"),
        "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "model_name": "tongyi-intent-detect-v3",
        "temperature": 0.7,
        "max_tokens": 4000,
        "top_p": 1.0,
        "timeout": 50,  
        "max_retries": 3,
    },
    "openrouter-gemini-flash": {
        "provider": "openrouter",
        "api_key": get_required_env("OPENROUTER_API_KEY"),
        "api_base": "https://openrouter.ai/api/v1",
        "model_name": "google/gemini-2.5-flash-preview-05-20",
        "temperature": 0.7,
        "max_tokens": 7000,
        "top_p": 1.0,
        "timeout": 45,  # 45秒超时
        "max_retries": 3,
    }
}

# 默认Agent与模型映射关系
SCENARIO_LLM_MAPPING = {
    # 低成本、高速度的分类任务
    "domain_classifier": "doubao-1.5-Lite",
    "category_classifier": "doubao-1.5-Lite",

    # 需要一定理解能力，但也要兼顾速度和成本
    "intent_recognition": "doubao-pro-32k",  # 改用更强的模型提高意图识别准确性
    "information_extractor": "openrouter-gemini-flash",
    "value_extractor": "doubao-pro-32k",
    "question_polisher": "doubao-pro-32k",
    "clarification_generator": "doubao-pro-32k",
    "clarification_request_generator": "doubao-pro-32k",  # 新增：澄清请求生成器

    # 新增：用户交互处理
    "user_interaction": "doubao-pro-32k",
    # 新增：知识库访问
    "knowledge_base": "qwen-plus",

    # 核心的、需要强大逻辑和流程控制的任务
    "conversation_flow": "qwen-max",

    # 高价值的生成和审查任务
    "document_generator": "openrouter-gemini-flash",
    "review_and_refine": "openrouter-gemini-flash",
}

# 默认模型配置
DEFAULT_MODEL = "deepseek-chat"

# 数据库配置
DATABASE_PATH = BASE_DIR / "data" / "aidatabase.db"

# 会话配置
SESSION_TIMEOUT = 3600  # 会话超时时间(秒)

# API超时配置
API_REQUEST_TIMEOUT = 60.0  # API请求超时时间(秒)，应该比LLM超时时间更长


