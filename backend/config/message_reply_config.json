{"version": "1.0", "description": "消息回复系统配置文件", "last_updated": "2025-06-20", "general": {"default_language": "zh-CN", "fallback_enabled": true, "llm_timeout": 10, "max_retry_attempts": 3, "enable_analytics": true, "enable_a_b_testing": false, "supported_languages": ["zh-CN", "en-US"]}, "reply_categories": {"greeting": {"enabled": true, "priority": 1, "fallback_template": "greeting", "description": "问候类回复"}, "confirmation": {"enabled": true, "priority": 1, "fallback_template": "reset_confirmation", "description": "确认类回复"}, "error": {"enabled": true, "priority": 1, "fallback_template": "system_error", "description": "错误类回复"}, "guidance": {"enabled": true, "priority": 1, "fallback_template": "initial_guidance", "description": "引导类回复"}, "clarification": {"enabled": true, "priority": 1, "fallback_template": "clarification_request", "description": "澄清类回复"}, "empathy": {"enabled": true, "priority": 1, "fallback_template": "empathy_fallback", "description": "共情类回复"}, "completion": {"enabled": true, "priority": 1, "fallback_template": "document_finalized", "description": "完成类回复"}, "system": {"enabled": true, "priority": 1, "fallback_template": "system_error", "description": "系统类回复"}}, "static_templates": {"reset_confirmation": {"content": "好的，我已重置会话。请告诉我您的新需求。", "category": "confirmation", "version": "1.0", "variables": []}, "document_finalized": {"content": "感谢您的确认！需求文档已最终确定。如果您有新的需求，请随时告诉我。", "category": "completion", "version": "1.0", "variables": []}, "modification_error": {"content": "抱歉，修改文档时出现错误，请稍后再试。", "category": "error", "version": "1.0", "variables": []}, "system_error": {"content": "系统处理您的请求时遇到问题，请稍后再试。", "category": "error", "version": "1.0", "variables": []}, "default_requirement_prompt": {"content": "请描述您的详细需求。", "category": "guidance", "version": "1.0", "variables": []}, "document_generated": {"content": "所有必要信息已收集完毕，我已根据您提供的信息生成了需求文档。请查看并确认：", "category": "completion", "version": "1.0", "variables": []}, "initial_guidance": {"content": "请您先告诉我您想做什么，例如\"我想开发一个软件\"或\"我需要设计一个Logo\"。", "category": "guidance", "version": "1.0", "variables": []}, "processing_error": {"content": "处理您的请求时出错: {error_msg}", "category": "error", "version": "1.0", "variables": ["error_msg"]}, "unknown_action": {"content": "抱歉，我好像遇到了一点内部问题，我们换个话题继续吧？", "category": "system", "version": "1.0", "variables": []}, "greeting": {"content": "您好！很高兴为您服务。请问有什么可以帮您？", "category": "greeting", "version": "1.0", "variables": []}, "clarification_request": {"content": "抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。", "category": "clarification", "version": "1.0", "variables": []}, "document_refinement": {"content": "非常抱歉文档未能让您满意。为了能更正错误，您能具体指出需要修改的部分吗？", "category": "clarification", "version": "1.0", "variables": []}, "domain_info_error": {"content": "抱歉，暂时无法获取领域信息。请稍后再试。", "category": "error", "version": "1.0", "variables": []}, "specific_requirement_help": {"content": "请描述您的具体需求，我将为您提供帮助。", "category": "guidance", "version": "1.0", "variables": []}, "domain_info_fetch_error": {"content": "获取领域信息时出错，请稍后再试。", "category": "error", "version": "1.0", "variables": []}, "document_generation_failed": {"content": "抱歉，文档生成失败，请稍后再试。您可以选择：\n1. 重新尝试生成文档\n2. 修改需求后重试", "category": "error", "version": "1.0", "variables": []}, "document_generator_not_initialized": {"content": "抱歉，文档生成器未初始化，无法生成文档。请联系管理员解决此问题。", "category": "error", "version": "1.0", "variables": []}, "document_not_found": {"content": "抱歉，未能找到生成的文档。", "category": "error", "version": "1.0", "variables": []}, "domain_category_error": {"content": "抱歉，目前无法为\"{domain_name}\"领域的\"{category_name}\"类别提供具体的指引。您可以尝试描述一下您的需求吗？", "category": "error", "version": "1.0", "variables": ["domain_name", "category_name"]}}, "dynamic_generators": {"empathy_and_clarify": {"agent_name": "empathy_generator", "temperature": 0.7, "max_tokens": 200, "fallback_template": "clarification_request", "description": "共情并澄清问题", "enabled": true}, "greeting_response": {"agent_name": "greeting_generator", "temperature": 0.7, "max_tokens": 150, "fallback_template": "greeting", "description": "动态问候回复", "enabled": true}, "clarification_response": {"agent_name": "clarification_generator", "temperature": 0.7, "max_tokens": 200, "fallback_template": "clarification_request", "description": "澄清问题回复", "enabled": true}, "apology_response": {"agent_name": "apology_generator", "temperature": 0.7, "max_tokens": 200, "fallback_template": "document_refinement", "description": "道歉和请求改进", "enabled": true}}, "a_b_testing": {"enabled": false, "test_groups": {"greeting": {"variant_a": "greeting", "variant_b": "greeting_friendly", "traffic_split": 0.5}}}, "analytics": {"enabled": true, "track_user_satisfaction": true, "track_response_time": true, "track_fallback_usage": true, "export_interval_hours": 24}, "multilingual": {"enabled": false, "auto_detect_language": false, "supported_languages": {"zh-CN": {"name": "简体中文", "enabled": true}, "en-US": {"name": "English", "enabled": false}}}}