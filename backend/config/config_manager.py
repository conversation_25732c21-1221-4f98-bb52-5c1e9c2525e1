# -*- coding: utf-8 -*-
"""
统一配置管理器
用于管理所有硬编码配置，支持热更新和配置验证
"""

import yaml
import json
import logging
from pathlib import Path
from typing import Any, Dict, Optional, Union
from dataclasses import dataclass
from threading import Lock
import time

logger = logging.getLogger(__name__)

@dataclass
class ConfigCache:
    """配置缓存项"""
    data: Dict[str, Any]
    last_modified: float
    file_path: Path

class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self, config_dir: str = "backend/config"):
        self.config_dir = Path(config_dir)
        self._cache: Dict[str, ConfigCache] = {}
        self._lock = Lock()
        self._load_all_configs()
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        config_files = [
            "business_rules.yaml",
            "message_templates.yaml", 
            "thresholds.yaml",
            "database_queries.yaml"
        ]
        
        for config_file in config_files:
            try:
                self._load_config(config_file)
                logger.info(f"成功加载配置文件: {config_file}")
            except Exception as e:
                logger.error(f"加载配置文件 {config_file} 失败: {e}")
    
    def _load_config(self, filename: str) -> Dict[str, Any]:
        """加载单个配置文件"""
        file_path = self.config_dir / filename
        
        if not file_path.exists():
            logger.warning(f"配置文件不存在: {file_path}")
            return {}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if filename.endswith('.yaml') or filename.endswith('.yml'):
                    data = yaml.safe_load(f)
                elif filename.endswith('.json'):
                    data = json.load(f)
                else:
                    logger.error(f"不支持的配置文件格式: {filename}")
                    return {}
            
            # 缓存配置
            with self._lock:
                self._cache[filename] = ConfigCache(
                    data=data or {},
                    last_modified=file_path.stat().st_mtime,
                    file_path=file_path
                )
            
            return data or {}
            
        except Exception as e:
            logger.error(f"解析配置文件 {filename} 失败: {e}")
            return {}
    
    def _get_config(self, filename: str, auto_reload: bool = True) -> Dict[str, Any]:
        """获取配置，支持自动重载"""
        if auto_reload:
            self._check_and_reload(filename)
        
        with self._lock:
            cache_item = self._cache.get(filename)
            if cache_item:
                return cache_item.data
            else:
                return self._load_config(filename)
    
    def _check_and_reload(self, filename: str):
        """检查文件是否修改，如果修改则重新加载"""
        with self._lock:
            cache_item = self._cache.get(filename)
            if not cache_item:
                return
            
            try:
                current_mtime = cache_item.file_path.stat().st_mtime
                if current_mtime > cache_item.last_modified:
                    logger.info(f"检测到配置文件 {filename} 已修改，重新加载")
                    self._load_config(filename)
            except Exception as e:
                logger.error(f"检查配置文件 {filename} 修改时间失败: {e}")
    
    # ==================== 业务规则配置 ====================
    
    def get_threshold(self, key: str, default: float = 0.0) -> float:
        """获取阈值配置"""
        thresholds = self._get_config("thresholds.yaml")
        
        # 支持嵌套键，如 "extraction.completeness_threshold"
        keys = key.split('.')
        value = thresholds
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                logger.warning(f"阈值配置 '{key}' 不存在，使用默认值: {default}")
                return default
        
        if isinstance(value, (int, float)):
            return float(value)
        else:
            logger.warning(f"阈值配置 '{key}' 不是数值类型，使用默认值: {default}")
            return default
    
    def get_business_rule(self, key: str, default: Any = None) -> Any:
        """获取业务规则配置"""
        rules = self._get_config("business_rules.yaml")
        
        keys = key.split('.')
        value = rules
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                logger.warning(f"业务规则 '{key}' 不存在，使用默认值: {default}")
                return default
        
        return value
    
    def get_message_template(self, key: str, **kwargs) -> str:
        """获取消息模板并替换占位符"""
        templates = self._get_config("message_templates.yaml")
        
        keys = key.split('.')
        template = templates
        
        for k in keys:
            if isinstance(template, dict) and k in template:
                template = template[k]
            else:
                logger.warning(f"消息模板 '{key}' 不存在")
                return f"[模板缺失: {key}]"
        
        if not isinstance(template, str):
            logger.warning(f"消息模板 '{key}' 不是字符串类型")
            return f"[模板格式错误: {key}]"
        
        # 替换占位符
        try:
            return template.format(**kwargs)
        except KeyError as e:
            logger.warning(f"消息模板 '{key}' 缺少占位符: {e}")
            return template
        except Exception as e:
            logger.error(f"格式化消息模板 '{key}' 失败: {e}")
            return template
    
    def get_database_query(self, key: str) -> str:
        """获取数据库查询模板"""
        queries = self._get_config("database_queries.yaml")
        
        keys = key.split('.')
        query = queries
        
        for k in keys:
            if isinstance(query, dict) and k in query:
                query = query[k]
            else:
                logger.warning(f"数据库查询模板 '{key}' 不存在")
                return ""
        
        if isinstance(query, str):
            return query.strip()
        else:
            logger.warning(f"数据库查询模板 '{key}' 不是字符串类型")
            return ""
    
    # ==================== 配置验证 ====================
    
    def validate_config(self, filename: str) -> Dict[str, Any]:
        """验证配置文件的完整性"""
        result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        config = self._get_config(filename, auto_reload=False)
        
        if not config:
            result["valid"] = False
            result["errors"].append(f"配置文件 {filename} 为空或无法加载")
            return result
        
        # 根据不同配置文件进行特定验证
        if filename == "thresholds.yaml":
            self._validate_thresholds(config, result)
        elif filename == "business_rules.yaml":
            self._validate_business_rules(config, result)
        elif filename == "message_templates.yaml":
            self._validate_message_templates(config, result)
        
        return result
    
    def _validate_thresholds(self, config: Dict, result: Dict):
        """验证阈值配置"""
        required_thresholds = [
            "extraction.completeness_threshold",
            "extraction.confidence_threshold",
            "llm.max_timeout"
        ]
        
        for threshold_key in required_thresholds:
            keys = threshold_key.split('.')
            value = config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    result["errors"].append(f"缺少必需的阈值配置: {threshold_key}")
                    result["valid"] = False
                    break
            else:
                if not isinstance(value, (int, float)):
                    result["errors"].append(f"阈值配置 {threshold_key} 必须是数值类型")
                    result["valid"] = False
    
    def _validate_business_rules(self, config: Dict, result: Dict):
        """验证业务规则配置"""
        if "completeness" not in config:
            result["errors"].append("缺少 completeness 业务规则配置")
            result["valid"] = False
    
    def _validate_message_templates(self, config: Dict, result: Dict):
        """验证消息模板配置"""
        if "error_messages" not in config:
            result["warnings"].append("建议添加 error_messages 模板配置")
    
    # ==================== 工具方法 ====================
    
    def reload_all(self):
        """重新加载所有配置"""
        logger.info("重新加载所有配置文件")
        with self._lock:
            self._cache.clear()
        self._load_all_configs()
    
    def get_config_status(self) -> Dict[str, Any]:
        """获取配置状态信息"""
        status = {
            "loaded_configs": list(self._cache.keys()),
            "config_dir": str(self.config_dir),
            "last_check": time.time()
        }
        
        for filename, cache_item in self._cache.items():
            status[f"{filename}_last_modified"] = cache_item.last_modified
        
        return status

# 全局配置管理器实例
config_manager = ConfigManager()
