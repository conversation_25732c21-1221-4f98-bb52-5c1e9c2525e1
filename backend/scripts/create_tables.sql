-- 确保 PRAGMA foreign_keys 启用
PRAGMA foreign_keys = ON;
-- 1. domains（领域表）
CREATE TABLE domains (
    domain_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    code TEXT NOT NULL UNIQUE,
    -- 添加唯一代码字段，用于程序引用
    description TEXT NOT NULL,
);
-- 2. categories（类别表）
CREATE TABLE categories (
    category_id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    code TEXT NOT NULL,
    -- 添加代码字段，用于程序引用
    description TEXT NOT NULL,
    FOREIGN KEY (domain_id) REFERENCES domains(domain_id) ON DELETE CASCADE,
    UNIQUE (domain_id, name),
    UNIQUE (domain_id, code) -- 确保在同一领域内代码唯一
);
-- 3. concern_points（关注点表）
CREATE TABLE concern_points (
    concern_point_id INTEGER PRIMARY KEY AUTOINCREMENT,
    category_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    code TEXT NOT NULL,
    -- 添加代码字段，便于程序引用
    description TEXT NOT NULL,
    priority TEXT NOT NULL CHECK (priority IN ('P0', 'P1', 'P2')),
    example TEXT,
    required INTEGER NOT NULL DEFAULT 0,
    FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE CASCADE,
    UNIQUE (category_id, code) -- 确保在同一类别内代码唯一
);
-- 4. focus_point_definitions（关注点定义表）
CREATE TABLE focus_point_definitions (
    id TEXT PRIMARY KEY,
    domain TEXT NOT NULL,
    category TEXT NOT NULL,
    description TEXT NOT NULL,
    priority TEXT NOT NULL,
    type TEXT DEFAULT 'text',
    question TEXT,
    validation TEXT,
    required INTEGER DEFAULT 0,
    UNIQUE(domain, category, id)
);
-- 5. conversations（会话表）- 增强版
CREATE TABLE conversations (
    conversation_id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain_id INTEGER REFERENCES domains(domain_id),
    category_id INTEGER REFERENCES categories(category_id),
    classifier_domain_id INTEGER,
    -- 分类器识别的原始领域ID
    classifier_category TEXT,
    -- 分类器识别的原始类别
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
);
-- 6. messages（消息表）
CREATE TABLE messages (
    message_id INTEGER PRIMARY KEY AUTOINCREMENT,
    conversation_id INTEGER NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    -- 关联会话
    sender_type TEXT NOT NULL CHECK (sender_type IN ('user', 'ai')),
    -- 发送者：用户或 AI
    content TEXT NOT NULL,
    -- 消息内容
    created_at TEXT DEFAULT (datetime('now'))
);
-- 7. concern_point_coverage（关注点覆盖表）
CREATE TABLE concern_point_coverage (
    coverage_id INTEGER PRIMARY KEY AUTOINCREMENT,
    conversation_id INTEGER NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    -- 关联会话
    concern_point_id INTEGER NOT NULL REFERENCES concern_points(concern_point_id) ON DELETE CASCADE,
    -- 关联关注点
    is_covered INTEGER NOT NULL DEFAULT 0,
    -- 是否覆盖 (SQLite中布尔值用0/1表示)
    extracted_info TEXT,
    -- 提取或用户提供的关注点信息
    CONSTRAINT unique_conversation_concern UNIQUE (conversation_id, concern_point_id) -- 防止重复覆盖
);
-- 8. requirements（需求表）
CREATE TABLE requirements (
    requirement_id INTEGER PRIMARY KEY AUTOINCREMENT,
    conversation_id INTEGER NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    -- 关联会话
    concern_point_id INTEGER REFERENCES concern_points(concern_point_id),
    -- 可为空，非必需关联关注点
    content TEXT NOT NULL,
    -- 需求描述
);
-- 9. documents（文档表）
CREATE TABLE documents (
    document_id INTEGER PRIMARY KEY AUTOINCREMENT,
    conversation_id INTEGER NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    -- 关联会话
    version INTEGER NOT NULL DEFAULT 1,
    -- 版本号
    content TEXT NOT NULL,
    -- Markdown 格式文档
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'confirmed', 'rejected')),
    -- 状态：草稿、已确认、已拒绝
    feedback TEXT,
    -- 用户反馈
    CONSTRAINT unique_conversation_version UNIQUE (conversation_id, version) -- 确保版本唯一
);
-- 新增：领域分类映射表
CREATE TABLE domain_classification_mapping (
    mapping_id INTEGER PRIMARY KEY AUTOINCREMENT,
    classifier_domain_id INTEGER NOT NULL,
    -- 分类器识别的领域ID
    actual_domain_id INTEGER NOT NULL,
    -- 关注点系统中的领域ID
    classifier_category TEXT,
    -- 分类器识别的类别(可选)
    actual_category_id INTEGER,
    -- 关注点系统中的类别ID
    FOREIGN KEY (actual_domain_id) REFERENCES domains(domain_id),
    FOREIGN KEY (actual_category_id) REFERENCES categories(category_id),
    UNIQUE (classifier_domain_id, classifier_category)
);
-- 新增：类别识别结果表
CREATE TABLE category_recognition_results (
    result_id INTEGER PRIMARY KEY AUTOINCREMENT,
    conversation_id INTEGER NOT NULL,
    input_text TEXT NOT NULL,
    recognized_domain_id INTEGER,
    recognized_category_id INTEGER,
    confidence REAL,
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    FOREIGN KEY (recognized_domain_id) REFERENCES domains(domain_id),
    FOREIGN KEY (recognized_category_id) REFERENCES categories(category_id)
);
-- 索引优化
CREATE INDEX idx_categories_domain_id ON categories(domain_id);
CREATE INDEX idx_concern_points_category_id ON concern_points(category_id);
CREATE INDEX idx_conversations_domain_id ON conversations(domain_id);
CREATE INDEX idx_conversations_category_id ON conversations(category_id);
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_concern_point_coverage_conversation_id ON concern_point_coverage(conversation_id);
CREATE INDEX idx_requirements_conversation_id ON requirements(conversation_id);
CREATE INDEX idx_documents_conversation_id ON documents(conversation_id);
-- 添加额外索引
CREATE INDEX idx_concern_points_code ON concern_points(code);
CREATE INDEX idx_categories_code ON categories(code);
CREATE INDEX idx_domains_code ON domains(code);
CREATE INDEX idx_domain_classification_mapping_classifier ON domain_classification_mapping(classifier_domain_id, classifier_category);