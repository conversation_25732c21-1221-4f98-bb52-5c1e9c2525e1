"""
初始化关注点状态管理相关的数据库表
"""
import sqlite3
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 直接设置数据库路径，避免导入settings
DB_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "aidatabase.db")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def init_database():
    """初始化数据库表结构"""
    db_path = DB_PATH

    # 确保数据库目录存在
    db_dir = Path(db_path).parent
    db_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"初始化数据库: {db_path}")

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 创建会话表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS conversations (
            conversation_id TEXT PRIMARY KEY,
            user_id TEXT,
            domain_id TEXT,
            category_id TEXT,
            status TEXT DEFAULT 'active' NOT NULL,
            created_at TEXT DEFAULT (datetime('now')),
            updated_at TEXT DEFAULT (datetime('now')),
            last_activity_at TEXT DEFAULT (datetime('now')),
            metadata TEXT
        )
        """)

        # 创建关注点覆盖状态表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS concern_point_coverage (
            coverage_id INTEGER PRIMARY KEY AUTOINCREMENT,
            conversation_id TEXT NOT NULL,
            focus_id TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            attempts INTEGER DEFAULT 0,
            is_covered INTEGER DEFAULT 0,
            extracted_info TEXT,
            updated_at TEXT DEFAULT (datetime('now')),
            FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id),
            UNIQUE(conversation_id, focus_id)
        )
        """)

        # 创建消息表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS messages (
            message_id INTEGER PRIMARY KEY AUTOINCREMENT,
            conversation_id TEXT NOT NULL,
            sender_type TEXT NOT NULL,
            content TEXT NOT NULL,
            focus_id TEXT,
            message_type TEXT,
            created_at TEXT DEFAULT (datetime('now')),
            FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id)
        )
        """)

        # 添加索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_concern_point_coverage_conversation_id ON concern_point_coverage(conversation_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_concern_point_coverage_focus_id ON concern_point_coverage(focus_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_messages_focus_id ON messages(focus_id)")

        conn.commit()
        logger.info("数据库表结构初始化完成")

    except sqlite3.Error as e:
        logger.error(f"数据库初始化失败: {e}")
        raise
    finally:
        if conn:
            conn.close()

def main():
    """主函数"""
    try:
        init_database()
        logger.info("数据库初始化成功")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
