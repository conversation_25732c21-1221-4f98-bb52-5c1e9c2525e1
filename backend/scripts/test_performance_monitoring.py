#!/usr/bin/env python3
"""
性能监控测试脚本

此脚本用于测试性能监控功能是否正常工作。
主要测试内容：
1. 性能监控器初始化
2. API调用性能跟踪
3. LLM调用性能跟踪
4. 数据库查询性能跟踪
5. 系统资源监控
6. 性能数据保存和加载
"""

import sys
import os
import time
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.utils.performance_monitor import performance_monitor, PerformanceMonitor
from backend.utils.performance_init import (
    init_performance_monitoring,
    shutdown_performance_features,
    get_performance_status
)
from backend.utils.performance_middleware import get_performance_stats

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_performance_monitor_initialization():
    """测试性能监控器初始化"""
    logger.info("=== 测试性能监控器初始化 ===")
    
    try:
        # 初始化性能监控
        monitor = init_performance_monitoring(
            enabled=True,
            data_dir="logs/test_performance",
            auto_save=False  # 测试时不自动保存
        )
        
        # 检查初始化状态
        status = get_performance_status()
        logger.info(f"监控状态: {status}")
        
        assert monitor.enabled, "性能监控应该已启用"
        assert monitor._initialized, "性能监控应该已初始化"
        
        logger.info("✅ 性能监控器初始化测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 性能监控器初始化测试失败: {str(e)}")
        return False


def test_api_performance_tracking():
    """测试API性能跟踪"""
    logger.info("=== 测试API性能跟踪 ===")
    
    try:
        # 模拟API调用
        @performance_monitor.track_api_call("test_endpoint")
        def mock_api_call():
            time.sleep(0.1)  # 模拟处理时间
            return "success"
        
        # 执行多次调用
        for i in range(5):
            result = mock_api_call()
            logger.info(f"API调用 {i+1}: {result}")
        
        # 检查指标
        api_metrics = performance_monitor.get_api_metrics()
        logger.info(f"API指标: {api_metrics}")
        
        assert "test_endpoint" in api_metrics, "应该记录test_endpoint的指标"
        assert api_metrics["test_endpoint"]["count"] == 5, "应该记录5次调用"
        
        logger.info("✅ API性能跟踪测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ API性能跟踪测试失败: {str(e)}")
        return False


def test_llm_performance_tracking():
    """测试LLM性能跟踪"""
    logger.info("=== 测试LLM性能跟踪 ===")
    
    try:
        # 模拟LLM调用
        with performance_monitor.track_llm_call("deepseek", "deepseek-chat", "completion"):
            time.sleep(0.2)  # 模拟LLM响应时间
            logger.info("模拟LLM调用完成")
        
        # 再次调用
        with performance_monitor.track_llm_call("deepseek", "deepseek-chat", "completion"):
            time.sleep(0.15)
            logger.info("模拟LLM调用完成")
        
        # 检查指标
        llm_metrics = performance_monitor.get_llm_metrics()
        logger.info(f"LLM指标: {llm_metrics}")
        
        expected_key = "deepseek_deepseek-chat_completion"
        assert expected_key in llm_metrics, f"应该记录{expected_key}的指标"
        assert llm_metrics[expected_key]["count"] == 2, "应该记录2次调用"
        
        logger.info("✅ LLM性能跟踪测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ LLM性能跟踪测试失败: {str(e)}")
        return False


def test_db_performance_tracking():
    """测试数据库性能跟踪"""
    logger.info("=== 测试数据库性能跟踪 ===")
    
    try:
        # 模拟数据库查询
        with performance_monitor.track_db_query("select", "users"):
            time.sleep(0.05)  # 模拟查询时间
            logger.info("模拟数据库查询完成")
        
        with performance_monitor.track_db_query("insert", "messages"):
            time.sleep(0.03)
            logger.info("模拟数据库插入完成")
        
        # 检查指标
        db_metrics = performance_monitor.get_db_metrics()
        logger.info(f"数据库指标: {db_metrics}")
        
        assert "select_users" in db_metrics, "应该记录select_users的指标"
        assert "insert_messages" in db_metrics, "应该记录insert_messages的指标"
        
        logger.info("✅ 数据库性能跟踪测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库性能跟踪测试失败: {str(e)}")
        return False


def test_resource_monitoring():
    """测试系统资源监控"""
    logger.info("=== 测试系统资源监控 ===")
    
    try:
        # 等待一段时间让资源监控收集数据
        logger.info("等待资源监控收集数据...")
        time.sleep(3)
        
        # 检查资源指标
        resource_metrics = performance_monitor.get_resource_metrics()
        logger.info(f"资源指标: {resource_metrics}")
        
        assert "avg_cpu" in resource_metrics, "应该有CPU使用率数据"
        assert "avg_memory" in resource_metrics, "应该有内存使用率数据"
        
        logger.info("✅ 系统资源监控测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 系统资源监控测试失败: {str(e)}")
        return False


def test_performance_data_persistence():
    """测试性能数据持久化"""
    logger.info("=== 测试性能数据持久化 ===")
    
    try:
        # 保存性能数据
        file_path = performance_monitor.save_metrics("test_performance_report.json")
        logger.info(f"性能数据已保存到: {file_path}")
        
        # 检查文件是否存在
        assert os.path.exists(file_path), "性能报告文件应该存在"
        
        # 检查文件内容
        import json
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        assert "api_metrics" in data, "报告应该包含API指标"
        assert "llm_metrics" in data, "报告应该包含LLM指标"
        assert "db_metrics" in data, "报告应该包含数据库指标"
        assert "resource_metrics" in data, "报告应该包含资源指标"
        
        logger.info("✅ 性能数据持久化测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 性能数据持久化测试失败: {str(e)}")
        return False


def test_performance_stats_api():
    """测试性能统计API"""
    logger.info("=== 测试性能统计API ===")
    
    try:
        # 获取性能统计
        stats = get_performance_stats()
        logger.info(f"性能统计: {stats}")
        
        assert "api_metrics" in stats, "统计应该包含API指标"
        assert "summary" in stats, "统计应该包含摘要信息"
        
        summary = stats["summary"]
        assert "total_api_calls" in summary, "摘要应该包含总API调用数"
        assert "avg_response_time" in summary, "摘要应该包含平均响应时间"
        
        logger.info("✅ 性能统计API测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 性能统计API测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    logger.info("开始性能监控功能测试")
    
    test_results = []
    
    # 运行所有测试
    tests = [
        test_performance_monitor_initialization,
        test_api_performance_tracking,
        test_llm_performance_tracking,
        test_db_performance_tracking,
        test_resource_monitoring,
        test_performance_data_persistence,
        test_performance_stats_api
    ]
    
    for test_func in tests:
        try:
            result = test_func()
            test_results.append(result)
        except Exception as e:
            logger.error(f"测试 {test_func.__name__} 执行异常: {str(e)}")
            test_results.append(False)
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    logger.info(f"\n=== 测试结果汇总 ===")
    logger.info(f"总测试数: {total}")
    logger.info(f"通过数: {passed}")
    logger.info(f"失败数: {total - passed}")
    logger.info(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        logger.info("🎉 所有测试通过！性能监控功能正常工作")
    else:
        logger.warning("⚠️  部分测试失败，请检查性能监控配置")
    
    # 清理资源
    try:
        shutdown_performance_features()
        logger.info("性能监控资源已清理")
    except Exception as e:
        logger.error(f"清理资源时出错: {str(e)}")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
