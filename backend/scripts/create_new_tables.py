#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建新表脚本
用于创建新的数据库表，不修改已有的domains、categories和concern_points表
"""

import os
import sys
import sqlite3
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('create_new_tables')

# 数据库文件路径
DB_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'aidatabase.db')
# SQL脚本路径
SQL_FILE = os.path.join(os.path.dirname(__file__), 'create_new_tables.sql')

def main():
    """主函数"""
    if not os.path.exists(DB_FILE):
        logger.error(f"数据库文件不存在: {DB_FILE}")
        sys.exit(1)
    
    if not os.path.exists(SQL_FILE):
        logger.error(f"SQL脚本文件不存在: {SQL_FILE}")
        sys.exit(1)
    
    try:
        # 读取SQL脚本
        with open(SQL_FILE, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        # 连接数据库
        conn = sqlite3.connect(DB_FILE)
        conn.execute("PRAGMA foreign_keys = ON")
        
        # 执行SQL脚本
        conn.executescript(sql_script)
        conn.commit()
        
        logger.info("新表创建成功！")
        
    except Exception as e:
        logger.error(f"创建新表时发生错误: {e}")
        sys.exit(1)
    finally:
        # 关闭连接
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main()