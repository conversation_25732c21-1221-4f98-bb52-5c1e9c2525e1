#!/usr/bin/env python3
"""
错误日志分析工具

此工具专门用于分析error.log文件，提供以下功能：
1. 错误统计和分类
2. 异常类型分析
3. 错误趋势分析
4. 问题定位和排查建议
5. 错误报告生成
"""

import json
import sys
import argparse
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import List, Dict, Any
import re

class ErrorLogAnalyzer:
    """错误日志分析器"""
    
    def __init__(self, log_file: str):
        self.log_file = Path(log_file)
        self.error_logs = []
        self.load_error_logs()
    
    def load_error_logs(self):
        """加载错误日志文件"""
        if not self.log_file.exists():
            print(f"❌ 错误日志文件不存在: {self.log_file}")
            return
        
        print(f"📖 加载错误日志文件: {self.log_file}")
        
        with open(self.log_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    log_entry = json.loads(line)
                    # 只处理ERROR和CRITICAL级别的日志
                    if log_entry.get('level') in ['ERROR', 'CRITICAL']:
                        log_entry['_line_number'] = line_num
                        self.error_logs.append(log_entry)
                except json.JSONDecodeError as e:
                    print(f"⚠️  第 {line_num} 行JSON解析失败: {e}")
        
        print(f"✅ 成功加载 {len(self.error_logs)} 条错误日志记录")
    
    def analyze_error_overview(self):
        """分析错误概览"""
        print("\n" + "="*60)
        print("🚨 错误日志概览分析")
        print("="*60)
        
        if not self.error_logs:
            print("✅ 没有发现错误日志")
            return
        
        # 时间范围
        timestamps = [log.get('timestamp') for log in self.error_logs if log.get('timestamp')]
        if timestamps:
            start_time = min(timestamps)
            end_time = max(timestamps)
            print(f"⏰ 错误时间范围: {start_time} ~ {end_time}")
        
        # 错误级别统计
        levels = Counter(log.get('level', 'UNKNOWN') for log in self.error_logs)
        print(f"\n📊 错误级别统计:")
        for level, count in levels.most_common():
            severity = "🔴" if level == "CRITICAL" else "🟠"
            print(f"  {severity} {level}: {count:,} 条")
        
        # 错误分类统计
        categories = Counter(log.get('error_category', 'unknown') for log in self.error_logs)
        print(f"\n🏷️  错误分类统计:")
        for category, count in categories.most_common():
            print(f"  📂 {category}: {count:,} 条")
        
        # 组件统计
        components = Counter(log.get('component', log.get('logger', 'unknown')) for log in self.error_logs)
        print(f"\n🔧 组件错误统计 (Top 10):")
        for component, count in components.most_common(10):
            print(f"  🔩 {component}: {count:,} 条")
    
    def analyze_exception_types(self):
        """分析异常类型"""
        print("\n" + "="*60)
        print("🐛 异常类型分析")
        print("="*60)
        
        exception_types = Counter()
        exception_messages = defaultdict(list)
        
        for log in self.error_logs:
            exception = log.get('exception', {})
            if exception and exception.get('type'):
                exc_type = exception['type']
                exc_message = exception.get('message', '')
                exception_types[exc_type] += 1
                exception_messages[exc_type].append(exc_message)
        
        if not exception_types:
            print("❌ 没有发现异常信息")
            return
        
        print(f"📈 异常类型统计:")
        for exc_type, count in exception_types.most_common():
            print(f"  🚫 {exc_type}: {count} 次")
            
            # 显示该异常类型的常见消息
            messages = exception_messages[exc_type]
            unique_messages = list(set(messages))[:3]  # 显示前3个不同的消息
            for msg in unique_messages:
                if msg:
                    preview = msg[:80] + "..." if len(msg) > 80 else msg
                    print(f"     💬 {preview}")
    
    def analyze_error_patterns(self):
        """分析错误模式"""
        print("\n" + "="*60)
        print("🔍 错误模式分析")
        print("="*60)
        
        # 按操作分析错误
        operations = Counter()
        for log in self.error_logs:
            operation = log.get('operation', 'unknown')
            operations[operation] += 1
        
        print(f"⚙️  操作错误统计:")
        for operation, count in operations.most_common(10):
            print(f"  🔄 {operation}: {count} 次")
        
        # 按外部服务分析错误
        external_services = Counter()
        for log in self.error_logs:
            if log.get('error_category') == 'external_service':
                service = log.get('external_service', 'unknown')
                external_services[service] += 1
        
        if external_services:
            print(f"\n🌐 外部服务错误统计:")
            for service, count in external_services.most_common():
                print(f"  🔗 {service}: {count} 次")
        
        # 按数据库表分析错误
        db_tables = Counter()
        for log in self.error_logs:
            if log.get('error_category') == 'database':
                table = log.get('database_table', 'unknown')
                db_tables[table] += 1
        
        if db_tables:
            print(f"\n🗄️  数据库错误统计:")
            for table, count in db_tables.most_common():
                print(f"  📊 {table}: {count} 次")
    
    def analyze_error_trends(self):
        """分析错误趋势"""
        print("\n" + "="*60)
        print("📈 错误趋势分析")
        print("="*60)
        
        # 按小时统计错误
        hourly_errors = defaultdict(int)
        for log in self.error_logs:
            timestamp = log.get('timestamp')
            if timestamp:
                try:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    hour = dt.hour
                    hourly_errors[hour] += 1
                except:
                    continue
        
        if hourly_errors:
            print(f"🕐 24小时错误分布:")
            for hour in range(24):
                count = hourly_errors.get(hour, 0)
                bar = "█" * (count // 5) if count > 0 else ""
                print(f"  {hour:02d}:00 {count:3d} {bar}")
        
        # 查找错误高峰期
        if hourly_errors:
            peak_hour = max(hourly_errors.items(), key=lambda x: x[1])
            print(f"\n⚡ 错误高峰期: {peak_hour[0]:02d}:00 ({peak_hour[1]} 次错误)")
    
    def analyze_critical_errors(self):
        """分析严重错误"""
        print("\n" + "="*60)
        print("🔥 严重错误分析")
        print("="*60)
        
        critical_errors = [log for log in self.error_logs if log.get('level') == 'CRITICAL']
        
        if not critical_errors:
            print("✅ 没有发现严重错误")
            return
        
        print(f"🚨 发现 {len(critical_errors)} 条严重错误:")
        
        for i, log in enumerate(critical_errors[-5:], 1):  # 显示最近5条
            timestamp = log.get('timestamp', 'N/A')
            component = log.get('component', 'N/A')
            message = log.get('message', 'N/A')
            impact = log.get('impact', 'N/A')
            
            print(f"\n  {i}. [{timestamp}] {component}")
            print(f"     💥 {message}")
            if impact != 'N/A':
                print(f"     📊 影响: {impact}")
            
            recovery_action = log.get('recovery_action')
            if recovery_action:
                print(f"     🔧 恢复措施: {recovery_action}")
    
    def generate_error_report(self, output_file: str = None):
        """生成错误报告"""
        print("\n" + "="*60)
        print("📋 生成错误报告")
        print("="*60)
        
        if output_file is None:
            output_file = f"error_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 统计数据
        report = {
            "report_time": datetime.now().isoformat(),
            "log_file": str(self.log_file),
            "total_errors": len(self.error_logs),
            "error_levels": dict(Counter(log.get('level', 'UNKNOWN') for log in self.error_logs)),
            "error_categories": dict(Counter(log.get('error_category', 'unknown') for log in self.error_logs)),
            "top_components": dict(Counter(log.get('component', log.get('logger', 'unknown')) for log in self.error_logs).most_common(10)),
            "exception_types": dict(Counter(log.get('exception', {}).get('type', 'unknown') for log in self.error_logs if log.get('exception', {}).get('type'))),
            "critical_errors_count": len([log for log in self.error_logs if log.get('level') == 'CRITICAL']),
            "time_range": {
                "start": min((log.get('timestamp') for log in self.error_logs if log.get('timestamp')), default=None),
                "end": max((log.get('timestamp') for log in self.error_logs if log.get('timestamp')), default=None)
            }
        }
        
        # 添加建议
        suggestions = []
        
        # 基于错误数量的建议
        if report["total_errors"] > 100:
            suggestions.append("错误数量较多，建议重点关注高频错误的根本原因")
        
        # 基于严重错误的建议
        if report["critical_errors_count"] > 0:
            suggestions.append(f"发现 {report['critical_errors_count']} 条严重错误，需要立即处理")
        
        # 基于错误分类的建议
        if "external_service" in report["error_categories"]:
            suggestions.append("外部服务错误较多，建议检查网络连接和服务可用性")
        
        if "database" in report["error_categories"]:
            suggestions.append("数据库错误较多，建议检查数据库连接和SQL语句")
        
        report["suggestions"] = suggestions
        
        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 错误报告已保存到: {output_file}")
        
        # 显示摘要
        print(f"\n📊 报告摘要:")
        print(f"  总错误数: {report['total_errors']}")
        print(f"  严重错误: {report['critical_errors_count']}")
        print(f"  主要错误类型: {list(report['error_categories'].keys())[:3]}")
        
        if suggestions:
            print(f"\n💡 建议:")
            for suggestion in suggestions:
                print(f"  • {suggestion}")
    
    def search_errors(self, keyword: str, limit: int = 10):
        """搜索错误日志"""
        print(f"\n🔍 搜索错误关键词: '{keyword}'")
        print("="*60)
        
        results = []
        for log in self.error_logs:
            message = log.get('message', '')
            if keyword.lower() in message.lower():
                results.append(log)
        
        if not results:
            print("❌ 没有找到匹配的错误")
            return
        
        print(f"✅ 找到 {len(results)} 条匹配的错误 (显示前 {limit} 条):")
        
        for i, log in enumerate(results[:limit], 1):
            timestamp = log.get('timestamp', 'N/A')
            level = log.get('level', 'N/A')
            component = log.get('component', log.get('logger', 'N/A'))
            message = log.get('message', 'N/A')
            
            print(f"\n  {i}. [{timestamp}] {level} - {component}")
            print(f"     {message}")
            
            if log.get('session_id'):
                print(f"     会话ID: {log['session_id']}")
            if log.get('exception', {}).get('type'):
                print(f"     异常类型: {log['exception']['type']}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="错误日志分析工具")
    parser.add_argument("log_file", nargs='?', default="logs/error.log", help="错误日志文件路径")
    parser.add_argument("--search", "-s", help="搜索关键词")
    parser.add_argument("--limit", type=int, default=10, help="搜索结果限制")
    parser.add_argument("--report", "-r", help="生成报告文件名")
    parser.add_argument("--overview", action="store_true", help="显示概览分析")
    parser.add_argument("--exceptions", action="store_true", help="显示异常分析")
    parser.add_argument("--patterns", action="store_true", help="显示错误模式")
    parser.add_argument("--trends", action="store_true", help="显示错误趋势")
    parser.add_argument("--critical", action="store_true", help="显示严重错误")
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = ErrorLogAnalyzer(args.log_file)
    
    if not analyzer.error_logs:
        print("❌ 没有可分析的错误日志数据")
        return
    
    # 执行分析
    if args.search:
        analyzer.search_errors(args.search, args.limit)
    elif args.overview or not any([args.exceptions, args.patterns, args.trends, args.critical]):
        analyzer.analyze_error_overview()
    
    if args.exceptions:
        analyzer.analyze_exception_types()
    
    if args.patterns:
        analyzer.analyze_error_patterns()
    
    if args.trends:
        analyzer.analyze_error_trends()
    
    if args.critical:
        analyzer.analyze_critical_errors()
    
    if args.report:
        analyzer.generate_error_report(args.report)

if __name__ == "__main__":
    main()
