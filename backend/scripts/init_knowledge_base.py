"""
知识库数据库初始化脚本

此脚本用于创建和填充知识库数据库，包括：
1. 创建数据库表结构
2. 插入示例数据
"""

import os
import json
import sqlite3
from pathlib import Path

# 数据库路径
DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'knowledge_base.db')

def init_database():
    """初始化数据库"""
    print(f"初始化数据库：{DB_PATH}")
    
    # 确保目录存在
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    
    # 创建数据库连接
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # 创建表结构
    create_tables(cursor)
    
    # 插入示例数据
    insert_sample_data(cursor)
    
    # 提交事务
    conn.commit()
    
    # 关闭连接
    conn.close()
    
    print("数据库初始化完成")

def create_tables(cursor):
    """创建表结构"""
    print("创建表结构...")
    
    # 创建领域表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS domains (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # 创建类别表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS categories (
        id TEXT PRIMARY KEY,
        domain_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE,
        UNIQUE(domain_id, name)
    )
    ''')
    
    # 创建关注点表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS focus_points (
        id TEXT PRIMARY KEY,
        category_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        required INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
        UNIQUE(category_id, name)
    )
    ''')
    
    # 创建关注点定义表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS focus_point_definitions (
        id TEXT PRIMARY KEY,
        domain TEXT NOT NULL,
        category TEXT NOT NULL,
        description TEXT NOT NULL,
        priority TEXT NOT NULL,
        type TEXT DEFAULT 'text',
        question TEXT,
        validation TEXT,
        required INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(domain, category, id)
    )
    ''')

def insert_sample_data(cursor):
    """插入示例数据"""
    print("插入示例数据...")
    
    # 插入领域数据
    domains = [
        ("domain1", "平面设计", "包括海报、宣传册、LOGO等平面设计需求"),
        ("domain2", "网站设计", "包括网站UI、交互设计等需求"),
        ("domain3", "软件开发", "包括各类软件、APP、小程序等开发需求"),
        ("domain4", "产品设计", "包括产品原型、交互设计等需求")
    ]
    
    for domain in domains:
        cursor.execute(
            "INSERT OR REPLACE INTO domains (id, name, description) VALUES (?, ?, ?)",
            domain
        )
    
    # 插入类别数据
    categories = [
        ("category1", "domain1", "海报设计", "各类海报的设计需求"),
        ("category2", "domain1", "LOGO设计", "企业、品牌LOGO设计需求"),
        ("category3", "domain2", "企业官网", "企业官方网站设计需求"),
        ("category4", "domain2", "电商网站", "电子商务网站设计需求"),
        ("category5", "domain3", "移动应用", "手机APP开发需求"),
        ("category6", "domain3", "Web应用", "基于浏览器的Web应用开发需求"),
        ("category7", "domain4", "硬件产品", "实体硬件产品设计需求"),
        ("category8", "domain4", "软件产品", "软件产品设计需求")
    ]
    
    for category in categories:
        cursor.execute(
            "INSERT OR REPLACE INTO categories (id, domain_id, name, description) VALUES (?, ?, ?, ?)",
            category
        )
    
    # 插入关注点数据
    focus_points = [
        ("fp1", "category1", "海报主题", "海报的主题或目的", 1),
        ("fp2", "category1", "目标受众", "海报的目标受众群体", 1),
        ("fp3", "category1", "尺寸要求", "海报的尺寸规格要求", 1),
        ("fp4", "category1", "风格偏好", "海报的设计风格偏好", 0),
        ("fp5", "category1", "颜色要求", "海报的颜色方案要求", 0),
        ("fp6", "category2", "品牌名称", "需要设计LOGO的品牌名称", 1),
        ("fp7", "category2", "行业领域", "品牌所属的行业或领域", 1),
        ("fp8", "category2", "目标受众", "品牌的目标受众群体", 1),
        ("fp9", "category2", "品牌价值观", "品牌的核心价值观和理念", 0),
        ("fp10", "category2", "竞品分析", "主要竞争对手的LOGO分析", 0)
    ]
    
    for focus_point in focus_points:
        cursor.execute(
            "INSERT OR REPLACE INTO focus_points (id, category_id, name, description, required) VALUES (?, ?, ?, ?, ?)",
            focus_point
        )
    
    # 插入关注点定义数据
    focus_point_definitions = [
        ("fpd1", "平面设计", "海报设计", "海报主题", "high", "text", "请描述您需要设计的海报主题或目的是什么？", 
         json.dumps({"type": "text", "min_length": 5}), 1),
        ("fpd2", "平面设计", "海报设计", "目标受众", "high", "text", "这个海报的目标受众群体是哪些人？", 
         json.dumps({"type": "text", "min_length": 3}), 1),
        ("fpd3", "平面设计", "海报设计", "尺寸要求", "high", "text", "海报的尺寸规格有什么要求？", 
         json.dumps({"type": "text", "min_length": 2}), 1),
        ("fpd4", "平面设计", "海报设计", "风格偏好", "medium", "text", "您对海报的设计风格有什么偏好？", 
         json.dumps({"type": "text"}), 0),
        ("fpd5", "平面设计", "海报设计", "颜色要求", "medium", "text", "对海报的颜色方案有什么要求或偏好？", 
         json.dumps({"type": "text"}), 0),
        ("fpd6", "平面设计", "LOGO设计", "品牌名称", "high", "text", "需要设计LOGO的品牌名称是什么？", 
         json.dumps({"type": "text", "min_length": 2}), 1),
        ("fpd7", "平面设计", "LOGO设计", "行业领域", "high", "text", "品牌所属的行业或领域是什么？", 
         json.dumps({"type": "text", "min_length": 2}), 1),
        ("fpd8", "平面设计", "LOGO设计", "目标受众", "high", "text", "品牌的目标受众群体是哪些人？", 
         json.dumps({"type": "text", "min_length": 3}), 1),
        ("fpd9", "平面设计", "LOGO设计", "品牌价值观", "medium", "text", "品牌的核心价值观和理念是什么？", 
         json.dumps({"type": "text"}), 0),
        ("fpd10", "平面设计", "LOGO设计", "竞品分析", "low", "text", "有哪些主要竞争对手？他们的LOGO风格是怎样的？", 
         json.dumps({"type": "text"}), 0)
    ]
    
    for definition in focus_point_definitions:
        cursor.execute(
            """INSERT OR REPLACE INTO focus_point_definitions 
            (id, domain, category, description, priority, type, question, validation, required) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
            definition
        )

if __name__ == "__main__":
    init_database()
