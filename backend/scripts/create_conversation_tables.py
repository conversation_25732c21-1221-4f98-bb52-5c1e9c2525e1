import os
import sqlite3
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 数据库文件路径
DB_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'aidatabase.db')

def create_tables():
    """创建会话相关表"""
    try:
        with sqlite3.connect(DB_FILE) as conn:
            conn.execute('PRAGMA foreign_keys = ON')
            cursor = conn.cursor()
            
            # 读取SQL文件
            sql_file = os.path.join(os.path.dirname(__file__), 'create_tables.sql')
            with open(sql_file, 'r', encoding='utf-8') as f:
                sql_script = f.read()
            
            # 执行SQL脚本
            cursor.executescript(sql_script)
            conn.commit()
            
            logger.info("成功创建会话相关表")
    except sqlite3.Error as e:
        logger.error(f"创建表失败: {e}")
        raise
    except Exception as e:
        logger.error(f"创建表失败: {e}")
        raise

def verify_tables():
    """验证表是否创建成功"""
    try:
        with sqlite3.connect(DB_FILE) as conn:
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]
            
            # 检查是否包含所有需要的表
            required_tables = ['conversations', 'messages', 'concern_point_coverage', 
                              'requirements', 'documents']
            
            for table in required_tables:
                if table not in table_names:
                    logger.error(f"表 '{table}' 未创建成功")
                    return False
            
            logger.info(f"验证成功，已创建表: {', '.join(required_tables)}")
            return True
    except sqlite3.Error as e:
        logger.error(f"验证表失败: {e}")
        return False

def main():
    """主函数：创建会话相关表"""
    try:
        # 创建表
        create_tables()
        
        # 验证表
        verify_tables()
        
        logger.info("会话相关表创建完成")
    except Exception as e:
        logger.error(f"创建表流程失败: {e}")
        raise

if __name__ == "__main__":
    main()