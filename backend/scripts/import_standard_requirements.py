import sqlite3
import re
import logging
import sys
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库文件
DB_FILE = 'backend/data/aidatabase.db'

# 数据库 schema
SCHEMA = """
-- domains 表：存储领域信息
CREATE TABLE IF NOT EXISTS domains (
    domain_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now'))
);

-- categories 表：存储领域下的类别
CREATE TABLE IF NOT EXISTS categories (
    category_id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (domain_id) REFERENCES domains(domain_id) ON DELETE CASCADE,
    UNIQUE (domain_id, name)
);

-- concern_points 表：存储类别的关注点
CREATE TABLE IF NOT EXISTS concern_points (
    concern_point_id INTEGER PRIMARY KEY AUTOINCREMENT,
    category_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    priority TEXT NOT NULL CHECK (priority IN ('P0', 'P1', 'P2')),
    example TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE CASCADE
);
"""

def init_database():
    """初始化数据库，创建表结构"""
    try:
        with sqlite3.connect(DB_FILE) as conn:
            conn.execute('PRAGMA foreign_keys = ON')  # 启用外键
            conn.executescript(SCHEMA)
            conn.commit()
        logger.info("数据库初始化成功")
    except sqlite3.Error as e:
        logger.error(f"数据库初始化失败: {e}")
        raise

def parse_markdown(file_path):
    """解析 Markdown 文件，提取领域、类别和关注点"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 提取领域（一级标题）
        domain_pattern = r'^\s*(.*?)\s*$'
        domains = []
        current_domain = None
        current_category = None
        data = {'domains': {}, 'categories': {}, 'concern_points': []}

        # 按行解析，使用状态机跟踪
        lines = content.splitlines()
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 领域（一级标题，去除括号内容）
            if line and not line.startswith('- ') and current_domain is None:
                current_domain = re.sub(r'\(.*?\)', '', line).strip()
                data['domains'][current_domain] = f"{current_domain}相关服务"
                current_category = None
                logger.debug(f"解析到领域: {current_domain}")
                
            # 类别（紧接在领域后的非关注点行）
            elif line and not line.startswith('- ') and current_domain and current_category is None:
                current_category = line
                data['categories'][current_category] = {
                    'domain': current_domain,
                    'description': f"{current_category}相关设计"
                }
                
            # 关注点（以 '- P[0-2]-' 开头）
            elif line.startswith('- P'):
                if current_category:
                    match = re.match(r'- (P[0-2])-(.+?):?\s*(.*)', line)
                    if match:
                        priority, name, description = match.groups()
                        data['concern_points'].append({
                            'category': current_category,
                            'name': name.strip(),
                            'description': description.strip(),
                            'priority': priority,
                            'example': None
                        })
            # 新领域（遇到非关注点行且已有当前领域）
            elif line and not line.startswith('- ') and current_domain:
                current_domain = line
                data['domains'][current_domain] = f"{current_domain}相关服务"
                current_category = None

        logger.info(f"解析完成：{len(data['domains'])} 个领域，{len(data['categories'])} 个类别，{len(data['concern_points'])} 个关注点")
        return data
    except Exception as e:
        logger.error(f"解析 Markdown 失败: {e}")
        raise

def import_data(data):
    """将解析的数据导入数据库"""
    try:
        with sqlite3.connect(DB_FILE) as conn:
            conn.execute('PRAGMA foreign_keys = ON')
            cursor = conn.cursor()

            # 插入领域
            for domain_name, description in data['domains'].items():
                cursor.execute("""
                    INSERT OR IGNORE INTO domains (name, description)
                    VALUES (?, ?)
                """, (domain_name, description))
            conn.commit()
            logger.info(f"插入 {cursor.rowcount} 个领域")

            # 插入类别并建立名称到ID的映射
            category_ids = {}
            for category_name, info in data['categories'].items():
                try:
                    # 获取domain_id（模糊匹配）
                    domain_name = info['domain']
                    # 准备多种匹配模式
                    patterns = [
                        f"%{domain_name}%",
                        f"%{domain_name.split('(')[0].strip()}%"
                    ]
                    # 添加纯中文匹配
                    chinese_only = ''.join([c for c in domain_name if '\u4e00' <= c <= '\u9fa5'])
                    if chinese_only:
                        patterns.append(f"%{chinese_only}%")
                    
                    # 构建动态SQL
                    sql = """
                        SELECT domain_id FROM domains 
                        WHERE """ + " OR ".join(["trim(name) LIKE ?"] * len(patterns))
                    cursor.execute(sql, patterns)
                    result = cursor.fetchone()
                    if not result:
                        raise ValueError(f"找不到匹配的领域: '{domain_name}'")
                    domain_id = result[0]
                    logger.debug(f"匹配领域: '{domain_name}' -> ID: {domain_id}")
                    
                    # 插入或获取类别
                    cursor.execute("""
                        INSERT OR IGNORE INTO categories (domain_id, name, description)
                        VALUES (?, ?, ?)
                    """, (domain_id, category_name, info['description']))
                    
                    if cursor.rowcount > 0:  # 新插入的类别
                        category_id = cursor.lastrowid
                    else:  # 已存在的类别
                        cursor.execute("""
                        SELECT category_id FROM categories 
                        WHERE domain_id = ? AND name = ?
                    """, (domain_id, category_name))
                    result = cursor.fetchone()
                    if not result:
                        raise ValueError(f"找不到类别: domain_id={domain_id}, name='{category_name}'")
                    category_id = result[0]
                    logger.debug(f"获取类别ID: {category_id} (domain_id={domain_id}, name='{category_name}')")
                    
                    category_ids[category_name] = category_id
                    logger.debug(f"类别 '{category_name}' ID: {category_id}")
                except Exception as e:
                    logger.error(f"处理类别 '{category_name}' 失败: {e}")
                    raise
            
            conn.commit()
            logger.info(f"成功处理 {len(category_ids)}/{len(data['categories'])} 个类别")

            # 插入关注点
            inserted_count = 0
            for cp in data['concern_points']:
                try:
                    cursor.execute("""
                        INSERT INTO concern_points (category_id, name, description, priority, example)
                        VALUES (?, ?, ?, ?, ?)
                    """, (category_ids[cp['category']], cp['name'], cp['description'], cp['priority'], cp['example']))
                    inserted_count += 1
                except KeyError:
                    logger.warning(f"找不到类别: {cp['category']}，跳过关注点: {cp['name']}")
            conn.commit()
            logger.info(f"成功插入 {inserted_count}/{len(data['concern_points'])} 个关注点")

    except sqlite3.Error as e:
        logger.error(f"数据导入失败: {e}")
        raise

def verify_data():
    """验证导入的数据完整性"""
    try:
        with sqlite3.connect(DB_FILE) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM domains")
            domain_count = cursor.fetchone()[0]
            cursor.execute("SELECT COUNT(*) FROM categories")
            category_count = cursor.fetchone()[0]
            cursor.execute("SELECT COUNT(*) FROM concern_points")
            concern_point_count = cursor.fetchone()[0]
            logger.info(f"验证结果：{domain_count} 个领域，{category_count} 个类别，{concern_point_count} 个关注点")
            return domain_count, category_count, concern_point_count
    except sqlite3.Error as e:
        logger.error(f"数据验证失败: {e}")
        raise

def main():
    """主函数：执行导入流程"""
    try:
        # 初始化数据库
        init_database()

        # 解析 Markdown (支持命令行参数指定文件)
        markdown_file = sys.argv[1] if len(sys.argv) > 1 else 'docs/development/需求标准库.md'
        logger.info(f"开始解析文件: {markdown_file}")
        data = parse_markdown(markdown_file)
        
        # 打印解析结果用于调试
        logger.debug("解析结果:")
        logger.debug(f"领域: {list(data['domains'].keys())}")
        logger.debug(f"类别: {list(data['categories'].keys())}")
        logger.debug(f"关注点数量: {len(data['concern_points'])}")

        # 导入数据
        logger.info("开始导入数据...")
        import_data(data)

        # 验证数据
        logger.info("验证数据完整性...")
        domain_count, category_count, concern_point_count = verify_data()
        
        if category_count == 0 or concern_point_count == 0:
            logger.error(f"导入失败: 类别({category_count})或关注点({concern_point_count})数量为0")
            # 打印数据库内容用于调试
            with sqlite3.connect(DB_FILE) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM domains")
                logger.debug("数据库中的领域: " + str(cursor.fetchall()))
                cursor.execute("SELECT name FROM categories")
                logger.debug("数据库中的类别: " + str(cursor.fetchall()))
        else:
            logger.info(f"导入成功：{domain_count} 个领域，{category_count} 个类别，{concern_point_count} 个关注点")
    except Exception as e:
        logger.error(f"导入流程失败: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    main()
