#!/usr/bin/env python
"""
领域分类性能测试脚本
用于测试DomainClassifierAgent在高负载情况下的性能表现
"""
import os
import sys
import json
import time
import asyncio
import logging
import statistics
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))

from agents.domain_classifier import DomainClassifierAgent
from agents.llm_service import LLMServiceAgent
from agents.knowledge_base import KnowledgeBaseAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('logs', f'domain_classifier_perf_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'))
    ]
)
logger = logging.getLogger(__name__)

# 测试配置
TEST_CONFIG = {
    "concurrent_requests": [1, 5, 10],  # 并发请求数
    "iterations": 5,  # 每个并发级别的迭代次数
    "test_inputs": [
        "我想开发一个手机应用",
        "需要设计一个公司logo",
        "你好",
        "我想做点东西",
        "我需要制作一个宣传视频"
    ]
}

class PerformanceMetrics:
    """性能指标收集类"""
    
    def __init__(self):
        self.response_times = []
        self.success_count = 0
        self.error_count = 0
        self.start_time = None
        self.end_time = None
        
    def start(self):
        """开始计时"""
        self.start_time = time.time()
        
    def stop(self):
        """停止计时"""
        self.end_time = time.time()
        
    def add_response_time(self, response_time):
        """添加响应时间"""
        self.response_times.append(response_time)
        
    def increment_success(self):
        """增加成功计数"""
        self.success_count += 1
        
    def increment_error(self):
        """增加错误计数"""
        self.error_count += 1
        
    def get_summary(self):
        """获取性能指标摘要"""
        if not self.response_times:
            return {
                "total_requests": 0,
                "success_rate": 0,
                "avg_response_time": 0,
                "min_response_time": 0,
                "max_response_time": 0,
                "median_response_time": 0,
                "total_duration": 0
            }
            
        total_requests = self.success_count + self.error_count
        success_rate = (self.success_count / total_requests) * 100 if total_requests > 0 else 0
        
        return {
            "total_requests": total_requests,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": success_rate,
            "avg_response_time": statistics.mean(self.response_times) if self.response_times else 0,
            "min_response_time": min(self.response_times) if self.response_times else 0,
            "max_response_time": max(self.response_times) if self.response_times else 0,
            "median_response_time": statistics.median(self.response_times) if self.response_times else 0,
            "p95_response_time": sorted(self.response_times)[int(len(self.response_times) * 0.95)] if len(self.response_times) >= 20 else None,
            "total_duration": self.end_time - self.start_time if self.end_time and self.start_time else 0
        }

async def classify_domain(domain_classifier, input_text, metrics):
    """执行领域分类并记录性能指标"""
    start_time = time.time()
    success = False
    
    try:
        result = await domain_classifier.classify(input_text)
        end_time = time.time()
        response_time = end_time - start_time
        
        metrics.add_response_time(response_time)
        metrics.increment_success()
        success = True
        
        return {
            "input": input_text,
            "result": result,
            "response_time": response_time,
            "success": True
        }
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        
        metrics.add_response_time(response_time)
        metrics.increment_error()
        
        return {
            "input": input_text,
            "error": str(e),
            "response_time": response_time,
            "success": False
        }

async def run_concurrent_test(domain_classifier, concurrency, test_inputs, iterations):
    """运行并发测试"""
    logger.info(f"开始并发测试: 并发数={concurrency}, 迭代次数={iterations}")
    
    metrics = PerformanceMetrics()
    metrics.start()
    
    for i in range(iterations):
        logger.info(f"迭代 {i+1}/{iterations}")
        
        # 创建任务列表
        tasks = []
        for j in range(concurrency):
            # 循环使用测试输入
            input_index = (j % len(test_inputs))
            input_text = test_inputs[input_index]
            
            task = classify_domain(domain_classifier, input_text, metrics)
            tasks.append(task)
        
        # 并发执行任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"任务执行异常: {str(result)}")
                metrics.increment_error()
            elif not result.get("success", False):
                logger.warning(f"分类失败: {result.get('error', 'Unknown error')}")
    
    metrics.stop()
    return metrics

async def main():
    """主函数"""
    logger.info("开始领域分类性能测试")
    
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    
    try:
        # 初始化LLM服务
        llm_service = LLMServiceAgent()
        
        # 初始化知识库
        knowledge_base = KnowledgeBaseAgent()
        
        # 初始化领域分类器
        domain_classifier = DomainClassifierAgent(llm_client=llm_service)
        domain_classifier.knowledge_base = knowledge_base
        
        # 预热 - 执行一次分类以加载所有资源
        logger.info("系统预热中...")
        await domain_classifier.classify("预热测试")
        
        # 运行不同并发级别的测试
        results = {}
        for concurrency in TEST_CONFIG["concurrent_requests"]:
            metrics = await run_concurrent_test(
                domain_classifier,
                concurrency,
                TEST_CONFIG["test_inputs"],
                TEST_CONFIG["iterations"]
            )
            
            summary = metrics.get_summary()
            results[f"concurrency_{concurrency}"] = summary
            
            logger.info(f"并发数 {concurrency} 测试结果:")
            logger.info(f"  总请求数: {summary['total_requests']}")
            logger.info(f"  成功率: {summary['success_rate']:.2f}%")
            logger.info(f"  平均响应时间: {summary['avg_response_time']:.2f}秒")
            logger.info(f"  最小响应时间: {summary['min_response_time']:.2f}秒")
            logger.info(f"  最大响应时间: {summary['max_response_time']:.2f}秒")
            logger.info(f"  中位数响应时间: {summary['median_response_time']:.2f}秒")
            logger.info(f"  总持续时间: {summary['total_duration']:.2f}秒")
        
        # 保存结果
        results_file = os.path.join('logs', f'perf_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"性能测试结果已保存到: {results_file}")
        logger.info("性能测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {str(e)}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
