#!/usr/bin/env python3
"""
日志分析工具

此工具用于分析JSON格式的结构化日志，提供以下功能：
1. 日志统计分析
2. 错误日志汇总
3. 性能指标分析
4. 业务流程追踪
5. 日志搜索和过滤
"""

import json
import sys
import argparse
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import List, Dict, Any
import re

class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self, log_file: str):
        self.log_file = Path(log_file)
        self.logs = []
        self.load_logs()
    
    def load_logs(self):
        """加载日志文件"""
        if not self.log_file.exists():
            print(f"❌ 日志文件不存在: {self.log_file}")
            return
        
        print(f"📖 加载日志文件: {self.log_file}")
        
        with open(self.log_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    log_entry = json.loads(line)
                    log_entry['_line_number'] = line_num
                    self.logs.append(log_entry)
                except json.JSONDecodeError as e:
                    print(f"⚠️  第 {line_num} 行JSON解析失败: {e}")
        
        print(f"✅ 成功加载 {len(self.logs)} 条日志记录")
    
    def analyze_overview(self):
        """分析日志概览"""
        print("\n" + "="*50)
        print("📊 日志概览分析")
        print("="*50)
        
        if not self.logs:
            print("❌ 没有日志数据")
            return
        
        # 时间范围
        timestamps = [log.get('timestamp') for log in self.logs if log.get('timestamp')]
        if timestamps:
            start_time = min(timestamps)
            end_time = max(timestamps)
            print(f"⏰ 时间范围: {start_time} ~ {end_time}")
        
        # 日志级别统计
        levels = Counter(log.get('level', 'UNKNOWN') for log in self.logs)
        print(f"\n📈 日志级别统计:")
        for level, count in levels.most_common():
            print(f"  {level}: {count:,} 条")
        
        # 模块统计
        modules = Counter(log.get('logger', 'UNKNOWN') for log in self.logs)
        print(f"\n🏗️  模块统计 (Top 10):")
        for module, count in modules.most_common(10):
            print(f"  {module}: {count:,} 条")
        
        # 会话统计
        sessions = Counter(log.get('session_id', 'UNKNOWN') for log in self.logs if log.get('session_id'))
        if sessions:
            print(f"\n👥 会话统计:")
            print(f"  总会话数: {len(sessions)}")
            print(f"  平均每会话日志数: {sum(sessions.values()) / len(sessions):.1f}")
    
    def analyze_errors(self):
        """分析错误日志"""
        print("\n" + "="*50)
        print("🚨 错误日志分析")
        print("="*50)
        
        error_logs = [log for log in self.logs if log.get('level') in ['ERROR', 'CRITICAL']]
        
        if not error_logs:
            print("✅ 没有发现错误日志")
            return
        
        print(f"❌ 发现 {len(error_logs)} 条错误日志")
        
        # 错误类型统计
        error_types = Counter()
        for log in error_logs:
            if log.get('exception', {}).get('type'):
                error_types[log['exception']['type']] += 1
            else:
                error_types['Unknown'] += 1
        
        print(f"\n🔍 错误类型统计:")
        for error_type, count in error_types.most_common():
            print(f"  {error_type}: {count} 次")
        
        # 显示最近的错误
        print(f"\n📋 最近的错误 (最多显示5条):")
        recent_errors = sorted(error_logs, key=lambda x: x.get('timestamp', ''), reverse=True)[:5]
        
        for i, log in enumerate(recent_errors, 1):
            print(f"\n  {i}. [{log.get('timestamp', 'N/A')}] {log.get('logger', 'N/A')}")
            print(f"     消息: {log.get('message', 'N/A')}")
            if log.get('exception', {}).get('message'):
                print(f"     异常: {log['exception']['message']}")
    
    def analyze_performance(self):
        """分析性能指标"""
        print("\n" + "="*50)
        print("⚡ 性能指标分析")
        print("="*50)
        
        # 查找包含duration的日志
        perf_logs = [log for log in self.logs if log.get('duration') is not None]
        
        if not perf_logs:
            print("❌ 没有发现性能数据")
            return
        
        print(f"📊 发现 {len(perf_logs)} 条性能记录")
        
        # 按类型分组分析
        perf_by_type = defaultdict(list)
        for log in perf_logs:
            log_type = log.get('type', 'unknown')
            perf_by_type[log_type].append(log.get('duration', 0))
        
        print(f"\n⏱️  性能统计:")
        for log_type, durations in perf_by_type.items():
            if durations:
                avg_duration = sum(durations) / len(durations)
                max_duration = max(durations)
                min_duration = min(durations)
                print(f"  {log_type}:")
                print(f"    平均耗时: {avg_duration:.3f}s")
                print(f"    最大耗时: {max_duration:.3f}s")
                print(f"    最小耗时: {min_duration:.3f}s")
                print(f"    调用次数: {len(durations)}")
    
    def analyze_business_flow(self):
        """分析业务流程"""
        print("\n" + "="*50)
        print("🔄 业务流程分析")
        print("="*50)
        
        # 查找业务流程日志
        business_logs = [log for log in self.logs if log.get('type') == 'business_flow']
        
        if not business_logs:
            print("❌ 没有发现业务流程日志")
            return
        
        print(f"📈 发现 {len(business_logs)} 条业务流程记录")
        
        # 按阶段统计
        stages = Counter(log.get('stage', 'unknown') for log in business_logs)
        print(f"\n🎯 业务阶段统计:")
        for stage, count in stages.most_common():
            print(f"  {stage}: {count} 次")
        
        # 按会话分析流程
        flows_by_session = defaultdict(list)
        for log in business_logs:
            session_id = log.get('session_id', 'unknown')
            flows_by_session[session_id].append({
                'timestamp': log.get('timestamp'),
                'stage': log.get('stage'),
                'message': log.get('message')
            })
        
        print(f"\n📝 会话流程示例 (显示前3个会话):")
        for i, (session_id, flows) in enumerate(list(flows_by_session.items())[:3], 1):
            print(f"\n  会话 {i}: {session_id}")
            sorted_flows = sorted(flows, key=lambda x: x.get('timestamp', ''))
            for flow in sorted_flows:
                print(f"    [{flow.get('timestamp', 'N/A')}] {flow.get('stage', 'N/A')}: {flow.get('message', 'N/A')}")
    
    def search_logs(self, keyword: str, level: str = None, limit: int = 10):
        """搜索日志"""
        print(f"\n🔍 搜索关键词: '{keyword}'")
        if level:
            print(f"   日志级别: {level}")
        print("="*50)
        
        results = []
        for log in self.logs:
            # 检查级别过滤
            if level and log.get('level') != level.upper():
                continue
            
            # 检查关键词
            message = log.get('message', '')
            if keyword.lower() in message.lower():
                results.append(log)
        
        if not results:
            print("❌ 没有找到匹配的日志")
            return
        
        print(f"✅ 找到 {len(results)} 条匹配的日志 (显示前 {limit} 条):")
        
        for i, log in enumerate(results[:limit], 1):
            print(f"\n  {i}. [{log.get('timestamp', 'N/A')}] {log.get('level', 'N/A')} - {log.get('logger', 'N/A')}")
            print(f"     {log.get('message', 'N/A')}")
            if log.get('session_id'):
                print(f"     会话ID: {log['session_id']}")
    
    def export_summary(self, output_file: str):
        """导出分析摘要"""
        print(f"\n📤 导出分析摘要到: {output_file}")
        
        summary = {
            "analysis_time": datetime.now().isoformat(),
            "log_file": str(self.log_file),
            "total_logs": len(self.logs),
            "level_stats": dict(Counter(log.get('level', 'UNKNOWN') for log in self.logs)),
            "module_stats": dict(Counter(log.get('logger', 'UNKNOWN') for log in self.logs).most_common(20)),
            "error_count": len([log for log in self.logs if log.get('level') in ['ERROR', 'CRITICAL']]),
            "session_count": len(set(log.get('session_id') for log in self.logs if log.get('session_id'))),
            "time_range": {
                "start": min((log.get('timestamp') for log in self.logs if log.get('timestamp')), default=None),
                "end": max((log.get('timestamp') for log in self.logs if log.get('timestamp')), default=None)
            }
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print("✅ 摘要导出完成")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="日志分析工具")
    parser.add_argument("log_file", help="日志文件路径")
    parser.add_argument("--search", "-s", help="搜索关键词")
    parser.add_argument("--level", "-l", help="过滤日志级别")
    parser.add_argument("--limit", type=int, default=10, help="搜索结果限制")
    parser.add_argument("--export", "-e", help="导出摘要到文件")
    parser.add_argument("--overview", action="store_true", help="显示概览分析")
    parser.add_argument("--errors", action="store_true", help="显示错误分析")
    parser.add_argument("--performance", action="store_true", help="显示性能分析")
    parser.add_argument("--business", action="store_true", help="显示业务流程分析")
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = LogAnalyzer(args.log_file)
    
    if not analyzer.logs:
        print("❌ 没有可分析的日志数据")
        return
    
    # 执行分析
    if args.search:
        analyzer.search_logs(args.search, args.level, args.limit)
    elif args.overview or not any([args.errors, args.performance, args.business]):
        analyzer.analyze_overview()
    
    if args.errors:
        analyzer.analyze_errors()
    
    if args.performance:
        analyzer.analyze_performance()
    
    if args.business:
        analyzer.analyze_business_flow()
    
    if args.export:
        analyzer.export_summary(args.export)

if __name__ == "__main__":
    main()
