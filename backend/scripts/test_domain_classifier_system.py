#!/usr/bin/env python
"""
领域分类系统测试脚本
用于在实际环境中测试DomainClassifierAgent的优化效果
"""
import os
import sys
import json
import asyncio
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))

from backend.agents.domain_classifier import DomainClassifierAgent
from backend.agents.conversation_flow import AutoGenConversationFlowAgent, ConversationState
from backend.agents.llm_service import AutoGenLLMServiceAgent
from backend.agents.knowledge_base import KnowledgeBaseAgent
from backend.agents.information_extractor import InformationExtractorAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('logs', f'domain_classifier_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'))
    ]
)
logger = logging.getLogger(__name__)

# 测试用例
TEST_CASES = [
    # 明确的领域输入
    {"input": "我想开发一个手机应用", "description": "明确的软件开发领域输入"},
    {"input": "需要设计一个公司logo", "description": "明确的平面设计领域输入"},
    {"input": "我需要制作一个宣传视频", "description": "明确的音视频制作领域输入"},
    
    # 模糊的输入
    {"input": "你好", "description": "简单问候"},
    {"input": "我想做点东西", "description": "模糊需求表达"},
    {"input": "有人吗", "description": "询问在线状态"},
    
    # 多轮对话测试
    {"input": "你好", "follow_up": "我想做个网站", "description": "从问候到明确需求的多轮对话"},
    {"input": "帮我做个东西", "follow_up": "就是那种可以在手机上用的", "description": "从模糊到半明确的多轮对话"},
    
    # 边缘情况
    {"input": "", "description": "空输入"},
    {"input": "我不知道我想要什么，你有什么建议？", "description": "请求建议"},
    {"input": "这个和那个哪个好？", "description": "比较型问题"},
]

async def test_domain_classifier():
    """测试DomainClassifierAgent"""
    logger.info("=== 开始DomainClassifierAgent单独测试 ===")
    
    # 初始化LLM服务
    llm_service = AutoGenLLMServiceAgent()
    
    # 初始化知识库
    knowledge_base = KnowledgeBaseAgent()
    
    # 初始化领域分类器
    domain_classifier = DomainClassifierAgent(llm_client=llm_service)
    domain_classifier.knowledge_base = knowledge_base
    
    # 测试单轮分类
    for case in TEST_CASES:
        if "follow_up" in case:
            continue  # 跳过多轮对话测试
            
        logger.info(f"测试用例: {case['description']}")
        logger.info(f"用户输入: {case['input']}")
        
        try:
            result = await domain_classifier.classify(case['input'])
            logger.info(f"分类结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            # 验证结果格式
            assert "domain_id" in result, "缺少domain_id字段"
            assert "confidence" in result, "缺少confidence字段"
            assert "reasoning" in result, "缺少reasoning字段"
            assert "status" in result, "缺少status字段"
            
            if result["status"] == "pending":
                assert "follow_up" in result, "status为pending但缺少follow_up字段"
                
            logger.info(f"测试通过: {case['description']}\n")
        except Exception as e:
            logger.error(f"测试失败: {str(e)}")
    
    logger.info("=== DomainClassifierAgent单独测试完成 ===\n")

async def test_conversation_flow():
    """测试ConversationFlowAgent与DomainClassifierAgent的交互"""
    logger.info("=== 开始ConversationFlow集成测试 ===")
    
    # 初始化LLM服务
    llm_service = AutoGenLLMServiceAgent()
    
    # 初始化知识库
    knowledge_base = KnowledgeBaseAgent()
    
    # 初始化领域分类器
    domain_classifier = DomainClassifierAgent(llm_client=llm_service)
    domain_classifier.knowledge_base = knowledge_base
    
    # 初始化信息提取器
    information_extractor = InformationExtractorAgent(
        llm_service=llm_service,  # LLM服务实例
        knowledge_base_agent=knowledge_base,  # 知识库代理实例
        intent_recognition_agent=None  # 意图识别代理可以为None
    )
    
    # 初始化对话流程管理器
    conversation_flow = AutoGenConversationFlowAgent(
        llm_client=llm_service,
        domain_classifier_agent=domain_classifier,
        information_extractor_agent=information_extractor,
        knowledge_base_agent=knowledge_base
    )
    
    # 测试单轮分类
    for case in TEST_CASES:
        if "follow_up" in case:
            logger.info(f"多轮对话测试: {case['description']}")
            logger.info(f"第一轮输入: {case['input']}")
            
            # 初始化新的对话
            session_id = f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            await conversation_flow.initialize_session()
            
            # 第一轮对话
            response1 = await conversation_flow.process_message({
                "text": case['input'],
                "session_id": session_id
            })
            logger.info(f"系统回复: {response1}")
            # 使用get_state()方法获取状态
            state = conversation_flow.get_state()
            logger.info(f"当前状态: {state.get('current_state')}")
            
            # 第二轮对话
            logger.info(f"第二轮输入: {case['follow_up']}")
            response2 = await conversation_flow.process_message({
                "text": case['follow_up'],
                "session_id": session_id
            })
            logger.info(f"系统回复: {response2}")
            # 再次获取更新后的状态
            state = conversation_flow.get_state()
            logger.info(f"当前状态: {state.get('current_state')}")
            logger.info(f"当前领域: {state.get('current_domain')}")
            
            logger.info(f"多轮对话测试完成: {case['description']}\n")
        else:
            logger.info(f"单轮对话测试: {case['description']}")
            logger.info(f"用户输入: {case['input']}")
            
            # 初始化新的对话
            await conversation_flow.initialize_session()
            
            # 处理消息
            response = await conversation_flow.process_message({
                "text": case['input'],
                "session_id": f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            })
            logger.info(f"系统回复: {response}")
            # 获取当前状态
            state = conversation_flow.get_state()
            logger.info(f"当前状态: {state.get('current_state')}")
            logger.info(f"当前领域: {state.get('current_domain')}")
            
            logger.info(f"单轮对话测试完成: {case['description']}\n")
    
    logger.info("=== ConversationFlow集成测试完成 ===")

async def main():
    """主函数"""
    logger.info("开始领域分类系统测试")
    
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    
    try:
        # 测试DomainClassifierAgent
        await test_domain_classifier()
        
        # 测试ConversationFlowAgent
        await test_conversation_flow()
        
        logger.info("所有测试完成")
    except Exception as e:
        logger.error(f"测试过程中出现错误: {str(e)}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
