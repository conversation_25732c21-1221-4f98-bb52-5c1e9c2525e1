#!/usr/bin/env python3
"""
会话日志测试脚本

此脚本用于测试优化后的会话日志功能：
1. 记录所有与session相关的操作，包括用户输入、AI回复、状态变更、关键业务节点
2. 日志内容必须带session_id、stage
3. 只保留INFO及以上，DEBUG只在排查时临时打开
4. 按天/大小切割
"""

import sys
import os
import time
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.utils.logging_config import (
    configure_logging, 
    SessionLogger
)

def test_session_logger_basic():
    """测试基础会话日志功能"""
    print("=== 测试基础会话日志功能 ===")
    
    # 配置日志系统
    configure_logging(
        log_level=logging.DEBUG,
        enable_console=True,
        enable_file=True,
        max_bytes=10*1024*1024,
        backup_count=7,
        json_format_enabled=True
    )
    
    session_id = f"test_session_{int(time.time())}"
    session_logger = SessionLogger(__name__, session_id=session_id, user_id="test_user_123")
    
    # 测试会话开始
    session_logger.log_session_start(
        user_agent="Mozilla/5.0 (Test Browser)",
        ip_address="*************"
    )
    
    print("✅ 基础会话日志功能测试完成")

def test_user_interaction_logging():
    """测试用户交互日志"""
    print("=== 测试用户交互日志 ===")
    
    session_id = f"test_session_{int(time.time())}"
    session_logger = SessionLogger(__name__, session_id=session_id, user_id="test_user_123")
    
    # 测试用户输入
    session_logger.log_user_input(
        message="你好，我想了解一下产品功能",
        input_type="text",
        channel="web"
    )
    
    # 测试AI回复
    session_logger.log_ai_response(
        response="您好！我很乐意为您介绍我们的产品功能...",
        model="deepseek-chat",
        duration=1.5,
        confidence=0.95
    )
    
    print("✅ 用户交互日志测试完成")

def test_state_management_logging():
    """测试状态管理日志"""
    print("=== 测试状态管理日志 ===")
    
    session_id = f"test_session_{int(time.time())}"
    session_logger = SessionLogger(__name__, session_id=session_id, user_id="test_user_123")
    
    # 测试状态变更
    session_logger.log_state_change(
        from_state="idle",
        to_state="processing",
        trigger="user_message_received"
    )
    
    session_logger.log_state_change(
        from_state="processing",
        to_state="waiting_for_llm",
        trigger="llm_call_initiated"
    )
    
    session_logger.log_state_change(
        from_state="waiting_for_llm",
        to_state="generating_response",
        trigger="llm_response_received"
    )
    
    session_logger.log_state_change(
        from_state="generating_response",
        to_state="idle",
        trigger="response_sent"
    )
    
    print("✅ 状态管理日志测试完成")

def test_business_nodes_logging():
    """测试关键业务节点日志"""
    print("=== 测试关键业务节点日志 ===")
    
    session_id = f"test_session_{int(time.time())}"
    session_logger = SessionLogger(__name__, session_id=session_id, user_id="test_user_123")
    
    # 测试各种业务节点
    session_logger.log_business_node(
        node_name="intent_recognition",
        node_type="nlp_processing",
        result="success",
        confidence=0.89
    )
    
    session_logger.log_business_node(
        node_name="domain_classification",
        node_type="classification",
        result="success",
        domain="product_inquiry"
    )
    
    session_logger.log_business_node(
        node_name="information_extraction",
        node_type="extraction",
        result="partial_success",
        extracted_entities=["产品", "功能"]
    )
    
    session_logger.log_business_node(
        node_name="response_generation",
        node_type="generation",
        result="success",
        response_type="informative"
    )
    
    print("✅ 关键业务节点日志测试完成")

def test_llm_call_logging():
    """测试LLM调用日志"""
    print("=== 测试LLM调用日志 ===")
    
    session_id = f"test_session_{int(time.time())}"
    session_logger = SessionLogger(__name__, session_id=session_id, user_id="test_user_123")
    
    # 测试LLM调用记录
    session_logger.log_llm_call(
        model="deepseek-chat",
        scenario="conversation",
        prompt_length=150,
        response_length=300,
        duration=2.3,
        token_usage={
            "prompt_tokens": 120,
            "completion_tokens": 180,
            "total_tokens": 300
        }
    )
    
    session_logger.log_llm_call(
        model="deepseek-chat",
        scenario="intent_recognition",
        prompt_length=80,
        response_length=50,
        duration=0.8,
        token_usage={
            "prompt_tokens": 60,
            "completion_tokens": 30,
            "total_tokens": 90
        }
    )
    
    print("✅ LLM调用日志测试完成")

def test_data_operations_logging():
    """测试数据操作日志"""
    print("=== 测试数据操作日志 ===")
    
    session_id = f"test_session_{int(time.time())}"
    session_logger = SessionLogger(__name__, session_id=session_id, user_id="test_user_123")
    
    # 测试数据操作记录
    session_logger.log_data_operation(
        operation="INSERT",
        table="conversations",
        result="success",
        affected_rows=1
    )
    
    session_logger.log_data_operation(
        operation="UPDATE",
        table="user_sessions",
        result="success",
        affected_rows=1
    )
    
    session_logger.log_data_operation(
        operation="SELECT",
        table="user_preferences",
        result="success",
        affected_rows=3
    )
    
    print("✅ 数据操作日志测试完成")

def test_nlp_processing_logging():
    """测试NLP处理日志"""
    print("=== 测试NLP处理日志 ===")
    
    session_id = f"test_session_{int(time.time())}"
    session_logger = SessionLogger(__name__, session_id=session_id, user_id="test_user_123")
    
    # 测试意图识别
    session_logger.log_intent_recognition(
        intent="product_inquiry",
        confidence=0.92,
        entities={
            "product_type": "软件",
            "inquiry_type": "功能介绍"
        }
    )
    
    # 测试领域分类
    session_logger.log_domain_classification(
        domain_id="PROD_001",
        domain_name="产品咨询",
        confidence=0.88
    )
    
    # 测试信息提取
    session_logger.log_information_extraction(
        extracted_count=3,
        total_points=5,
        extraction_result={
            "关注点1": "产品功能",
            "关注点2": "价格信息",
            "关注点3": "使用方式"
        }
    )
    
    print("✅ NLP处理日志测试完成")

def test_session_lifecycle():
    """测试完整会话生命周期"""
    print("=== 测试完整会话生命周期 ===")
    
    session_id = f"test_session_{int(time.time())}"
    session_logger = SessionLogger(__name__, session_id=session_id, user_id="test_user_123")
    
    # 1. 会话开始
    session_logger.log_session_start(
        user_agent="Mozilla/5.0 (Test Browser)",
        ip_address="*************"
    )
    
    # 2. 用户输入
    session_logger.log_user_input(
        message="你好，我想了解一下你们的产品",
        input_type="text"
    )
    
    # 3. 意图识别
    session_logger.log_intent_recognition(
        intent="product_inquiry",
        confidence=0.95
    )
    
    # 4. LLM调用
    session_logger.log_llm_call(
        model="deepseek-chat",
        scenario="conversation",
        prompt_length=100,
        response_length=200,
        duration=1.8
    )
    
    # 5. AI回复
    session_logger.log_ai_response(
        response="您好！我很乐意为您介绍我们的产品...",
        model="deepseek-chat",
        duration=1.8
    )
    
    # 6. 数据保存
    session_logger.log_data_operation(
        operation="INSERT",
        table="conversations",
        result="success",
        affected_rows=1
    )
    
    # 7. 会话结束
    session_logger.log_session_end(
        reason="user_disconnect",
        duration=45.6
    )
    
    print("✅ 完整会话生命周期测试完成")

def test_error_in_session():
    """测试会话中的错误记录"""
    print("=== 测试会话中的错误记录 ===")
    
    session_id = f"test_session_{int(time.time())}"
    session_logger = SessionLogger(__name__, session_id=session_id, user_id="test_user_123")
    
    # 测试会话错误记录
    session_logger.log_error_in_session(
        error_type="llm_timeout",
        error_message="LLM服务响应超时",
        stage="llm_processing",
        timeout_duration=30.0
    )
    
    session_logger.log_error_in_session(
        error_type="data_save_failed",
        error_message="保存对话记录失败",
        stage="data_processing",
        table="conversations"
    )
    
    print("✅ 会话错误记录测试完成")

def check_session_log_file():
    """检查会话日志文件"""
    print("=== 检查会话日志文件 ===")
    
    session_log_path = Path("logs/session.log")
    if not session_log_path.exists():
        print("❌ 会话日志文件不存在")
        return
    
    with open(session_log_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"📊 会话日志文件包含 {len(lines)} 行")
    
    # 检查最近的几条会话日志
    print("\n📋 最近的会话日志 (最多显示5条):")
    recent_lines = lines[-5:] if len(lines) >= 5 else lines
    
    for i, line in enumerate(recent_lines, 1):
        try:
            import json
            log_data = json.loads(line.strip())
            timestamp = log_data.get('timestamp', 'N/A')
            level = log_data.get('level', 'N/A')
            session_id = log_data.get('session_id', 'N/A')
            stage = log_data.get('stage', 'N/A')
            log_type = log_data.get('type', 'N/A')
            message = log_data.get('message', 'N/A')[:80] + "..." if len(log_data.get('message', '')) > 80 else log_data.get('message', 'N/A')
            
            print(f"  {i}. [{timestamp}] {level}")
            print(f"     会话: {session_id} | 阶段: {stage} | 类型: {log_type}")
            print(f"     消息: {message}")
            
        except json.JSONDecodeError:
            print(f"  {i}. [解析失败] {line.strip()[:100]}...")
    
    print("✅ 会话日志文件检查完成")

def main():
    """主测试函数"""
    print("开始会话日志功能测试")
    print("=" * 50)
    
    try:
        test_session_logger_basic()
        print()
        
        test_user_interaction_logging()
        print()
        
        test_state_management_logging()
        print()
        
        test_business_nodes_logging()
        print()
        
        test_llm_call_logging()
        print()
        
        test_data_operations_logging()
        print()
        
        test_nlp_processing_logging()
        print()
        
        test_session_lifecycle()
        print()
        
        test_error_in_session()
        print()
        
        check_session_log_file()
        print()
        
        print("🎉 所有会话日志测试完成！")
        print("\n建议检查以下内容：")
        print("1. logs/session.log - 查看结构化的会话日志")
        print("2. 确认所有日志都包含session_id和stage信息")
        print("3. 确认只记录了INFO及以上级别的日志")
        print("4. 确认会话相关的操作都被完整记录")
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
