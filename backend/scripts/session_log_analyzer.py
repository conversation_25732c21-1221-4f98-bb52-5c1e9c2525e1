#!/usr/bin/env python3
"""
会话日志分析工具

此工具专门用于分析session.log文件，提供以下功能：
1. 会话统计和分析
2. 用户行为分析
3. 会话流程追踪
4. 性能分析
5. 会话质量评估
"""

import json
import sys
import argparse
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import List, Dict, Any
import re

class SessionLogAnalyzer:
    """会话日志分析器"""
    
    def __init__(self, log_file: str):
        self.log_file = Path(log_file)
        self.session_logs = []
        self.sessions = defaultdict(list)
        self.load_session_logs()
    
    def load_session_logs(self):
        """加载会话日志文件"""
        if not self.log_file.exists():
            print(f"❌ 会话日志文件不存在: {self.log_file}")
            return
        
        print(f"📖 加载会话日志文件: {self.log_file}")
        
        with open(self.log_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    log_entry = json.loads(line)
                    log_entry['_line_number'] = line_num
                    self.session_logs.append(log_entry)
                    
                    # 按session_id分组
                    session_id = log_entry.get('session_id', 'unknown')
                    self.sessions[session_id].append(log_entry)
                    
                except json.JSONDecodeError as e:
                    print(f"⚠️  第 {line_num} 行JSON解析失败: {e}")
        
        print(f"✅ 成功加载 {len(self.session_logs)} 条会话日志记录")
        print(f"✅ 发现 {len(self.sessions)} 个会话")
    
    def analyze_session_overview(self):
        """分析会话概览"""
        print("\n" + "="*60)
        print("👥 会话概览分析")
        print("="*60)
        
        if not self.session_logs:
            print("❌ 没有会话日志数据")
            return
        
        # 时间范围
        timestamps = [log.get('timestamp') for log in self.session_logs if log.get('timestamp')]
        if timestamps:
            start_time = min(timestamps)
            end_time = max(timestamps)
            print(f"⏰ 时间范围: {start_time} ~ {end_time}")
        
        # 会话统计
        print(f"\n📊 会话统计:")
        print(f"  总会话数: {len(self.sessions)}")
        print(f"  总日志条数: {len(self.session_logs)}")
        print(f"  平均每会话日志数: {len(self.session_logs) / len(self.sessions):.1f}")
        
        # 会话类型统计
        session_types = Counter()
        for session_id, logs in self.sessions.items():
            types = set(log.get('type', 'unknown') for log in logs)
            if 'session_start' in types and 'session_end' in types:
                session_types['完整会话'] += 1
            elif 'session_start' in types:
                session_types['未结束会话'] += 1
            else:
                session_types['片段会话'] += 1
        
        print(f"\n📈 会话类型分布:")
        for session_type, count in session_types.items():
            print(f"  {session_type}: {count} 个")
        
        # 日志类型统计
        log_types = Counter(log.get('type', 'unknown') for log in self.session_logs)
        print(f"\n🏷️  日志类型统计:")
        for log_type, count in log_types.most_common():
            print(f"  {log_type}: {count} 条")
    
    def analyze_user_behavior(self):
        """分析用户行为"""
        print("\n" + "="*60)
        print("👤 用户行为分析")
        print("="*60)
        
        # 用户输入分析
        user_inputs = [log for log in self.session_logs if log.get('type') == 'user_input']
        if user_inputs:
            print(f"📝 用户输入统计:")
            print(f"  总输入次数: {len(user_inputs)}")
            
            input_lengths = [log.get('input_length', 0) for log in user_inputs if log.get('input_length')]
            if input_lengths:
                avg_length = sum(input_lengths) / len(input_lengths)
                print(f"  平均输入长度: {avg_length:.1f} 字符")
                print(f"  最长输入: {max(input_lengths)} 字符")
                print(f"  最短输入: {min(input_lengths)} 字符")
        
        # AI回复分析
        ai_responses = [log for log in self.session_logs if log.get('type') == 'ai_response']
        if ai_responses:
            print(f"\n🤖 AI回复统计:")
            print(f"  总回复次数: {len(ai_responses)}")
            
            response_lengths = [log.get('response_length', 0) for log in ai_responses if log.get('response_length')]
            if response_lengths:
                avg_length = sum(response_lengths) / len(response_lengths)
                print(f"  平均回复长度: {avg_length:.1f} 字符")
                print(f"  最长回复: {max(response_lengths)} 字符")
                print(f"  最短回复: {min(response_lengths)} 字符")
            
            # 回复时间分析
            durations = [log.get('generation_duration', 0) for log in ai_responses if log.get('generation_duration')]
            if durations:
                avg_duration = sum(durations) / len(durations)
                print(f"  平均回复时间: {avg_duration:.2f} 秒")
                print(f"  最长回复时间: {max(durations):.2f} 秒")
                print(f"  最短回复时间: {min(durations):.2f} 秒")
    
    def analyze_session_flows(self):
        """分析会话流程"""
        print("\n" + "="*60)
        print("🔄 会话流程分析")
        print("="*60)
        
        # 分析状态变更
        state_changes = [log for log in self.session_logs if log.get('type') == 'state_change']
        if state_changes:
            print(f"🔀 状态变更统计:")
            print(f"  总状态变更次数: {len(state_changes)}")
            
            transitions = Counter()
            for log in state_changes:
                from_state = log.get('from_state', 'unknown')
                to_state = log.get('to_state', 'unknown')
                transitions[f"{from_state} -> {to_state}"] += 1
            
            print(f"  常见状态转换:")
            for transition, count in transitions.most_common(5):
                print(f"    {transition}: {count} 次")
        
        # 分析业务节点
        business_nodes = [log for log in self.session_logs if log.get('type') == 'business_node']
        if business_nodes:
            print(f"\n🎯 业务节点统计:")
            print(f"  总业务节点数: {len(business_nodes)}")
            
            node_types = Counter(log.get('node_type', 'unknown') for log in business_nodes)
            print(f"  节点类型分布:")
            for node_type, count in node_types.most_common():
                print(f"    {node_type}: {count} 次")
            
            node_results = Counter(log.get('result', 'unknown') for log in business_nodes)
            print(f"  节点结果分布:")
            for result, count in node_results.most_common():
                print(f"    {result}: {count} 次")
    
    def analyze_performance(self):
        """分析性能指标"""
        print("\n" + "="*60)
        print("⚡ 性能指标分析")
        print("="*60)
        
        # LLM调用性能
        llm_calls = [log for log in self.session_logs if log.get('type') == 'llm_call']
        if llm_calls:
            print(f"🧠 LLM调用性能:")
            print(f"  总调用次数: {len(llm_calls)}")
            
            durations = [log.get('duration', 0) for log in llm_calls if log.get('duration')]
            if durations:
                avg_duration = sum(durations) / len(durations)
                print(f"  平均调用时间: {avg_duration:.2f} 秒")
                print(f"  最长调用时间: {max(durations):.2f} 秒")
                print(f"  最短调用时间: {min(durations):.2f} 秒")
            
            # 模型使用统计
            models = Counter(log.get('model', 'unknown') for log in llm_calls)
            print(f"  模型使用分布:")
            for model, count in models.most_common():
                print(f"    {model}: {count} 次")
            
            # Token使用统计
            token_usages = [log.get('token_usage', {}) for log in llm_calls if log.get('token_usage')]
            if token_usages:
                total_tokens = sum(usage.get('total_tokens', 0) for usage in token_usages)
                print(f"  总Token使用量: {total_tokens:,}")
                print(f"  平均每次调用Token: {total_tokens / len(token_usages):.1f}")
        
        # 数据操作性能
        data_ops = [log for log in self.session_logs if log.get('type') == 'data_operation']
        if data_ops:
            print(f"\n💾 数据操作统计:")
            print(f"  总操作次数: {len(data_ops)}")
            
            operations = Counter(log.get('operation', 'unknown') for log in data_ops)
            print(f"  操作类型分布:")
            for operation, count in operations.most_common():
                print(f"    {operation}: {count} 次")
            
            results = Counter(log.get('result', 'unknown') for log in data_ops)
            print(f"  操作结果分布:")
            for result, count in results.most_common():
                print(f"    {result}: {count} 次")
    
    def analyze_session_quality(self):
        """分析会话质量"""
        print("\n" + "="*60)
        print("⭐ 会话质量评估")
        print("="*60)
        
        complete_sessions = 0
        successful_sessions = 0
        error_sessions = 0
        
        for session_id, logs in self.sessions.items():
            if session_id == 'unknown':
                continue
                
            log_types = set(log.get('type', 'unknown') for log in logs)
            
            # 检查会话完整性
            if 'session_start' in log_types and 'session_end' in log_types:
                complete_sessions += 1
            
            # 检查会话成功率
            if 'ai_response' in log_types and 'user_input' in log_types:
                successful_sessions += 1
            
            # 检查错误会话
            if 'session_error' in log_types:
                error_sessions += 1
        
        total_sessions = len(self.sessions) - (1 if 'unknown' in self.sessions else 0)
        
        print(f"📊 会话质量指标:")
        print(f"  完整会话率: {complete_sessions / total_sessions * 100:.1f}% ({complete_sessions}/{total_sessions})")
        print(f"  成功会话率: {successful_sessions / total_sessions * 100:.1f}% ({successful_sessions}/{total_sessions})")
        print(f"  错误会话率: {error_sessions / total_sessions * 100:.1f}% ({error_sessions}/{total_sessions})")
        
        # 会话时长分析
        session_durations = []
        for session_id, logs in self.sessions.items():
            if session_id == 'unknown':
                continue
            
            session_end_logs = [log for log in logs if log.get('type') == 'session_end']
            if session_end_logs:
                duration = session_end_logs[0].get('total_duration', 0)
                if duration > 0:
                    session_durations.append(duration)
        
        if session_durations:
            avg_duration = sum(session_durations) / len(session_durations)
            print(f"\n⏱️  会话时长统计:")
            print(f"  平均会话时长: {avg_duration:.1f} 秒")
            print(f"  最长会话: {max(session_durations):.1f} 秒")
            print(f"  最短会话: {min(session_durations):.1f} 秒")
    
    def trace_session(self, session_id: str):
        """追踪特定会话的完整流程"""
        print(f"\n🔍 追踪会话: {session_id}")
        print("="*60)
        
        if session_id not in self.sessions:
            print(f"❌ 会话 {session_id} 不存在")
            return
        
        logs = sorted(self.sessions[session_id], key=lambda x: x.get('timestamp', ''))
        
        print(f"📋 会话流程 (共 {len(logs)} 条记录):")
        for i, log in enumerate(logs, 1):
            timestamp = log.get('timestamp', 'N/A')
            log_type = log.get('type', 'unknown')
            stage = log.get('stage', 'N/A')
            message = log.get('message', 'N/A')[:60] + "..." if len(log.get('message', '')) > 60 else log.get('message', 'N/A')
            
            print(f"  {i:2d}. [{timestamp}] {log_type} ({stage})")
            print(f"      {message}")
            
            # 显示关键信息
            if log_type == 'user_input' and log.get('input_length'):
                print(f"      输入长度: {log['input_length']} 字符")
            elif log_type == 'ai_response' and log.get('response_length'):
                print(f"      回复长度: {log['response_length']} 字符, 耗时: {log.get('generation_duration', 0):.2f}s")
            elif log_type == 'llm_call' and log.get('duration'):
                print(f"      模型: {log.get('model', 'N/A')}, 耗时: {log['duration']:.2f}s")
    
    def generate_session_report(self, output_file: str = None):
        """生成会话分析报告"""
        if output_file is None:
            output_file = f"session_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        print(f"\n📋 生成会话分析报告: {output_file}")
        
        # 统计数据
        report = {
            "report_time": datetime.now().isoformat(),
            "log_file": str(self.log_file),
            "total_sessions": len(self.sessions),
            "total_logs": len(self.session_logs),
            "log_types": dict(Counter(log.get('type', 'unknown') for log in self.session_logs)),
            "user_interactions": len([log for log in self.session_logs if log.get('type') == 'user_input']),
            "ai_responses": len([log for log in self.session_logs if log.get('type') == 'ai_response']),
            "llm_calls": len([log for log in self.session_logs if log.get('type') == 'llm_call']),
            "business_nodes": len([log for log in self.session_logs if log.get('type') == 'business_node']),
            "time_range": {
                "start": min((log.get('timestamp') for log in self.session_logs if log.get('timestamp')), default=None),
                "end": max((log.get('timestamp') for log in self.session_logs if log.get('timestamp')), default=None)
            }
        }
        
        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 报告已保存")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="会话日志分析工具")
    parser.add_argument("log_file", nargs='?', default="logs/session.log", help="会话日志文件路径")
    parser.add_argument("--trace", "-t", help="追踪特定会话ID")
    parser.add_argument("--report", "-r", help="生成报告文件名")
    parser.add_argument("--overview", action="store_true", help="显示概览分析")
    parser.add_argument("--behavior", action="store_true", help="显示用户行为分析")
    parser.add_argument("--flows", action="store_true", help="显示会话流程分析")
    parser.add_argument("--performance", action="store_true", help="显示性能分析")
    parser.add_argument("--quality", action="store_true", help="显示质量评估")
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = SessionLogAnalyzer(args.log_file)
    
    if not analyzer.session_logs:
        print("❌ 没有可分析的会话日志数据")
        return
    
    # 执行分析
    if args.trace:
        analyzer.trace_session(args.trace)
    elif args.overview or not any([args.behavior, args.flows, args.performance, args.quality]):
        analyzer.analyze_session_overview()
    
    if args.behavior:
        analyzer.analyze_user_behavior()
    
    if args.flows:
        analyzer.analyze_session_flows()
    
    if args.performance:
        analyzer.analyze_performance()
    
    if args.quality:
        analyzer.analyze_session_quality()
    
    if args.report:
        analyzer.generate_session_report(args.report)

if __name__ == "__main__":
    main()
