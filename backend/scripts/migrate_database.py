#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库迁移脚本
用于安全地添加code字段到domains、categories和concern_points表
同时保留现有数据
"""

import os
import sys
import sqlite3
import logging
import shutil
import uuid
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('db_migration')

# 数据库文件路径
DB_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'aidatabase.db')

def backup_database():
    """备份数据库文件"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_file = f"{DB_FILE}.backup_{timestamp}"
    try:
        shutil.copy2(DB_FILE, backup_file)
        logger.info(f"数据库已备份到: {backup_file}")
        return backup_file
    except Exception as e:
        logger.error(f"备份数据库失败: {e}")
        sys.exit(1)

def generate_code(name):
    """根据名称生成唯一代码"""
    # 移除特殊字符，转为小写，用下划线替换空格
    base_code = ''.join(c for c in name if c.isalnum() or c.isspace()).lower().replace(' ', '_')
    # 如果为空，使用随机字符串
    if not base_code:
        base_code = uuid.uuid4().hex[:8]
    # 添加随机后缀确保唯一性
    return f"{base_code}_{uuid.uuid4().hex[:4]}"

def migrate_domains(conn):
    """迁移domains表"""
    cursor = conn.cursor()
    
    # 检查是否已有code字段
    cursor.execute("PRAGMA table_info(domains)")
    columns = [col[1] for col in cursor.fetchall()]
    if 'code' in columns:
        logger.info("domains表已有code字段，跳过迁移")
        return
    
    logger.info("开始迁移domains表...")
    
    # 创建临时表
    cursor.execute("""
    CREATE TABLE domains_temp (
        domain_id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        created_at TEXT,
        updated_at TEXT
    )
    """)
    
    # 复制数据到临时表
    cursor.execute("INSERT INTO domains_temp SELECT domain_id, name, description, created_at, updated_at FROM domains")
    
    # 获取所有域名
    cursor.execute("SELECT domain_id, name FROM domains_temp")
    domains = cursor.fetchall()
    
    # 删除原表
    cursor.execute("DROP TABLE domains")
    
    # 创建新表
    cursor.execute("""
    CREATE TABLE domains (
        domain_id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        code TEXT NOT NULL UNIQUE,
        description TEXT NOT NULL,
        created_at TEXT DEFAULT (datetime('now')),
        updated_at TEXT DEFAULT (datetime('now'))
    )
    """)
    
    # 为每个域生成唯一代码并插入新表
    for domain_id, name in domains:
        code = generate_code(name)
        cursor.execute("""
        SELECT COUNT(*) FROM domains WHERE code = ?
        """, (code,))
        while cursor.fetchone()[0] > 0:  # 确保代码唯一
            code = generate_code(name)
        
        # 从临时表获取完整数据
        cursor.execute("""
        SELECT domain_id, name, description, created_at, updated_at 
        FROM domains_temp WHERE domain_id = ?
        """, (domain_id,))
        domain_data = cursor.fetchone()
        
        # 插入到新表
        cursor.execute("""
        INSERT INTO domains (domain_id, name, code, description, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
        """, (domain_data[0], domain_data[1], code, domain_data[2], 
              domain_data[3] or datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 
              domain_data[4] or datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
    
    # 删除临时表
    cursor.execute("DROP TABLE domains_temp")
    
    logger.info(f"domains表迁移完成，共处理 {len(domains)} 条记录")

def migrate_categories(conn):
    """迁移categories表"""
    cursor = conn.cursor()
    
    # 检查是否已有code字段
    cursor.execute("PRAGMA table_info(categories)")
    columns = [col[1] for col in cursor.fetchall()]
    if 'code' in columns:
        logger.info("categories表已有code字段，跳过迁移")
        return
    
    logger.info("开始迁移categories表...")
    
    # 创建临时表
    cursor.execute("""
    CREATE TABLE categories_temp (
        category_id INTEGER PRIMARY KEY,
        domain_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        created_at TEXT,
        updated_at TEXT
    )
    """)
    
    # 复制数据到临时表
    cursor.execute("INSERT INTO categories_temp SELECT category_id, domain_id, name, description, created_at, updated_at FROM categories")
    
    # 获取所有类别
    cursor.execute("SELECT category_id, domain_id, name FROM categories_temp")
    categories = cursor.fetchall()
    
    # 删除原表
    cursor.execute("DROP TABLE categories")
    
    # 创建新表
    cursor.execute("""
    CREATE TABLE categories (
        category_id INTEGER PRIMARY KEY AUTOINCREMENT,
        domain_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        code TEXT NOT NULL,
        description TEXT NOT NULL,
        created_at TEXT DEFAULT (datetime('now')),
        updated_at TEXT DEFAULT (datetime('now')),
        FOREIGN KEY (domain_id) REFERENCES domains(domain_id) ON DELETE CASCADE,
        UNIQUE (domain_id, name),
        UNIQUE (domain_id, code)
    )
    """)
    
    # 为每个类别生成唯一代码并插入新表
    for category_id, domain_id, name in categories:
        code = generate_code(name)
        # 确保在同一domain_id下code唯一
        cursor.execute("""
        SELECT COUNT(*) FROM categories WHERE domain_id = ? AND code = ?
        """, (domain_id, code))
        while cursor.fetchone()[0] > 0:
            code = generate_code(name)
        
        # 从临时表获取完整数据
        cursor.execute("""
        SELECT category_id, domain_id, name, description, created_at, updated_at 
        FROM categories_temp WHERE category_id = ?
        """, (category_id,))
        category_data = cursor.fetchone()
        
        # 插入到新表
        cursor.execute("""
        INSERT INTO categories (category_id, domain_id, name, code, description, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (category_data[0], category_data[1], category_data[2], code, category_data[3],
              category_data[4] or datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
              category_data[5] or datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
    
    # 删除临时表
    cursor.execute("DROP TABLE categories_temp")
    
    logger.info(f"categories表迁移完成，共处理 {len(categories)} 条记录")

def migrate_concern_points(conn):
    """迁移concern_points表"""
    cursor = conn.cursor()
    
    # 检查是否已有code字段
    cursor.execute("PRAGMA table_info(concern_points)")
    columns = [col[1] for col in cursor.fetchall()]
    if 'code' in columns:
        logger.info("concern_points表已有code字段，跳过迁移")
        return
    
    logger.info("开始迁移concern_points表...")
    
    # 创建临时表
    cursor.execute("""
    CREATE TABLE concern_points_temp (
        concern_point_id INTEGER PRIMARY KEY,
        category_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        priority TEXT NOT NULL,
        example TEXT,
        required INTEGER NOT NULL,
        created_at TEXT,
        updated_at TEXT
    )
    """)
    
    # 复制数据到临时表
    cursor.execute("""
    INSERT INTO concern_points_temp 
    SELECT concern_point_id, category_id, name, description, priority, example, required, created_at, updated_at 
    FROM concern_points
    """)
    
    # 获取所有关注点
    cursor.execute("SELECT concern_point_id, category_id, name FROM concern_points_temp")
    concern_points = cursor.fetchall()
    
    # 删除原表
    cursor.execute("DROP TABLE concern_points")
    
    # 创建新表
    cursor.execute("""
    CREATE TABLE concern_points (
        concern_point_id INTEGER PRIMARY KEY AUTOINCREMENT,
        category_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        code TEXT NOT NULL,
        description TEXT NOT NULL,
        priority TEXT NOT NULL CHECK (priority IN ('P0', 'P1', 'P2')),
        example TEXT,
        required INTEGER NOT NULL DEFAULT 0,
        created_at TEXT DEFAULT (datetime('now')),
        updated_at TEXT DEFAULT (datetime('now')),
        FOREIGN KEY (category_id) REFERENCES categories(category_id) ON DELETE CASCADE,
        UNIQUE (category_id, code)
    )
    """)
    
    # 为每个关注点生成唯一代码并插入新表
    for concern_point_id, category_id, name in concern_points:
        code = generate_code(name)
        # 确保在同一category_id下code唯一
        cursor.execute("""
        SELECT COUNT(*) FROM concern_points WHERE category_id = ? AND code = ?
        """, (category_id, code))
        while cursor.fetchone()[0] > 0:
            code = generate_code(name)
        
        # 从临时表获取完整数据
        cursor.execute("""
        SELECT concern_point_id, category_id, name, description, priority, example, required, created_at, updated_at 
        FROM concern_points_temp WHERE concern_point_id = ?
        """, (concern_point_id,))
        concern_point_data = cursor.fetchone()
        
        # 插入到新表
        cursor.execute("""
        INSERT INTO concern_points 
        (concern_point_id, category_id, name, code, description, priority, example, required, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (concern_point_data[0], concern_point_data[1], concern_point_data[2], code, 
              concern_point_data[3], concern_point_data[4], concern_point_data[5], concern_point_data[6],
              concern_point_data[7] or datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
              concern_point_data[8] or datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
    
    # 删除临时表
    cursor.execute("DROP TABLE concern_points_temp")
    
    logger.info(f"concern_points表迁移完成，共处理 {len(concern_points)} 条记录")

def create_indexes(conn):
    """创建必要的索引"""
    cursor = conn.cursor()
    
    # 创建索引
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_categories_domain_id ON categories(domain_id)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_concern_points_category_id ON concern_points(category_id)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_concern_points_code ON concern_points(code)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_categories_code ON categories(code)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_domains_code ON domains(code)")
    
    logger.info("索引创建完成")

def main():
    """主函数"""
    if not os.path.exists(DB_FILE):
        logger.error(f"数据库文件不存在: {DB_FILE}")
        sys.exit(1)
    
    # 备份数据库
    backup_file = backup_database()
    
    try:
        # 连接数据库
        conn = sqlite3.connect(DB_FILE)
        conn.execute("PRAGMA foreign_keys = OFF")  # 临时关闭外键约束
        
        # 开始事务
        conn.execute("BEGIN TRANSACTION")
        
        # 迁移表
        migrate_domains(conn)
        migrate_categories(conn)
        migrate_concern_points(conn)
        
        # 创建索引
        create_indexes(conn)
        
        # 提交事务
        conn.commit()
        conn.execute("PRAGMA foreign_keys = ON")  # 重新启用外键约束
        
        logger.info("数据库迁移成功完成！")
        
    except Exception as e:
        # 发生错误，回滚事务
        if 'conn' in locals():
            conn.rollback()
        logger.error(f"迁移过程中发生错误: {e}")
        logger.info(f"正在从备份恢复数据库...")
        try:
            shutil.copy2(backup_file, DB_FILE)
            logger.info("数据库已从备份恢复")
        except Exception as restore_error:
            logger.error(f"恢复数据库失败: {restore_error}")
            logger.error(f"请手动从备份文件恢复: {backup_file}")
        sys.exit(1)
    finally:
        # 关闭连接
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main()