#!/usr/bin/env python3
"""
测试日志颜色显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.utils.logging_config import configure_logging, get_logger

# 配置日志系统（只启用控制台，不启用文件）
configure_logging(
    enable_console=True,
    enable_file=False,  # 关闭文件日志，只看控制台效果
    log_level=20  # INFO级别
)

# 获取日志记录器
logger = get_logger(__name__)

print("=== 测试日志颜色显示 ===")
print("请查看以下日志消息的颜色效果：\n")

# 测试不同级别的日志
logger.debug("这是一条DEBUG日志")
logger.info("这是一条INFO日志")
logger.warning("这是一条WARNING日志")
logger.error("这是一条ERROR日志")

print("\n=== 测试用户输入颜色 ===")

# 测试用户输入相关的日志颜色
logger.info("[用户输入] 你好，我想设计一个电商网站")
logger.info("用户输入: 这是另一种格式")
logger.info("[输入消息] 这是输入消息格式")
logger.info("输入消息: 这是另一种输入消息格式")
logger.info("[输出内容] 这是输出内容格式")
logger.info("输出内容: 这是另一种输出内容格式")

print("\n=== 测试普通消息 ===")
logger.info("这是普通的INFO消息，应该是青色")
logger.info("这条消息不包含特殊关键词")

print("\n=== 颜色测试完成 ===")
print("如果您看到了不同的颜色，说明颜色配置生效了！")
print("如果所有文字都是同一颜色，可能是终端不支持颜色显示。")
