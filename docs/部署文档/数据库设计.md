# 数据库设计文档

## 概述

本文档描述了需求采集系统的数据库设计，包括表结构、关系和用途。数据库使用 SQLite 实现，位于 `backend/data/aidatabase.db`。

## 表结构

### 1. domains（领域表）

存储系统支持的各个业务领域的基本信息。

| 字段名      | 类型    | 约束                      | 描述                   |
| ----------- | ------- | ------------------------- | ---------------------- |
| domain_id   | INTEGER | PRIMARY KEY AUTOINCREMENT | 领域唯一标识符         |
| name        | TEXT    | NOT NULL UNIQUE           | 领域名称               |
| code        | TEXT    | NOT NULL UNIQUE           | 领域代码，用于程序引用 |
| description | TEXT    | NOT NULL                  | 领域描述               |
| created_at  | TEXT    | DEFAULT (datetime('now')) | 创建时间               |
| updated_at  | TEXT    | DEFAULT (datetime('now')) | 更新时间               |

### 2. categories（类别表）

存储每个领域下的具体类别。

| 字段名      | 类型    | 约束                          | 描述                   |
| ----------- | ------- | ----------------------------- | ---------------------- |
| category_id | INTEGER | PRIMARY KEY AUTOINCREMENT     | 类别唯一标识符         |
| domain_id   | INTEGER | REFERENCES domains(domain_id) | 关联的领域 ID          |
| name        | TEXT    | NOT NULL                      | 类别名称               |
| code        | TEXT    | NOT NULL                      | 类别代码，用于程序引用 |
| description | TEXT    | NOT NULL                      | 类别描述               |
| created_at  | TEXT    | DEFAULT (datetime('now'))     | 创建时间               |
| updated_at  | TEXT    | DEFAULT (datetime('now'))     | 更新时间               |
|             |         | UNIQUE (domain_id, name)      | 确保同一领域内名称唯一 |
|             |         | UNIQUE (domain_id, code)      | 确保同一领域内代码唯一 |

### 3. concern_points（关注点表）

存储每个类别下需要关注的具体点。

| 字段名           | 类型    | 约束                                            | 描述                     |
| ---------------- | ------- | ----------------------------------------------- | ------------------------ |
| concern_point_id | INTEGER | PRIMARY KEY AUTOINCREMENT                       | 关注点唯一标识符         |
| category_id      | INTEGER | REFERENCES categories(category_id)              | 关联的类别 ID            |
| name             | TEXT    | NOT NULL                                        | 关注点名称               |
| code             | TEXT    | NOT NULL                                        | 关注点代码，用于程序引用 |
| description      | TEXT    | NOT NULL                                        | 关注点描述               |
| priority         | TEXT    | NOT NULL CHECK (priority IN ('P0', 'P1', 'P2')) | 优先级                   |
| example          | TEXT    |                                                 | 示例说明                 |
| required         | INTEGER | NOT NULL DEFAULT 0                              | 是否必需(0=否，1=是)     |
| created_at       | TEXT    | DEFAULT (datetime('now'))                       | 创建时间                 |
| updated_at       | TEXT    | DEFAULT (datetime('now'))                       | 更新时间                 |
|                  |         | UNIQUE (category_id, code)                      | 确保同一类别内代码唯一   |

### 4. conversations（会话表）

跟踪需求引导会话的基本信息。

| 字段名               | 类型    | 约束                                                                             | 描述                             |
| -------------------- | ------- | -------------------------------------------------------------------------------- | -------------------------------- |
| conversation_id      | INTEGER | PRIMARY KEY AUTOINCREMENT                                                        | 会话唯一标识符                   |
| domain_id            | INTEGER | REFERENCES domains(domain_id)                                                    | 关联的领域 ID                    |
| category_id          | INTEGER | REFERENCES categories(category_id)                                               | 关联的类别 ID                    |
| classifier_domain_id | INTEGER |                                                                                  | 分类器识别的原始领域 ID          |
| classifier_category  | TEXT    |                                                                                  | 分类器识别的原始类别             |
| status               | TEXT    | NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')) | 会话状态：进行中、已完成、已取消 |
| created_at           | TEXT    | DEFAULT (datetime('now'))                                                        | 创建时间                         |
| updated_at           | TEXT    | DEFAULT (datetime('now'))                                                        | 更新时间                         |

### 5. messages（消息表）

存储会话中的用户输入和 AI 回复。

| 字段名          | 类型    | 约束                                                                 | 描述                  |
| --------------- | ------- | -------------------------------------------------------------------- | --------------------- |
| message_id      | INTEGER | PRIMARY KEY AUTOINCREMENT                                            | 消息唯一标识符        |
| conversation_id | INTEGER | NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE | 关联的会话 ID         |
| sender_type     | TEXT    | NOT NULL CHECK (sender_type IN ('user', 'ai'))                       | 发送者类型：用户或 AI |
| content         | TEXT    | NOT NULL                                                             | 消息内容              |
| created_at      | TEXT    | DEFAULT (datetime('now'))                                            | 创建时间              |

### 6. concern_point_coverage（关注点覆盖表）

跟踪会话中已覆盖的关注点。

| 字段名           | 类型    | 约束                                                                              | 描述                       |
| ---------------- | ------- | --------------------------------------------------------------------------------- | -------------------------- |
| coverage_id      | INTEGER | PRIMARY KEY AUTOINCREMENT                                                         | 覆盖记录唯一标识符         |
| conversation_id  | INTEGER | NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE              | 关联的会话 ID              |
| concern_point_id | INTEGER | NOT NULL REFERENCES concern_points(concern_point_id) ON DELETE CASCADE            | 关联的关注点 ID            |
| is_covered       | INTEGER | NOT NULL DEFAULT 0                                                                | 是否已覆盖（0=否，1=是）   |
| extracted_info   | TEXT    |                                                                                   | 提取或用户提供的关注点信息 |
| created_at       | TEXT    | DEFAULT (datetime('now'))                                                         | 创建时间                   |
| updated_at       | TEXT    | DEFAULT (datetime('now'))                                                         | 更新时间                   |
|                  |         | CONSTRAINT unique_conversation_concern UNIQUE (conversation_id, concern_point_id) | 防止重复覆盖               |

### 7. requirements（需求表）

存储具体需求条目。

| 字段名           | 类型    | 约束                                                                 | 描述                      |
| ---------------- | ------- | -------------------------------------------------------------------- | ------------------------- |
| requirement_id   | INTEGER | PRIMARY KEY AUTOINCREMENT                                            | 需求唯一标识符            |
| conversation_id  | INTEGER | NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE | 关联的会话 ID             |
| concern_point_id | INTEGER | REFERENCES concern_points(concern_point_id)                          | 关联的关注点 ID（可为空） |
| content          | TEXT    | NOT NULL                                                             | 需求描述                  |
| created_at       | TEXT    | DEFAULT (datetime('now'))                                            | 创建时间                  |
| updated_at       | TEXT    | DEFAULT (datetime('now'))                                            | 更新时间                  |

### 8. documents（文档表）

存储生成的需求文档。

| 字段名          | 类型    | 约束                                                                          | 描述                       |
| --------------- | ------- | ----------------------------------------------------------------------------- | -------------------------- |
| document_id     | INTEGER | PRIMARY KEY AUTOINCREMENT                                                     | 文档唯一标识符             |
| conversation_id | INTEGER | NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE          | 关联的会话 ID              |
| version         | INTEGER | NOT NULL DEFAULT 1                                                            | 版本号                     |
| content         | TEXT    | NOT NULL                                                                      | Markdown 格式文档          |
| status          | TEXT    | NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'confirmed', 'rejected')) | 状态：草稿、已确认、已拒绝 |
| feedback        | TEXT    |                                                                               | 用户反馈                   |
| created_at      | TEXT    | DEFAULT (datetime('now'))                                                     | 创建时间                   |
| updated_at      | TEXT    | DEFAULT (datetime('now'))                                                     | 更新时间                   |
|                 |         | CONSTRAINT unique_conversation_version UNIQUE (conversation_id, version)      | 确保版本唯一               |

### 9. domain_classification_mapping（领域分类映射表）

存储领域分类系统与关注点系统之间的映射关系。

| 字段名               | 类型    | 约束                                               | 描述                   |
| -------------------- | ------- | -------------------------------------------------- | ---------------------- |
| mapping_id           | INTEGER | PRIMARY KEY AUTOINCREMENT                          | 映射唯一标识符         |
| classifier_domain_id | INTEGER | NOT NULL                                           | 分类器识别的领域 ID    |
| actual_domain_id     | INTEGER | NOT NULL REFERENCES domains(domain_id)             | 关注点系统中的领域 ID  |
| classifier_category  | TEXT    |                                                    | 分类器识别的类别(可选) |
| actual_category_id   | INTEGER | REFERENCES categories(category_id)                 | 关注点系统中的类别 ID  |
| created_at           | TEXT    | DEFAULT (datetime('now'))                          | 创建时间               |
| updated_at           | TEXT    | DEFAULT (datetime('now'))                          | 更新时间               |
|                      |         | UNIQUE (classifier_domain_id, classifier_category) | 确保映射唯一           |

### 10. category_recognition_results（类别识别结果表）

存储类别识别的结果信息。

| 字段名                 | 类型    | 约束                                                                 | 描述           |
| ---------------------- | ------- | -------------------------------------------------------------------- | -------------- |
| result_id              | INTEGER | PRIMARY KEY AUTOINCREMENT                                            | 结果唯一标识符 |
| conversation_id        | INTEGER | NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE | 关联的会话 ID  |
| input_text             | TEXT    | NOT NULL                                                             | 用户输入文本   |
| recognized_domain_id   | INTEGER | REFERENCES domains(domain_id)                                        | 识别的领域 ID  |
| recognized_category_id | INTEGER | REFERENCES categories(category_id)                                   | 识别的类别 ID  |
| confidence             | REAL    |                                                                      | 识别置信度     |
| created_at             | TEXT    | DEFAULT (datetime('now'))                                            | 创建时间       |

## 表关系

```mermaid
erDiagram
    domains ||--o{ categories : "contains"
    categories ||--o{ concern_points : "has"
    domains ||--o{ conversations : "belongs_to"
    categories ||--o{ conversations : "belongs_to"
    conversations ||--o{ messages : "contains"
    conversations ||--o{ concern_point_coverage : "tracks"
    conversations ||--o{ requirements : "generates"
    conversations ||--o{ documents : "produces"
    conversations ||--o{ category_recognition_results : "records"
    concern_points ||--o{ concern_point_coverage : "is_tracked_in"
    concern_points ||--o{ requirements : "inspires"
    domains ||--o{ domain_classification_mapping : "is_mapped_to"
    categories ||--o{ domain_classification_mapping : "is_mapped_to"

    domains {
        integer domain_id PK
        text name
        text code
        text description
        text created_at
        text updated_at
    }

    categories {
        integer category_id PK
        integer domain_id FK
        text name
        text code
        text description
        text created_at
        text updated_at
    }

    concern_points {
        integer concern_point_id PK
        integer category_id FK
        text name
        text code
        text description
        text priority
        text example
        integer required
        text created_at
        text updated_at
    }

    conversations {
        integer conversation_id PK
        integer domain_id FK
        integer category_id FK
        integer classifier_domain_id
        text classifier_category
        text status
        text created_at
        text updated_at
    }

    domain_classification_mapping {
        integer mapping_id PK
        integer classifier_domain_id
        integer actual_domain_id FK
        text classifier_category
        integer actual_category_id FK
        text created_at
        text updated_at
    }

    category_recognition_results {
        integer result_id PK
        integer conversation_id FK
        text input_text
        integer recognized_domain_id FK
        integer recognized_category_id FK
        real confidence
        text created_at
    }
```

## 数据访问模式

### KnowledgeBaseAgent

- 通过 `aiosqlite` 进行异步数据库操作
- 使用连接池优化性能
- 实现了各种数据访问方法：
  - `get_domains()`: 获取所有领域
  - `get_categories(domain_id)`: 获取指定领域的类别
  - `get_concern_points(category_id)`: 获取指定类别的关注点
  - `get_domain_by_code(code)`: 通过代码获取领域
  - `get_category_by_code(domain_id, code)`: 通过代码获取类别
  - `map_classifier_domain(classifier_domain_id, classifier_category)`: 映射分类器领域到实际领域

### ClassifierMappingManager

- 管理分类器识别结果与实际领域/类别的映射
- 提供映射查询和更新功能
- 支持自动映射和手动映射

### ConversationManager

- 管理会话生命周期
- 提供会话创建、更新和查询功能
- 跟踪会话状态变化

### MessageRepository

- 存储和检索会话消息
- 支持按会话 ID 和时间范围查询

### ConcernPointTracker

- 跟踪关注点覆盖情况
- 更新已提取的关注点信息
- 计算会话完成度

### DocumentGenerator

- 基于会话和需求生成文档
- 管理文档版本
- 处理用户反馈

## 索引优化

系统创建了以下索引以提高查询性能：

1. `idx_categories_domain_id`: 加速按领域查询类别
2. `idx_concern_points_category_id`: 加速按类别查询关注点
3. `idx_conversations_domain_id`: 加速按领域查询会话
4. `idx_conversations_category_id`: 加速按类别查询会话
5. `idx_messages_conversation_id`: 加速按会话查询消息
6. `idx_concern_point_coverage_conversation_id`: 加速按会话查询关注点覆盖情况
7. `idx_requirements_conversation_id`: 加速按会话查询需求
8. `idx_documents_conversation_id`: 加速按会话查询文档
9. `idx_concern_points_code`: 加速按代码查询关注点
10. `idx_categories_code`: 加速按代码查询类别
11. `idx_domains_code`: 加速按代码查询领域
12. `idx_domain_classification_mapping_classifier`: 加速分类器映射查询

## 性能优化

1. 使用连接池减少数据库连接开销
2. 实现查询缓存减少重复查询
3. 使用索引优化查询性能
4. 启用慢查询日志进行性能监控
5. 使用事务确保数据一致性

## 数据库初始化

数据库初始化 SQL 文件位于 `backend/scripts/create_tables.sql`，包含：

1. 创建所有必要的表
2. 创建索引
3. 设置约束条件

执行初始化：

```bash
python backend/scripts/create_conversation_tables.py
```

## 维护建议

1. 定期备份数据库文件

   ```bash
   python -m backend.scripts.backup_db
   ```

2. 监控数据库性能

   ```bash
   python -m backend.scripts.diagnose
   ```

3. 定期清理过期数据

   ```bash
   python -m backend.scripts.clean_old_data --days 90
   ```

4. 优化数据库

   ```bash
   python -m backend.scripts.optimize_db
   ```

5. 保持数据的一致性和完整性
