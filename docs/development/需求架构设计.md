# 需求采集系统架构设计

## 1. 背景与目标

### 1.1 当前问题

当前需求采集系统在处理用户输入时存在一些局限性：

- 过度依赖简单规则（如消息长度）来判断用户意图
- 缺乏对对话上下文的充分理解
- 流程较为刚性，难以适应用户的自然交流方式
- 未充分利用大语言模型的理解能力

### 1.2 设计目标

- 创建一个更灵活、智能的对话管理架构
- 充分利用LLM的语言理解能力
- 支持用户在任何阶段的自由表达
- 保持对话流程的连贯性和目标导向性
- 提高系统的鲁棒性和可扩展性

---

## 2. 架构概述

### 2.1 核心组件

```mermaid
graph TD
    A[用户输入] --> B[对话管理器]
    B --> C[意图识别器]
    C --> B
    B --> D[领域分类器]
    B --> E[采集AI]
    B --> F[文档生成器]
    B --> G[上下文管理器]
    H[知识库] --> B
    B --> I[用户响应]
```

### 2.2 主要流程

1. **用户输入处理**：接收并预处理用户输入
2. **意图识别**：分析用户意图和提取关键信息
3. **流程控制决策**：基于意图和当前状态决定下一步行动
4. **专业AI调用**：根据决策调用相应的专业AI模块
5. **响应生成**：生成上下文相关的回复
6. **状态更新**：更新会话状态和历史记录

---

## 3. 核心组件设计

### 3.1 对话管理器 (DialogueManager)

对话管理器是系统的中央协调者，负责整个对话流程的管理。

#### 3.1.1 主要职责

- 接收和处理用户输入
- 协调不同AI模块的工作
- 基于意图和会话状态做出决策
- 维护对话的连贯性和目标导向性
- 处理异常情况和流程偏离

#### 3.1.2 关键方法

- `process_message(user_input)`: 处理用户消息的主入口
- `decide_next_action(intent_result, current_stage, session_state)`: 决定下一步行动
- `execute_action(action, user_input, session_state, intent_result)`: 执行决定的行动
- `generate_contextual_response(user_input, session_state)`: 生成上下文相关的回复

---

### 3.2 意图识别器 (IntentRecognizer)

意图识别器负责理解用户输入的真实意图，是系统智能化的关键组件。

#### 3.2.1 主要职责

- 分析用户输入，识别意图类型
- 考虑对话上下文和会话状态
- 提取用户输入中的关键信息
- 为意图分配合理的置信度

#### 3.2.2 支持的意图类型

- **inform**: 用户提供信息或回答问题，包括简短的确认回复
- **clarify**: 用户请求澄清或需要更多信息
- **complete**: 用户表示任务已完成或希望结束
- **set_value**: 用户想要设置某个关注点的值
- **remove_value**: 用户想要移除某个关注点的值
- **request_action**: 用户请求AI执行某个操作
- **confirm**: 用户明确确认某个提议或文档
- **reject**: 用户拒绝某个提议或文档
- **modify**: 用户希望修改已提供的信息或生成的文档

#### 3.2.3 提示词优化

优化后的提示词更简洁，支持多意图处理：

```markdown
# 意图识别提示词

## 当前任务

分析用户输入，识别其意图类型，并提取相关信息。考虑对话上下文和会话状态，做出准确判断。

## 当前会话信息

**用户输入**: {user_input}

**对话历史**: {conversation_history}

**当前状态**: {current_state}

## 输出格式

- intent_type: 意图类型
- confidence: 置信度
- extracted_values: 提取的关键信息
- reasoning: 判断理由
```

---

### 3.3 上下文管理器 (ContextManager)

上下文管理器负责维护对话的上下文信息，为其他模块提供支持。

#### 3.3.1 主要职责

- 维护对话历史和当前状态
- 提供上下文信息给意图识别器和其他模块
- 支持多轮对话的上下文追踪

#### 3.3.2 关键方法

- `get_context(session_id)`: 获取当前会话的上下文
- `update_context(session_id, key, value)`: 更新会话上下文
- `clear_context(session_id)`: 清除会话上下文

---

### 3.4 领域分类器 (DomainClassifier)

领域分类器负责根据用户输入判断所属领域。

#### 3.4.1 改进功能

- **多轮分类**：如果初次分类置信度较低，通过后续对话进一步确认。
- **分类解释**：提供分类结果的解释，帮助用户理解分类依据。

---

## 4. 流程设计

### 4.1 会话阶段

系统定义了以下主要会话阶段：

1. **initial**: 初始阶段，主要进行领域分类
2. **collecting**: 收集阶段，获取关注点信息
3. **confirming**: 确认阶段，确认生成的需求文档
4. **completed**: 完成阶段，需求采集已完成

### 4.2 阶段转换逻辑

```mermaid
stateDiagram-v2
    [*] --> initial
    initial --> collecting: 领域分类成功
    collecting --> confirming: 关键信息已收集
    confirming --> collecting: 用户请求修改
    confirming --> completed: 用户确认
    completed --> [*]
```

---

## 5. 实施路线图

### 5.1 阶段一：基础架构实现

1. 实现对话管理器框架
2. 开发增强版意图识别器
3. 优化提示词模板
4. 实现基本的会话状态管理

### 5.2 阶段二：专业模块优化

1. 改进领域分类器
2. 增强采集AI的信息提取能力
3. 优化文档生成器
4. 实现上下文管理器

### 5.3 阶段三：系统集成与测试

1. 集成所有组件
2. 进行端到端测试
3. 性能优化
4. 用户体验改进

---

## 6. 技术考量

### 6.1 性能优化

- 使用缓存减少重复LLM调用
- 优化提示词长度和复杂度
- 实现并行处理机制
- 采用异步编程模式

### 6.2 可扩展性设计

- 模块化架构，便于添加新功能
- 标准化接口，支持不同LLM提供商
- 可配置的意图类型和处理逻辑
- 支持多语言和多模态扩展

### 6.3 安全与隐私

- 实现会话数据加密
- 遵循最小权限原则
- 提供数据清理和删除机制
- 合规性考量

---

## 7. 总结

本架构设计通过引入智能对话管理器和增强的意图识别能力，显著提升了需求采集系统的灵活性和智能性。系统不再依赖简单规则来判断用户意图，而是充分利用LLM的理解能力，考虑对话上下文和会话状态，提供更自然、流畅的对话体验。

这种设计不仅解决了当前的短回复处理问题，还为系统提供了更强大的架构基础，使其能够处理更复杂的对话场景和用户需求。通过分阶段实施，可以逐步提升系统能力，同时保持系统的稳定性和可靠性。