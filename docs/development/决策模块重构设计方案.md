# 决策模块重构设计方案

## 1. 设计背景
当前决策模块存在以下问题：
- 决策逻辑与执行逻辑耦合度高
- 新增策略需要修改代码
- 缺乏统一的配置管理

重构目标：
- 实现决策与执行的解耦
- 支持配置化策略管理
- 提高系统可扩展性

## 2. 架构设计

```mermaid
graph TD
    A[意图决策引擎] -->|决策结果| B[ConversationFlow]
    B -->|根据handler配置| C[执行AI服务]
    D[策略配置YAML] -->|提供映射规则| B
```

## 3. 核心变更

### 3.1 决策返回值结构
```python
{
    "intent": "ask_question",
    "strategy": {
        "action": "generate_clarification",
        "handler": "information_extractor.process_question",
        "params": {
            "question": "原始问题内容",
            "context": "会话上下文"
        }
    }
}
```

### 3.2 策略配置文件
路径：`backend/config/strategy_handlers.yaml`

示例：
```yaml
version: 1.0
handlers:
  - intent: "ask_question"
    actions:
      - action: "generate_clarification"
        handler: "information_extractor.process_question"
        params_mapping:
          question: "$.message.content"
          context: "$.context"
```

## 4. 实施步骤

1. 创建策略配置文件
2. 实现配置加载器
3. 重构ConversationFlow执行逻辑
4. 编写单元测试
5. 更新相关文档

## 5. 附录

### 5.1 异常处理方案
- 配置缺失fallback机制
- 调用超时重试策略
- 错误日志规范

### 5.2 性能考量
- 配置预加载
- 缓存热点策略
- 异步加载机制
