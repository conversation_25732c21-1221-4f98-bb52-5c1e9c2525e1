# 会话恢复测试指南

## 🎯 测试目标

验证系统的会话恢复功能是否正常工作，确保用户退出后重新进入能够无缝继续之前的对话。

## 🚀 快速开始

### 1. 启动系统

```bash
# 启动后端服务
cd backend
python -m uvicorn api.main:app --reload --port 8000

# 启动前端服务（新终端）
cd frontend
npm run dev
```

### 2. 使用前端测试面板

1. **打开浏览器**访问 `http://localhost:3000`
2. **找到测试面板**：页面右下角的蓝色"测试面板"按钮
3. **点击展开**测试面板

## 📋 测试步骤

### 测试场景1：基础会话恢复

1. **开始新对话**
   - 在聊天框输入："我想开发一个在线教育平台"
   - 观察系统响应和session_id

2. **记录会话ID**
   - 在测试面板中查看当前会话ID
   - 点击复制按钮复制会话ID

3. **继续对话**
   - 回答系统的问题，例如："主要面向K12学生，包含在线课程和作业系统"
   - 继续回答几个关注点问题

4. **模拟退出**
   - 点击"生成新会话ID"按钮（模拟用户退出）
   - 或者刷新页面

5. **恢复会话**
   - 在测试面板的"设置会话ID"输入框中粘贴之前复制的会话ID
   - 点击"设置"按钮
   - 点击"测试会话恢复"按钮

6. **验证恢复效果**
   - 观察系统是否能识别之前的对话状态
   - 检查是否继续询问未完成的关注点
   - 验证不会重复询问已回答的问题

### 测试场景2：不同状态恢复

**测试COLLECTING_INFO状态恢复：**
1. 进行到信息收集阶段（已确定领域类别）
2. 记录会话ID并切换到新会话
3. 恢复原会话ID，验证是否继续收集信息

**测试DOCUMENTING状态恢复：**
1. 完成所有关注点，等待文档生成
2. 记录会话ID并切换到新会话
3. 恢复原会话ID，验证是否显示已生成的文档

## 🛠️ 测试面板功能说明

### 主要功能

1. **当前会话ID显示**
   - 显示当前正在使用的会话ID
   - 支持一键复制

2. **手动设置会话ID**
   - 输入框：手动输入要恢复的会话ID
   - 设置按钮：应用新的会话ID

3. **测试操作**
   - 生成新会话ID：创建全新的会话
   - 测试会话恢复：发送恢复测试消息

### 使用技巧

- **复制会话ID**：点击复制按钮，会话ID会保存到剪贴板
- **快速设置**：在输入框中按Enter键快速设置会话ID
- **恢复测试**：点击"测试会话恢复"会自动发送"请继续我们之前的对话"消息

## 🔍 验证要点

### ✅ 成功标志

- [ ] 系统能识别历史会话
- [ ] 不重复询问已回答的问题
- [ ] 正确恢复对话状态（IDLE/COLLECTING_INFO/DOCUMENTING）
- [ ] 关注点处理状态正确恢复
- [ ] 消息历史完整保留

### ❌ 失败标志

- [ ] 系统不识别历史会话ID
- [ ] 重复询问已回答的问题
- [ ] 对话状态错误
- [ ] 关注点状态丢失
- [ ] 消息历史缺失

## 🐛 问题排查

### 常见问题

1. **测试面板不显示**
   - 检查前端是否正确启动
   - 查看浏览器控制台是否有错误

2. **会话恢复失败**
   - 检查后端服务是否正常运行
   - 查看后端日志：`tail -f logs/session.log`
   - 验证数据库中是否有对应的会话数据

3. **API连接失败**
   - 确认后端运行在 `http://localhost:8000`
   - 检查网络请求是否正常

### 调试命令

```bash
# 查看会话日志
tail -f logs/session.log | grep "your_session_id"

# 查看应用日志
tail -f logs/app.log | grep -E "(恢复|restore)"

# 检查数据库数据
sqlite3 backend/data/aidatabase.db
SELECT * FROM conversations WHERE conversation_id = 'your_session_id';
```

## 📊 测试报告

测试完成后，请记录：

1. **测试环境**：操作系统、浏览器版本
2. **测试结果**：各场景的通过/失败状态
3. **发现的问题**：详细描述任何异常行为
4. **改进建议**：对功能的改进建议

## 🎉 测试完成

如果所有测试场景都通过，说明会话恢复功能工作正常！

如果有测试失败，请：
1. 记录具体的失败场景
2. 收集相关日志信息
3. 检查数据库状态
4. 报告问题以便修复
