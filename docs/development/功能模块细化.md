# AI 驱动的多领域需求引导系统功能模块细化文档（基于现有大模型）

## 1. 初始需求分析模块

### 1.1 输入解析器

- **功能描述**：接收并处理用户的原始文本输入
- **处理逻辑**：
  - 文本预处理：去除冗余空格、标点规范化
  - 分句分段：将文本分割为可处理的单元
  - 语言检测：识别输入语言类型，支持多语言处理
- **输入参数**：原始文本字符串
- **输出参数**：预处理后的文本结构
- **大模型集成**：
  - 使用大模型的文本清理能力，通过结构化提示词指导大模型规范化文本
  - 设计预处理指令模板，确保输出格式一致性

### 1.2 实体识别组件

- **功能描述**：利用大模型识别文本中的关键实体
- **处理逻辑**：
  - 提示词设计：设计专门用于实体提取的提示词模板
  - 结构化输出定义：定义 JSON 格式的实体输出结构
  - 多次验证：使用不同提示策略交叉验证实体识别结果
- **识别实体类型**：
  - 技术类：编程语言、框架、平台、工具等
  - 业务类：行业术语、流程名称、角色名称等
  - 性能类：速度要求、容量要求、安全要求等
- **输入参数**：预处理后的文本
- **输出参数**：实体列表（实体名称、类型、位置、置信度）
- **提示词优化**：
  - 使用 Few-shot 示例增强识别准确率
  - 设计多轮提取策略，先粗后细提高覆盖率

### 1.3 关系提取组件

- **功能描述**：利用大模型分析实体间的逻辑关系
- **处理逻辑**：
  - 关系提示词构建：基于已识别实体构建关系提取提示词
  - 链式思考提示：引导大模型逐步推理实体间关系
  - 结构化输出解析：将自然语言输出转换为结构化关系数据
- **关系类型**：
  - 组成关系：A 是 B 的一部分
  - 依赖关系：A 依赖于 B
  - 因果关系：A 导致 B
  - 时序关系：A 在 B 之前
- **输入参数**：实体列表、预处理后的文本
- **输出参数**：关系图谱（实体对、关系类型、置信度）
- **API 优化策略**：
  - 批量处理实体对减少 API 调用次数
  - 使用缓存机制存储已识别的关系模式

### 1.4 情感分析组件

- **功能描述**：利用大模型评估用户对各需求点的重视程度
- **处理逻辑**：
  - 情感分析提示词：设计针对重要性评估的提示词
  - 多维度评分指导：引导模型给出多维度评分
  - 证据提取：要求大模型提供评分依据
- **评估维度**：
  - 重要性：高、中、低三级
  - 紧急度：高、中、低三级
  - 确定性：确定、可能、不确定三级
- **输入参数**：预处理后的文本、实体列表
- **输出参数**：情感评估结果（实体、维度、评分）
- **置信度控制**：
  - 设置最低置信度阈值，低于阈值的评估标记为"待确认"
  - 对低置信度评估使用反向提问策略获取用户确认

### 1.5 初始信息评估器

- **功能描述**：判断初始信息是否足够进行下一步处理
- **处理逻辑**：
  - 信息完整度评估：使用大模型评估信息完整性
  - 缺失信息识别：识别关键缺失的信息点
  - 基于规则的决策：根据评估结果决定下一步操作
- **决策规则**：
  - 满分 100 分，70 分以上直接进入领域识别
  - 50-70 分请求补充特定信息
  - 50 分以下请求用户重新描述需求
- **输入参数**：实体列表、关系图谱、情感评估结果
- **输出参数**：评估结果（分数、决策建议、缺失信息列表）
- **提示词策略**：
  - 使用结构化打分提示词获取量化评估结果
  - 设计信息缺失识别专用提示模板

## 2. 领域识别与关注点生成模块

### 2.1 领域分类器

- **功能描述**：利用大模型识别需求所属的专业领域
- **处理逻辑**：
  - 领域判断提示词：设计领域判断专用提示词
  - 证据要求：要求大模型提供判断依据
  - 多轮确认：通过不同角度的提问交叉验证
- **支持领域**：
  - 软件开发（前端、后端、移动、桌面等细分）
  - UI/UX 设计
  - 数据科学与分析
  - 内容创作与营销
  - 项目管理
  - 产品设计
- **输入参数**：实体列表、关系图谱、预处理后的文本
- **输出参数**：领域分类结果（领域名称、置信度、多标签列表）
- **模型选择策略**：
  - 对专业领域判断使用专业性更强的大模型版本
  - 设置最低置信度阈值进行人工确认

### 2.2 领域知识库接口

- **功能描述**：与大模型交互获取特定领域的专业知识
- **处理逻辑**：
  - 领域知识查询：构建领域知识查询提示词
  - 知识提取与整合：将模型输出整合为结构化知识
  - 知识检索与管理：管理已获取的领域知识缓存
- **知识库结构**：
  - 领域术语库：特定领域专业术语及解释
  - 关注点图谱：领域关键关注点及层次关系
  - 最佳实践库：领域常见问题及解决方案
- **输入参数**：领域分类结果
- **输出参数**：领域知识库（术语列表、关注点图谱、最佳实践列表）
- **缓存策略**：
  - 缓存常用领域知识减少重复 API 调用
  - 定期更新知识库确保时效性

### 2.3 关注点生成器

- **功能描述**：利用大模型基于 MVP 原则创建分层关注点结构
- **处理逻辑**：
  - 关注点提取提示词：设计提取关键问题的提示词
  - 分层策略：指导大模型按照必要、条件、深度三层分类
  - 优先级建议：获取大模型对关注点优先级的建议
- **关注点结构**：
  - 第一层：必要关注点（核心问题，必须解决）
  - 第二层：条件关注点（根据需求规模可能需要解决）
  - 第三层：深度关注点（专业深化问题）
- **输入参数**：领域知识库、实体列表、关系图谱
- **输出参数**：分层关注点列表（关注点 ID、描述、层级、优先级初始值）
- **模型交互优化**：
  - 使用链式提示逐步细化关注点
  - 结合已有实体和关系构建上下文丰富的提示词

### 2.4 优先级排序引擎

- **功能描述**：利用大模型动态调整关注点优先级
- **处理逻辑**：
  - 优先级评估提示词：设计专用于优先级排序的提示词
  - 多因素考量指导：引导大模型综合多因素排序
  - 排序结果解析：将大模型输出转换为结构化优先级数据
- **情境感知因素**：
  - 项目规模感知：大型项目增加深度关注点优先级
  - 用户专业度感知：专业用户增加技术关注点优先级
  - 用户耐心度感知：耐心度低减少非必要关注点数量
- **输入参数**：分层关注点列表、情感评估结果、用户交互历史
- **输出参数**：优先级排序后的关注点列表（关注点 ID、更新优先级）
- **决策链提示**：
  - 使用思维链提示引导大模型逐步推理优先级判断过程
  - 保留推理链作为优先级调整的解释依据

### 2.5 用户确认管理器

- **功能描述**：处理用户对领域和关注点的确认
- **处理逻辑**：
  - 确认提示生成：生成用户友好的确认提示
  - 反馈解析：解析用户对关注点的确认或修改
  - 更新指令生成：基于用户反馈生成更新指令
- **交互模式**：
  - 单选确认：对单一领域的确认
  - 多选确认：对多领域混合的确认
  - 列表调整：允许用户调整关注点列表
- **输入参数**：领域分类结果、分层关注点列表、用户反馈
- **输出参数**：确认后的领域和关注点列表（确认状态、修改记录）
- **交互优化**：
  - 设计简洁明了的确认界面减少用户负担
  - 使用大模型生成用户友好的确认提示文本

## 3. 混合信息采集模块

### 3.1 信息差距分析器

- **功能描述**：利用大模型识别已有信息与完整需求间的差距
- **处理逻辑**：
  - 差距分析提示词：设计信息完整度分析提示词
  - 缺失点识别：引导大模型识别关键缺失信息
  - 完整度评估：获取模型对信息完整度的量化评估
- **评估维度**：
  - 完整度：已收集/所需信息比例
  - 清晰度：信息的明确程度
  - 一致性：信息间的逻辑一致性
- **输入参数**：关注点列表、已收集的实体和关系
- **输出参数**：信息差距报告（缺失关注点、完整度评分）
- **分析策略**：
  - 使用领域知识增强提示词，引导大模型基于专业知识评估
  - 设计结构化输出格式，确保差距分析结果可直接应用

### 3.2 问题生成器

- **功能描述**：利用大模型生成针对性问题获取缺失信息
- **处理逻辑**：
  - 问题生成提示词：设计引导生成精准问题的提示词
  - 问题优化指导：引导大模型优化问题表述
  - 问题多样化：指导生成不同类型的问题
- **问题类型**：
  - 开放性问题：获取广泛信息
  - 封闭性问题：确认特定信息
  - 假设性问题：验证推测信息
  - 引导性问题：引导用户思考特定方向
- **输入参数**：信息差距报告、关注点列表
- **输出参数**：问题列表（问题 ID、内容、类型、目标关注点）
- **质量控制**：
  - 使用问题评估提示词，让大模型自评问题质量
  - 设计问题改进循环，不断优化问题表述

### 3.3 交互形式选择器

- **功能描述**：利用大模型选择最适合的交互方式获取信息
- **处理逻辑**：
  - 交互形式推荐：引导大模型推荐最佳交互形式
  - 用户特征分析：基于用户历史交互分析用户偏好
  - 交互效率预估：预估不同交互形式的信息获取效率
- **交互形式**：
  - 问答交替：标准一问一答形式
  - 引导式选择：提供选项供用户选择
  - 确认式提问：请求确认推测信息
  - 批量信息收集：一次提供多个相关简单问题
- **输入参数**：问题列表、用户交互历史
- **输出参数**：交互计划（问题 ID、交互形式、呈现顺序）
- **个性化优化**：
  - 使用用户历史交互数据增强提示词，提高交互形式选择精准度
  - 设计适应性策略，根据用户反馈动态调整交互形式

### 3.4 用户响应处理器

- **功能描述**：利用大模型处理各种类型的用户响应
- **处理逻辑**：
  - 响应分析提示词：设计用于分析用户回答的提示词
  - 信息提取指导：引导大模型从回答中提取关键信息
  - 特殊响应处理策略：指导处理不确定、简短等特殊响应
- **处理策略**：
  - 不确定响应：提供教育性解释和例子
  - 过于简短响应：使用开放性问题鼓励详细说明
  - 过于冗长响应：使用摘要提取关键点
  - 偏离主题响应：礼貌引导回到主题
- **输入参数**：用户响应文本、相关问题信息
- **输出参数**：处理结果（提取信息、处理策略、后续行动）
- **理解增强**：
  - 使用上下文增强提示词，提高响应理解准确度
  - 设计多轮提取策略，确保关键信息全面提取

### 3.5 进度跟踪器

- **功能描述**：跟踪信息采集进度并提供反馈
- **处理逻辑**：
  - 进度评估提示词：引导大模型评估完成进度
  - 时间预估指导：获取大模型对剩余时间的预估
  - 进度反馈生成：生成用户友好的进度反馈
- **反馈形式**：
  - 进度条：可视化完成百分比
  - 阶段标记：标明当前处于哪个信息采集阶段
  - 剩余时间：预估完成剩余采集的时间
- **输入参数**：信息差距报告、已处理问题数
- **输出参数**：进度报告（完成百分比、当前阶段、预估剩余时间）
- **用户体验优化**：
  - 设计鼓励性提示词，增强用户完成动力
  - 使用里程碑设计，让用户感知明确进展

## 4. 文档生成与迭代优化模块

### 4.1 文档结构生成器

- **功能描述**：利用大模型创建符合行业标准的文档结构
- **处理逻辑**：
  - 结构设计提示词：引导大模型设计文档结构
  - 领域适应性指导：基于领域调整文档结构
  - 结构优化建议：获取结构优化建议
- **文档结构**：
  - 执行摘要：项目概述和关键点
  - 需求背景：项目背景和目标
  - 功能需求：详细功能描述
  - 非功能需求：性能、安全等要求
  - 约束条件：时间、资源等限制
  - 附录：术语表、参考资料等
- **输入参数**：领域信息、关注点列表
- **输出参数**：文档结构框架（章节列表、层次关系）
- **模板整合**：
  - 提供行业标准模板作为提示词参考
  - 设计结构验证提示词，确保结构符合行业最佳实践

### 4.2 内容填充引擎

- **功能描述**：利用大模型根据采集信息填充文档内容
- **处理逻辑**：
  - 内容生成提示词：设计引导生成专业内容的提示词
  - 信息整合指导：引导大模型整合已收集信息
  - 一致性检查引导：指导大模型检查内容一致性
- **文本生成特性**：
  - 专业术语使用：根据领域使用适当术语
  - 层次化描述：从概述到细节的层次描述
  - 精确表达：使用精确而非模糊的语言
- **输入参数**：文档结构框架、采集的需求信息
- **输出参数**：填充后的文档内容（章节 ID、内容文本）
- **质量控制**：
  - 使用专业性评估提示词，确保内容专业水平
  - 设计内容审核循环，优化内容质量

### 4.3 不确定性标记器

- **功能描述**：利用大模型标识文档中的不确定或缺失内容
- **处理逻辑**：
  - 不确定性识别提示词：引导识别低置信度内容
  - 缺失点标记指导：指导标记重要缺失信息
  - 冲突检测引导：引导检测信息冲突点
- **标记方式**：
  - 高亮标记：使用颜色高亮不确定内容
  - 注释标记：添加说明注释
  - 问题标记：将不确定内容转化为问题
- **输入参数**：填充后的文档内容、信息置信度数据
- **输出参数**：标记后的文档（不确定内容位置、类型、说明）
- **审核增强**：
  - 设计多角度审核提示词，从不同视角检查不确定性
  - 使用反向质疑策略，主动挑战文档中的可疑断言

### 4.4 反馈收集器

- **功能描述**：收集用户对文档的结构化反馈
- **处理逻辑**：
  - 反馈提示设计：设计用户友好的反馈收集提示
  - 反馈解析：解析用户提供的反馈内容
  - 反馈分类：将反馈分类为不同维度和严重程度
- **反馈结构**：
  - 量化评分：1-5 分评估准确性和完整性
  - 评论文本：文字说明不满意原因
  - 修改建议：用户直接提供的修改建议
- **输入参数**：标记后的文档、用户反馈输入
- **输出参数**：反馈报告（章节 ID、评分、评论、修改建议）
- **用户体验优化**：
  - 设计简洁的反馈表单减少用户负担
  - 使用大模型生成个性化感谢信息增强用户参与感

### 4.5 智能修改引擎

- **功能描述**：利用大模型基于反馈智能修改文档
- **处理逻辑**：
  - 修改指令生成：基于反馈生成修改指令
  - 内容重写引导：引导大模型重写有问题内容
  - 改进验证：验证修改是否解决了反馈问题
- **修改优先级**：
  - 最高优先级：评分低于 3 分的部分
  - 次高优先级：有具体修改建议的部分
  - 一般优先级：评分 3-4 分的部分
- **输入参数**：反馈报告、原文档内容
- **输出参数**：修改计划（修改位置、修改内容、修改理由）
- **迭代改进**：
  - 设计比较提示词，引导大模型比较原内容与修改内容
  - 使用修改理由生成，为每处修改提供合理解释

### 4.6 版本管理器

- **功能描述**：管理文档的不同版本
- **处理逻辑**：
  - 版本保存：保存每次修改后的文档版本
  - 差异提取：利用大模型提取版本间的关键差异
  - 变更摘要生成：生成版本变更的摘要说明
- **版本控制功能**：
  - 历史版本存储：保存最多 5 个历史版本
  - 版本比较：可视化展示版本差异
  - 最佳元素组合：允许从不同版本选择最佳部分
- **输入参数**：当前文档、修改后文档
- **输出参数**：版本信息（版本 ID、创建时间、变更摘要、完整文档）
- **效率优化**：
  - 仅存储关键差异减少存储开销
  - 使用大模型生成高质量变更摘要便于理解版本区别

## 5. 系统集成与控制模块

### 5.1 工作流控制器

- **功能描述**：协调各模块工作，管理整体流程
- **处理逻辑**：
  - 流程状态管理：跟踪当前处于哪个业务流程
  - 模块调度：调用相应模块执行任务
  - 异常处理：处理各模块可能出现的异常
- **流程控制机制**：
  - 状态机：使用状态机管理流程状态转换
  - 事件驱动：响应用户操作和系统事件
  - 决策点管理：处理各流程中的决策点
- **输入参数**：用户操作、系统状态
- **输出参数**：下一步行动计划（模块调用、参数传递）
- **大模型集成**：
  - 使用大模型作为决策辅助，处理复杂分支选择
  - 设计流程监控提示词，由大模型监控流程健康状态

### 5.2 会话上下文管理器

- **功能描述**：维护整个交互过程的上下文信息
- **处理逻辑**：
  - 上下文存储：存储交互历史和关键信息
  - 上下文检索：根据需要检索历史信息
  - 上下文压缩：使用大模型压缩冗长历史减少 token 消耗
- **上下文内容**：
  - 会话历史：用户输入和系统回应
  - 提取实体：已识别的关键实体
  - 确认信息：用户已确认的信息
  - 临时状态：当前处理状态
- **输入参数**：交互事件、需要存储的信息
- **输出参数**：上下文信息（历史记录、当前状态）
- **Token 优化**：
  - 设计上下文摘要策略，使用大模型提取历史交互精华
  - 实现分层上下文，根据重要性选择性传递历史信息

### 5.3 API 调用优化管理器

- **功能描述**：优化大模型 API 调用，控制成本和性能
- **处理逻辑**：
  - 请求合并：合并可并行处理的多个请求
  - 缓存管理：管理常见请求响应的缓存
  - 成本监控：监控 API 调用成本和配额使用情况
- **优化策略**：
  - 批处理请求：将多个小请求合并为批量请求
  - 分级模型调用：根据任务复杂度选择不同能力的模型
  - 提示词压缩：优化提示词减少 token 消耗
- **输入参数**：API 请求队列、系统状态
- **输出参数**：优化后的请求计划（请求顺序、合并策略）
- **自适应调整**：
  - 实时学习请求模式，动态优化调用策略
  - 设计回退机制，在 API 限流时降级处理

### 5.4 多模型协作管理器

- **功能描述**：管理和协调不同大模型的协作
- **处理逻辑**：
  - 模型能力评估：评估不同大模型的擅长领域
  - 任务分配：根据任务特性分配给适合的模型
  - 结果整合：整合多个模型的输出结果
- **协作模式**：
  - 串行处理：一个模型的输出作为另一个模型的输入
  - 并行处理：多个模型同时处理相同任务并比较结果
  - 专家模式：针对特定领域任务使用专门模型
- **输入参数**：任务描述、可用模型列表
- **输出参数**：任务分配计划（任务 ID、分配模型、处理顺序）
- **容错机制**：
  - 设计模型不可用时的替代策略
  - 实现结果一致性检查，处理模型间输出冲突

以上功能模块全部基于现有大模型能力实现，无需模型训练，重点通过提示词工程、API 集成和结果处理实现系统功能。
