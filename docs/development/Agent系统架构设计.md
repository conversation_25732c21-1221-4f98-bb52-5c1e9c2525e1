# 🤖 Agent系统架构设计文档

> **文档版本**: v1.0  
> **创建日期**: 2025-06-16  
> **适用范围**: Agent系统设计、交互流程、扩展指南  

## 📋 目录

- [1. Agent系统概述](#1-agent系统概述)
- [2. 核心Agent详解](#2-核心agent详解)
- [3. Agent交互流程](#3-agent交互流程)
- [4. 配置管理机制](#4-配置管理机制)
- [5. 扩展开发指南](#5-扩展开发指南)

---

## 1. Agent系统概述

### 🎯 设计理念

本项目采用**Agent-Based Architecture**，将复杂的需求采集任务分解为多个专门化的智能代理，每个Agent负责特定的功能领域，通过协作完成整体任务。

### 🏗️ 架构原则

- **单一职责**: 每个Agent专注于特定功能
- **松耦合**: Agent间通过标准接口通信
- **可扩展**: 支持动态添加新Agent
- **配置驱动**: 通过配置文件管理Agent行为

### 📊 Agent分类体系

```mermaid
graph TD
    A[Agent系统] --> B[输入处理层]
    A --> C[核心处理层]
    A --> D[输出生成层]
    A --> E[支撑服务层]
    
    B --> B1[领域分类器]
    B --> B2[意图识别器]
    B --> B3[类别分类器]
    
    C --> C1[对话流程管理器]
    C --> C2[信息提取器]
    C --> C3[通用意图处理器]
    
    D --> D1[文档生成器]
    D --> D2[审核优化器]
    D --> D3[问题润色器]
    
    E --> E1[知识库管理]
    E --> E2[用户交互处理]
    E --> E3[LLM服务]
```

---

## 2. 核心Agent详解

### 🔍 输入处理层Agent

#### DomainClassifierAgent (领域分类器)
```python
职责: 识别用户输入所属的业务领域
输入: 用户原始文本
输出: 领域分类结果 + 置信度
模型: qwen-plus (高速度、低成本)
```

**核心功能:**
- 多领域文本分类
- 置信度评估
- 领域知识库集成

#### IntentRecognitionAgent (意图识别器)
```python
职责: 分析用户的具体意图和需求类型
输入: 用户文本 + 领域信息
输出: 意图类型 + 关键信息
模型: qwen-plus
```

**支持的意图类型:**
- 需求描述
- 问题咨询
- 功能查询
- 帮助请求

#### CategoryClassifierAgent (类别分类器)
```python
职责: 对需求进行细分类别标注
输入: 文本 + 领域 + 意图
输出: 具体类别标签
模型: qwen-plus
```

### ⚙️ 核心处理层Agent

#### ConversationFlowAgent (对话流程管理器)
```python
职责: 控制整体对话流程和状态管理
输入: 所有Agent的输出结果
输出: 下一步行动指令
模型: qwen-max (强逻辑推理能力)
```

**状态管理:**
```python
class ConversationState(Enum):
    IDLE = auto()              # 空闲状态
    PROCESSING_INTENT = auto() # 处理意图
    COLLECTING_INFO = auto()   # 收集信息
    DOCUMENTING = auto()       # 文档生成
    COMPLETED = auto()         # 完成状态
```

#### InformationExtractorAgent (信息提取器)
```python
职责: 从对话中提取结构化信息
输入: 对话历史 + 提取模板
输出: 结构化需求信息
模型: doubao-pro-32k
```

**提取信息类型:**
- 功能需求
- 非功能需求
- 约束条件
- 优先级信息

#### GeneralIntentAgent (通用意图处理器)
```python
职责: 处理通用对话意图和上下文理解
输入: 用户输入 + 对话上下文
输出: 意图理解结果
模型: qwen-plus
```

### 📝 输出生成层Agent

#### DocumentGenerator (文档生成器)
```python
职责: 生成最终的需求文档
输入: 结构化需求信息
输出: 格式化需求文档
模型: qwen-max (高质量生成)
```

**文档模板支持:**
- 功能需求规格书
- 用户故事文档
- 技术需求文档
- 验收标准文档

#### ReviewAndRefineAgent (审核优化器)
```python
职责: 审查和优化生成的内容
输入: 初始文档 + 质量标准
输出: 优化后的文档
模型: openrouter-gemini-flash
```

#### QuestionPolisherAgent (问题润色器)
```python
职责: 优化和改进问题表述
输入: 原始问题
输出: 润色后的问题
模型: doubao-pro-32k
```

### 🛠️ 支撑服务层Agent

#### KnowledgeBaseAgent (知识库管理)
```python
职责: 管理和访问系统知识库
功能: 知识检索、更新、维护
数据源: SQLite数据库
```

#### UserInteractionAgent (用户交互处理)
```python
职责: 处理与用户的直接交互
功能: 会话管理、状态跟踪
模型: doubao-pro-32k
```

#### LLMServiceAgent (LLM服务)
```python
职责: 统一的LLM调用服务
功能: 模型选择、调用管理、缓存
支持: 多种LLM提供商
```

---

## 3. Agent交互流程

### 🔄 标准处理流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant API as API网关
    participant CF as 对话流程管理器
    participant DC as 领域分类器
    participant IR as 意图识别器
    participant IE as 信息提取器
    participant DG as 文档生成器
    participant RR as 审核优化器
    
    User->>API: 发送消息
    API->>CF: 转发消息
    CF->>DC: 领域分类
    DC-->>CF: 分类结果
    CF->>IR: 意图识别
    IR-->>CF: 意图结果
    CF->>IE: 信息提取
    IE-->>CF: 结构化信息
    CF->>DG: 生成文档
    DG-->>CF: 初始文档
    CF->>RR: 审核优化
    RR-->>CF: 最终文档
    CF-->>API: 返回结果
    API-->>User: 响应用户
```

### 📋 消息格式标准

#### 标准输入消息
```json
{
    "content": "用户输入内容",
    "context": {
        "session_id": "会话ID",
        "user_id": "用户ID",
        "timestamp": "时间戳"
    },
    "type": "user_message",
    "sender": "user"
}
```

#### 标准输出响应
```json
{
    "status": "success|error",
    "content": "响应内容",
    "context": {
        "session_id": "会话ID",
        "current_state": "当前状态",
        "next_action": "下一步行动"
    },
    "metadata": {
        "agent_name": "处理Agent",
        "model_used": "使用的模型",
        "confidence": "置信度"
    }
}
```

### 🎯 决策流程

#### 意图决策树
```
用户输入
├── 是否为需求描述？
│   ├── 是 → 启动需求收集流程
│   └── 否 → 继续分析
├── 是否为问题咨询？
│   ├── 是 → 启动知识库查询
│   └── 否 → 继续分析
├── 是否为功能查询？
│   ├── 是 → 返回功能说明
│   └── 否 → 通用对话处理
```

---

## 4. 配置管理机制

### ⚙️ Agent-Model映射配置

```yaml
# 场景-模型映射关系
scenario_llm_mapping:
  # 分类任务 - 高速度低成本
  domain_classifier: "qwen-plus"
  category_classifier: "qwen-plus"
  intent_recognition: "qwen-plus"
  
  # 理解任务 - 平衡性能和成本
  information_extractor: "doubao-pro-32k"
  question_polisher: "doubao-pro-32k"
  user_interaction: "doubao-pro-32k"
  
  # 核心任务 - 高性能
  conversation_flow: "qwen-max"
  document_generator: "qwen-max"
  
  # 审核任务 - 专业模型
  review_and_refine: "openrouter-gemini-flash"
```

### 🔧 Agent配置参数

```python
# Agent通用配置
AGENT_CONFIG = {
    "max_retries": 3,           # 最大重试次数
    "timeout": 30,              # 超时时间(秒)
    "cache_enabled": True,      # 是否启用缓存
    "cache_ttl": 3600,         # 缓存过期时间
    "log_level": "INFO",       # 日志级别
    "enable_metrics": True     # 是否启用指标收集
}

# 特定Agent配置
DOMAIN_CLASSIFIER_CONFIG = {
    "confidence_threshold": 0.8,  # 置信度阈值
    "max_categories": 5,          # 最大分类数
    "enable_fallback": True       # 启用回退机制
}
```

### 📊 性能优化配置

```json
{
    "performance_optimization": {
        "enable_caching": true,
        "cache_size": 1000,
        "batch_processing": true,
        "max_batch_size": 10,
        "async_processing": true,
        "connection_pool_size": 20
    }
}
```

---

## 5. 扩展开发指南

### 🆕 添加新Agent步骤

#### 1. 创建Agent类
```python
from backend.agents.base import AutoGenBaseAgent

class NewCustomAgent(AutoGenBaseAgent):
    def __init__(self, name="NewCustomAgent", **kwargs):
        super().__init__(name=name, **kwargs)
        self.agent_type = "custom_agent"
    
    async def process_message(self, message: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        # 实现具体的处理逻辑
        pass
```

#### 2. 注册Agent配置
```python
# 在 backend/config/settings.py 中添加
AGENT_TYPES["CUSTOM_AGENT"] = "custom_agent"
AGENT_NAMES["custom_agent"] = "NewCustomAgent"
SCENARIO_LLM_MAPPING["custom_agent"] = "qwen-plus"
```

#### 3. 集成到对话流程
```python
# 在 ConversationFlowAgent 中添加调用逻辑
async def handle_custom_scenario(self, message):
    custom_agent = self.get_agent("custom_agent")
    result = await custom_agent.process_message(message)
    return result
```

### 🔌 Agent接口规范

#### 必须实现的方法
```python
class AgentInterface:
    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理消息的核心方法"""
        raise NotImplementedError
    
    def get_agent_info(self) -> Dict[str, Any]:
        """返回Agent基本信息"""
        return {
            "name": self.name,
            "type": self.agent_type,
            "version": "1.0.0",
            "capabilities": []
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        return True
```

### 📈 性能监控集成

```python
from backend.utils.performance_monitor import monitor_performance

class MonitoredAgent(AutoGenBaseAgent):
    @monitor_performance
    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        # Agent处理逻辑
        pass
```

### 🧪 测试规范

```python
import pytest
from backend.agents.your_agent import YourAgent

class TestYourAgent:
    @pytest.fixture
    def agent(self):
        return YourAgent()
    
    @pytest.mark.asyncio
    async def test_process_message(self, agent):
        message = {"content": "test message"}
        result = await agent.process_message(message)
        assert result["status"] == "success"
    
    def test_agent_info(self, agent):
        info = agent.get_agent_info()
        assert "name" in info
        assert "type" in info
```

---

## 📝 最佳实践

### ✅ 开发建议

1. **遵循单一职责原则**: 每个Agent只负责一个明确的功能
2. **实现标准接口**: 确保Agent间可以无缝协作
3. **添加完整测试**: 包括单元测试和集成测试
4. **编写详细文档**: 说明Agent的功能、输入输出格式
5. **性能优化**: 合理使用缓存和异步处理

### ⚠️ 注意事项

1. **避免循环依赖**: Agent间不应形成循环调用
2. **错误处理**: 实现完善的异常处理机制
3. **资源管理**: 及时释放不需要的资源
4. **日志记录**: 记录关键操作和错误信息
5. **配置管理**: 避免硬编码，使用配置文件

---

*本文档将随着系统发展持续更新和完善。*
