
# 由己AI需求采集项目文档

## 1. 项目概述

### 1.1 项目目标
构建一个基于AI的智能需求采集系统，通过两阶段AI架构实现高效、准确的需求获取和文档生成。

### 1.2 技术栈
- 后端：Python + FastAPI
- 前端：TypeScript + React + Vite
- AI集成：OpenAI API
- 数据存储：SQLite (sessions.db)
- 文档格式：Markdown
- 部署：Docker + Nginx

## 2. 系统架构

### 2.1 核心模块架构
```
backend/
├── api/            # API接口层
├── core/           # 核心业务逻辑
├── config/         # 配置管理
├── data/          # 数据存储
├── models/        # 数据模型
├── tests/         # 测试用例
└── utils/         # 工具函数
```

### 2.2 核心模块说明

#### 2.2.1 领域分类模块 (domain_classifier.py)
- 职责：
  - 领域知识库管理
  - 分类提示词构建
  - 调用LLM进行分类
  - 结果解析与置信度评估
- 关键功能：
  - `classify_domain()`: 领域分类主函数
  - `get_domain_details()`: 获取领域详细信息
  - `load_knowledge_base()`: 加载知识库

#### 2.2.2 需求采集模块 (collection_ai.py)
- 职责：
  - 关注点管理
  - 提示词构建
  - 问题生成
  - 值提取
- 关键功能：
  - `generate_collection_question()`: 生成采集问题
  - `extract_focus_point_value()`: 提取关注点值
  - `get_focus_points_by_domain_and_category()`: 获取关注点列表

#### 2.2.3 意图识别模块 (intent_recognition.py)
- 职责：
  - 用户意图解析
  - 修改意图识别
  - 关注点值提取
- 关键功能：
  - `parse_user_feedback()`: 解析用户反馈
  - `IntentProxy`: 意图处理代理
  - 意图中间件实现

#### 2.2.4 会话管理模块 (session_manager.py)
- 职责：
  - 会话状态管理
  - 数据持久化
  - 状态转换控制
- 关键功能：
  - `get_session_state()`: 获取会话状态
  - `update_session_state()`: 更新会话状态
  - `load/save_session_data()`: 数据持久化

### 2.3 工作流程

```mermaid
graph TD
    A[用户输入] --> B[领域分类]
    B --> C{分类结果}
    C -->|确定| D[类别识别]
    C -->|不确定| E[追加提问]
    D --> F[关注点采集]
    F --> G{是否完成}
    G -->|否| F
    G -->|是| H[生成文档]
    H --> I[用户确认]
    I -->|需要修改| F
    I -->|确认| J[完成]
```

## 3. 关键特性

### 3.1 两阶段AI架构
1. 分类AI：领域与类别识别
2. 采集AI：需求信息获取

### 3.2 智能状态管理
- 多维度会话状态
- 动态优先级调整
- 智能冲突解决

### 3.3 错误处理机制
- 多级降级策略
- 详细日志记录
- 异常恢复机制

## 4. 配置系统

### 4.1 核心配置文件
```
config/
├── config.py           # 主配置
├── focus_points.json   # 关注点配置
├── intent_thresholds.json  # 意图阈值
└── conflict_rules.json    # 冲突规则
```

### 4.2 知识库结构
```
data/
├── knowledge_base.json     # 领域知识库
├── intent_examples.json    # 意图示例
└── focus_point_mapping.json # 关注点映射
```

## 5. 测试体系

### 5.1 测试类型
- 单元测试
- 集成测试
- 端到端测试
- 性能测试

### 5.2 测试覆盖
- 领域分类准确性
- 意图识别有效性
- 状态管理可靠性
- 采集流程完整性

## 6. 部署架构

### 6.1 开发环境
- Python 3.11+
- Node.js 16+
- SQLite 3

### 6.2 生产环境
- Docker容器化
- Nginx反向代理
- 日志聚合系统

## 7. 监控与维护

### 7.1 日志系统
- API调用日志
- 错误追踪
- 性能监控

### 7.2 系统维护
- 知识库更新
- 模型调优
- 配置管理

## 8. 未来规划

### 8.1 功能增强
- 多语言支持
- 自定义模板
- 批量处理能力

### 8.2 性能优化
- 响应时间优化
- 资源使用优化
- 成本控制策略

## 9. 最佳实践

### 9.1 开发规范
- 代码风格指南
- 提交规范
- 文档规范

### 9.2 运维指南
- 部署流程
- 监控策略
- 备份恢复

这个文档结构清晰地展示了项目的各个方面，便于理解和维护。每个部分都可以根据需要进行进一步展开和细化。你觉得这个结构是否符合你的需求？或者需要我对某个部分进行更详细的说明？