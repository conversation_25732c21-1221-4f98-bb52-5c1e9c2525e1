# 文档生成器模版修复总结

## 问题描述

从日志中发现文档生成器没有输出替换占位符后的模版内容，这导致无法看到实际发送给LLM的提示词，影响调试和优化。

## 根本原因

### 1. **未使用统一的PromptLoader**
文档生成器 (`DocumentGenerator`) 有自己的模版加载逻辑：
- 使用 `_get_system_prompt()` 方法直接读取文件
- 没有使用 `PromptLoader` 进行占位符替换
- 没有集成到统一的模版日志系统中

### 2. **模版加载方式不一致**
```python
# 原来的方式 - 直接文件读取
def _get_system_prompt(self) -> str:
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    return self._extract_prompt_from_content(content)

# 其他Agent使用的方式 - PromptLoader
prompt = prompt_loader.load_prompt("template_name", variables)
```

### 3. **缺少占位符替换**
文档生成器的模版 `document_template.md` 包含占位符 `{user_focused_info}`，但没有被正确替换。

## 修复方案

### ✅ **1. 统一使用PromptLoader**

修改 `backend/agents/document_generator.py` 中的 `_call_llm` 方法：

```python
# 修复前
def _call_llm(self, user_info: Dict[str, Any], conversation_id: str):
    response = await self.llm_client.call_llm(
        messages=[
            {"role": "system", "content": self._get_system_prompt()},  # 直接读取
            {"role": "user", "content": f"用户提供的信息:\n{json.dumps(user_info, ensure_ascii=False)}"}
        ],
        # ...
    )

# 修复后  
def _call_llm(self, user_info: Dict[str, Any], conversation_id: str):
    # 使用PromptLoader加载模版并替换占位符
    system_prompt = self.prompt_loader.load_prompt(
        "document_template",
        {"user_focused_info": json.dumps(user_info, ensure_ascii=False)}
    )
    
    response = await self.llm_client.call_llm(
        messages=[
            {"role": "system", "content": system_prompt},  # 使用PromptLoader
            {"role": "user", "content": f"用户提供的信息:\n{json.dumps(user_info, ensure_ascii=False)}"}
        ],
        # ...
    )
```

### ✅ **2. 移除冗余代码**

删除了不再需要的方法：
- `_get_system_prompt()` - 直接文件读取
- `_extract_prompt_from_content()` - 手动内容提取  
- `_get_default_prompt()` - 硬编码默认提示词

### ✅ **3. 集成到统一日志系统**

现在文档生成器会自动使用全局的模版日志配置：
- 当 `template_logging_enabled=True` 时输出模版内容
- 当 `template_logging_enabled=False` 时不输出模版内容
- 与其他Agent保持一致的行为

## 修复效果

### ✅ **模版内容正确输出**

现在可以在日志中看到完整的模版内容：

```
[模板内容] document_template 替换占位符后的完整内容:
--------------------------------------------------
# 极简输入信息润色与提炼提示词 (适应固定输出格式)

## 任务描述
你是一位信息架构师和内容润色专家...

## 输入信息 (用户提供)
{"project_name": "需求文档", "date": "2025-06-18", "requirements": "### 为小美医生策划十周年庆典上播放的纪录片拍摄脚本..."}

## 输出格式
你必须以JSON格式输出润色和提炼后的信息...
--------------------------------------------------
```

### ✅ **占位符正确替换**

- ✅ `{user_focused_info}` 被替换为实际的用户信息JSON
- ✅ 用户信息正确嵌入到模版中
- ✅ 模版结构完整保留

### ✅ **统一的配置管理**

- ✅ 支持全局模版日志开关
- ✅ 与其他Agent行为一致
- ✅ 便于调试和优化

## 验证结果

测试结果显示修复成功：

```
✅ 占位符已正确替换
✅ 用户信息已正确嵌入模版
✅ 模版内容完整输出
✅ 集成到统一日志系统
```

## 预期改进

1. **调试便利性**: 可以看到发送给LLM的实际提示词
2. **问题排查**: 当LLM输出异常时，可以检查提示词是否正确
3. **模版优化**: 可以根据实际输出调整模版内容
4. **一致性**: 所有Agent使用相同的模版加载机制
5. **可配置性**: 支持全局开启/关闭模版日志输出

## 相关文件

- `backend/agents/document_generator.py` - 文档生成器主文件
- `backend/prompts/document_template.md` - 文档生成模版
- `backend/utils/prompt_loader.py` - 统一模版加载器
- `backend/utils/logging_config.py` - 模版日志配置
- `docs/development/文档生成器模版修复总结.md` - 本文档

## 最佳实践

### 🎯 **模版使用原则**

1. **统一使用PromptLoader**: 所有Agent都应该使用PromptLoader加载模版
2. **避免硬编码**: 不要在代码中硬编码提示词内容
3. **占位符命名**: 使用清晰、描述性的占位符名称
4. **模版文档化**: 在模版文件中添加清晰的说明和示例

### 🔧 **开发建议**

- 开发时启用模版日志: `configure_logging(template_logging_enabled=True)`
- 生产环境关闭模版日志: `configure_logging(template_logging_enabled=False)`
- 定期检查模版内容是否符合预期
- 使用版本控制跟踪模版变更
