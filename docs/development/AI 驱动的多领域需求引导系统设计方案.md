## 一、背景与目标

雇主在发布需求时，常因缺乏专业表达能力而导致需求描述模糊不清，使自由职业者难以理解和完成任务。本系统旨在通过 AI 辅助，引导雇主清晰、全面地表达需求，形成专业的需求文档。**本方案旨在提供一个平台无关的通用设计，并强调知识管理的最佳实践。**

## 二、系统架构与流程

### 2.1 两阶段 AI 架构

采用两阶段 AI 处理流程，利用大型语言模型（LLM）的能力，通过不同的提示词和角色指令处理各个阶段：

1.  **领域分类 AI (分类 AI - Domain Classification AI):**
    *   **职责:** 专门负责对用户初始输入进行领域分类。
    *   **输入:** 用户原始需求描述。
    *   **输出:** 领域分类结果 (例如：`{"domain": "平面设计", "confidence": 0.95}`)。
    *   **实现:** 通过调用 **LLM API** 实现。
    *   **关键：Prompt 设计:** 成功的分类严重依赖于精心设计的 Prompt。该 Prompt **必须** 包含：
        *   明确的指令。
        *   **预定义的领域列表及描述:** 这是分类的核心依据。**（最佳实践：此列表应从外部结构化知识源管理，并在构建 Prompt 时动态注入，以提高可维护性。）** 例如：
            *   `1. 平面设计（涉及Logo/海报/包装等视觉创作）`
            *   `2. 软件开发（涉及APP/网站/系统开发）`
            *   ...
        *   用户输入占位符。
        *   输出格式要求。

2.  **领域内类别识别/细化 Agent:**
    *   **职责:** 在确定主领域后，进一步识别用户需求的具体类别（如“平面设计”下的“海报设计”）。
    *   **输入:** 用户原始需求描述、已识别的主领域。
    *   **输出:** 具体类别名称 (例如: "海报设计") 及可能提取的初步信息。
    *   **实现:** 通过调用 **LLM API** 实现。
    *   **关键：动态 Prompt 构建:** 其 Prompt 需要动态构建，包含：
        *   明确的指令（识别领域内的具体类别）。
        *   **从外部知识源查询到的、该主领域下的所有可能类别及其描述。**
        *   用户原始输入。
        *   输出格式要求。

3.  **采集 AI (采集 AI - Collection AI):**
    *   **职责:** 在特定领域和类别内，基于关注点引导用户完善需求细节。
    *   **输入:** 用户原始需求描述、领域、类别、对话历史、以及后续用户输入。
    *   **核心机制:**
        *   **知识库查找:** 根据接收到的领域和类别信息，从**外部结构化知识源 (如数据库)** 中**查询**相关的“关注点”列表（包含描述、优先级等）。
        *   **动态 Prompt 构建:** **使用从知识源查询到的**“关注点”列表、对话历史、用户输入、角色定义等，动态构建用于调用 LLM 的 Prompt。
        *   **对话生成:** 基于构建好的 Prompt，通过调用 **LLM API** 生成提问或引导话术。
    *   **输出:** 引导用户的提问、最终整理的需求文档。
    *   **实现:** 主要通过调用 **LLM API** 实现，并结合 **工作流引擎** 的逻辑进行状态管理和流程控制。

### 2.2 系统处理流程

```mermaid
graph TD
    A[用户输入初始需求] --> B(领域分类 AI);
    B -- 输出领域分类结果 --> C{工作流路由逻辑};
    C -- 路由到领域分支 --> D_Query(查询领域内类别信息);
    D_Query -- 从知识源获取 --> D(领域内类别识别/细化 Agent);
    D -- 输入: 用户输入 + 类别信息 --> E{输出具体类别及初步信息};
    E -- 类别信息 --> F_Query(查询类别关注点);
    F_Query -- 从知识源获取 --> F(加载关注点列表);
    F --> G[采集 AI: 构建 Prompt];
    G -- 包含关注点列表, 历史, 用户输入, 类别信息 --> H((采集 AI: LLM 生成话术));
    H --> I[呈现给用户];
    I --> J[用户回复];
    J --> K{所有关注点已覆盖?};
    K -- 否 --> G;
    K -- 是 --> L[生成文档初稿];
    L --> M{客户确认};
    M -- 确认 --> N[结束];
    M -- 修改 --> J;

    subgraph "知识源 (数据库/配置)"
        style Knowledge fill:#eee,stroke:#333,stroke-dasharray: 5 5
        D_Query -- 查询 --> Knowledge[领域/类别/关注点];
        F_Query -- 查询 --> Knowledge;
    end

    subgraph "领域处理分支 (例如: 平面设计)"
        D_Query
        D
        E
        F_Query
        F
        G
        H
        I
        J
        K
        L
        M
    end

    style B fill:#ccf,stroke:#333,stroke-width:2px
    style D fill:#fdb,stroke:#333,stroke-width:2px
    style H fill:#f9d,stroke:#333,stroke-width:2px
    style F fill:#lightgrey,stroke:#333,stroke-width:2px
    style C fill:#bdf,stroke:#333,stroke-width:2px
    style D_Query fill:#eef,stroke:#333,stroke-width:1px
    style F_Query fill:#eef,stroke:#333,stroke-width:1px
```

**流程说明:**

1.  用户输入初始需求。
2.  **分类 AI** (通过 LLM API 调用) 进行领域判断。
3.  **工作流引擎** 根据领域结果执行 **路由逻辑**，将流程导向对应分支。
4.  工作流引擎**查询知识源**，获取当前领域下的类别信息。
5.  **领域内类别识别/细化 Agent** 利用这些信息分析用户输入，确定具体类别。
6.  工作流引擎根据确定的类别，**再次查询知识源**，获取该类别对应的详细“关注点”列表。
7.  加载关注点列表。
8.  **采集 AI** 基于加载的关注点列表、对话历史等，**动态构建 Prompt**。
9.  通过 **LLM API 调用** 生成引导性提问。
10. 将问题呈现给用户 (通过用户界面)。
11. 接收用户回复。
12. 系统判断是否所有必要的 "关注点" 都已覆盖。
13. 若未覆盖，返回步骤 8 (采集 AI 构建 Prompt) 继续提问。
14. 若已覆盖，生成文档初稿。
15. 请求客户确认文档。
16. 根据客户反馈决定结束或返回继续采集/修改。

## 三、混合信息采集策略 (由采集 AI 执行)

### 3.1 信息来源

*   **被动提取 (初始分析阶段或贯穿全程):**
    *   利用 LLM 的自然语言理解能力，从用户输入中直接提取与 "关注点" 相关的信息。
*   **主动询问 (核心交互阶段):**
    *   当信息不足或需要澄清时，**采集 AI** 根据从**知识源查询并注入 Prompt** 的 "关注点" (特别是未覆盖的高优先级关注点) 生成问题。

### 3.2 询问逻辑

*   **优先级驱动:** 优先询问 P0、P1 级别的未覆盖关注点 (优先级信息从知识源获取)。
*   **上下文感知:** 结合对话历史，避免重复提问，确认已理解的信息。
*   **动态调整:** 根据用户回答的清晰度、专业度、耐心度，调整提问的深度和方式。
*   **类别聚焦:** 基于已确定的类别，围绕相关关注点进行提问。

## 四、会话式需求采集的用语和形式 (采集 AI 的 Prompt 设计关键)

### 4.1 角色扮演与语气

*   **设定专业角色:** 在调用 LLM 时，通过 Prompt 指令设定其角色。
*   **专业友好型:** 指导 LLM 使用专业但平易近人、用户易懂的语言。
*   **引导原则:** 在 Prompt 中明确告知 LLM 遵循的原则。

### 4.2 问题生成

*   **基于查询到的 "关注点":** 指导 LLM 将**从知识源获取并注入 Prompt** 的 "关注点" 描述转化为自然的提问。
*   **明确性:** 指导 LLM 每次聚焦 1-2 个关注点。
*   **渐进性:** 指导 LLM 从核心问题到细节问题提问。
*   **上下文关联:** 在 Prompt 中包含相关对话历史。

### 4.3 交互形式多样化

*   **直接提问:** 指导 LLM 生成直接的问题。
*   **引导式选择 (处理不确定性):** 指导 LLM 在用户不确定时提供选项（选项也可考虑从知识源获取或由 LLM 基于常识生成）。
*   **确认式提问:** 指导 LLM 基于已有信息生成确认性问题。
*   **提供示例 (处理不确定性):** 在 Prompt 中提供示例，或指导 LLM 基于**知识源中可能包含的示例/解释**来帮助用户。

### 4.4 特殊情况处理

*   **不确定回答:** 在 Prompt 中加入处理策略，指导 LLM 如何应对。
*   **模糊回答:** 指导 LLM 追问细节或提供具体方向。

### 4.5 会话流程控制

*   **进度感知:** **工作流引擎**需要维护一个状态，记录已覆盖的关注点，并在构建 Prompt 时将此状态信息传递给采集 AI。
*   **用户反馈:** 指导 LLM 在适当的时候告知用户当前进展或下一步重点。

## 五、迭代优化流程

### 5.1 初稿生成

*   当 **工作流引擎** 判断已覆盖所有必要 (如 P0, P1) 关注点后，触发文档生成逻辑，基于收集到的信息生成结构化的需求文档初稿 (Markdown 格式)。
*   **标记不确定性:** 对于用户未明确或 AI 推断的信息，进行标记。

### 5.2 客户确认与反馈

*   将文档呈现给客户，请求确认信息的准确性和完整性。
*   **结构化反馈 (可选):** 可设计评分或评论机制。

### 5.3 迭代修改

*   如果客户提出修改意见，**工作流引擎** 将流程导回 **采集 AI** 交互阶段，针对性地补充或修正信息。
*   更新文档，再次提交确认。

### 5.4 版本管理

*   系统应具备保存沟通过程中文档版本的能力。

## 六、实施与扩展建议

1.  **技术选型:**
    *   **LLM:** 选择合适的模型 API。
    *   **工作流引擎:** 选择适合业务流程编排的工具或框架。
    *   **知识源/知识库:** **强烈推荐使用数据库 (SQL/NoSQL)、结构化配置文件 (YAML/JSON) 或专用配置服务来存储和管理领域、类别、关注点及其属性。** 这确保了数据的准确性、一致性和可维护性，优于 RAG 或 Prompt 硬编码。
    *   **状态管理:** 需要机制来维护对话状态和已收集信息 (如 Redis, 数据库)。
2.  **知识库管理:** 需要建立便捷的流程和工具来维护外部知识源中的内容。
3.  **渐进式部署:** 先从核心领域开始实施。
4.  **持续学习:** 分析实际对话数据，优化知识库内容、Prompt 设计和工作流逻辑。
5.  **多语言/多模态:** 未来可扩展。

## 七、总结

本方案设计了一个平台无关的、采用 **两阶段 AI (分类 AI + 采集 AI)**、以**外部结构化知识源 (如数据库)** 存储的领域/类别/关注点为核心的智能需求引导系统。通过明确的领域分类、基于精确查询的知识进行结构化提问、以及专业的会话引导，旨在显著提高需求获取的效率、准确性和可维护性。
