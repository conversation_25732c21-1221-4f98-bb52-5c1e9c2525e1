# 领域需求采集v3

# 一.具体流程

1. **用户输入信息 (User Input):**  用户像之前一样，输入他们的需求描述。
2. **领域分类 AI (分类 AI - Domain Classification AI):**
   - **第一个大模型 (分类 AI)**  专门负责  **对用户输入的信息进行领域分类**。
   - 它的任务和之前方案的 "领域识别模块" 类似，判断用户需求属于哪个领域 (例如平面设计、软件开发等)。
   - 输出结果是  **领域分类** (例如 "平面设计")。
3. **领域分类传递 (Domain Transfer):** **分类 AI 的输出结果 (领域分类)**  会被  **传递给第二个大模型**。
4. **采集 AI (采集 AI - Collection AI):**
   - **第二个大模型 (采集 AI)**  接收到  **用户的原始输入信息**  和  **领域分类结果**。
   - **关键步骤： 知识库查找 (Knowledge Base Lookup)** - 采集 AI 会  **根据接收到的领域分类**， **在 "需求校准" 知识库中查找**  与该领域相关的  **"关注点" (Focus Points)**。
   - **"需求校准" 知识库**  就像是一个  **针对不同领域的结构化 checklist**， 里面预先定义了  **每个领域的需求采集应该关注的关键方面**。 例如，平面设计领域的关注点可能是 "设计类型"、"设计风格"、"使用场景" 等等。
5. **沟通方向 (Communication Direction):**
   - **采集 AI 将从知识库中查找到的 "关注点" 作为与用户沟通的方向和重点。**
   - 换句话说， **"关注点" 就变成了 采集 AI 需要向用户提问和引导用户补充信息的 结构化问题列表。**
   - 采集 AI 会根据这些 "关注点" **生成问题或提示语**， 引导用户提供更详细、更全面的需求信息。

# **需求校准 知识库的具体结构和内容是什么样的**

[需求校准知识库](https://www.notion.so/1aa5b091bca480759d52d9f0d7d8b98c?pvs=21)

# **分类 AI 和 采集 AI 的协同工作方式**

## 1.工作流程

1. 用户输入需求。
2. 领域分类 AI 接收用户输入， 进行领域分类， 并输出分类结果 (领域、置信度)。
3. 工作流系统 接收领域分类 AI 的输出结果， 并根据 领域分类结果 将 工作流 路由到 相应的领域分支。
4. (在每个领域分支内部) Coze 意图识别节点 接收 用户输入， 进行意图识别， 并输出 意图 ID 和 分类原因。
5. (在每个领域分支内部) 工作流系统 根据 意图 ID， 从 "需求校准" 知识库 中 获取 相应的 "关注点" 列表。
6. (在每个领域分支内部) 采集 AI 基于 "关注点" 生成 提问或引导话术， 并返回给用户， 引导用户补充需求信息。
7. 重复步骤 1-6， 直到用户需求被充分澄清。

## **2. 领域分类 AI (Domain Classification AI):**

- **实现方式 (Coze):**
  - **方案 A： 使用 Coze 平台的 Prompt Node 调用 大模型 (例如 Qwen) 实现领域分类。**
    - **优点：** 灵活性高， 可以自定义 Prompt， 并选择不同的 大模型。
    - **缺点：** 需要手动编写 Prompt， 并处理 API 调用和数据解析等细节。
  - **方案 B： 探索 Coze 平台 是否提供 现成的 领域分类模块 (如果存在)。**
    - **优点：** 简化开发流程， 提高开发效率。
    - **缺点：** 灵活性较低， 可能无法自定义 Prompt 或选择不同的 大模型。
  - **选择哪种方案取决于 Coze 平台 的具体功能和您的需求。** 如果 Coze 提供了 现成的 领域分类模块， 并且其性能和灵活性能够满足您的需求， 那么使用该模块是更 разумный 的选择。 否则， 使用 Prompt Node 调用 大模型 仍然是一个可行的方案。

### **1. 方案 A Prompt 设计 (如果选择 Prompt Node 方案):**

- Prompt 的核心目标是 **准确地识别用户需求所属的领域**。
- Prompt 可以包含以下信息：
  - **明确的指令：** 例如 "请判断用户需求所属的领域，并返回 JSON 格式的结果。"
  - **预定义的领域列表：** 例如 "平面设计、软件开发、UI/UX 设计、营销推广、法律咨询、其他"。
  - **领域描述 (可选)：** 为了帮助模型更好地理解领域， 可以为每个领域提供简要的描述。
  - **输出格式要求：** 明确要求模型返回 JSON 格式的结果， 包含 "domain" 字段 (表示领域名称) 和 "confidence" 字段 (表示置信度)。
- **示例 Prompt:**

```
请判断用户需求所属领域：
1. 平面设计（涉及Logo/海报/包装等视觉创作）
2. 软件开发（涉及APP/网站/系统开发）
3. UI/UX设计（涉及用户界面、交互流程、用户体验）
4. 营销推广（涉及推广策略/用户增长）
5. 法律咨询（涉及合同、法律条款、诉讼）
6. 其他（无法归类时）

请根据用户需求，选择最合适的领域。

返回JSON格式：
{
  "domain": "领域名称",
  "confidence": 置信度 (0-1)
}

用户需求： {{user_input}}

```

- **输出结果：**
  - 领域分类 AI 应该输出 JSON 格式的结果， 例如：
  ```json
  {
    "domain": "平面设计",
    "confidence": 0.95
  }
  ```

### **2. Coze 意图识别节点 (Coze Intent Recognition Node):**

- **配置：**

  - **模型选择：** 根据性能和成本选择合适的 大模型 (例如 Qwen)。
  - **输入：** 将 用户的原始输入 ({{user\_input}}) 传递给 意图识别节点。
  - **对话历史：** 开启 对话历史 功能， 并设置合适的 会话轮数， 以便模型 能够 结合上下文信息 进行 意图识别。
  - **意图匹配：**
    - **对于每个领域， 定义 相应的 意图分类选项 (意图列表)。**
    - **意图 的 数量 和 内容 取决于 该领域 的 "关注点" 的 数量 和 复杂程度。**
    - **每个 意图 应该 对应 一个 或 多个 "关注点"。**
  - **高级设置 (Prompt)：**
    - **编写 意图识别 Prompt， 指导 模型 更 精准地 识别 用户意图。**
    - **Prompt 的 核心目标 是 将 用户输入 映射到 预定义的 意图 列表。**
    - **Prompt 可以 包含 以下信息：**
      - 明确的指令： 例如 "请根据用户输入， 判断用户表达了哪个意图， 并返回对应的意图 ID。"
      - 意图列表： 列出所有 预定义的 意图， 并为每个意图 分配一个唯一的 ID。
      - 意图描述 (可选)： 为了帮助模型更好地理解意图， 可以为每个意图 提供简要的描述。
      - 用户输入示例 (可选)： 为每个意图 提供一些 用户输入 的示例， 帮助模型 学习 如何识别 不同的 表达方式。
      - 输出格式要求： 明确要求模型返回 意图 ID 和 分类原因。
    - **示例 Prompt (以 "平面设计" 领域 的 "LOGO 设计" 类别 为例):**

  ```
  请根据用户输入，判断用户表达了哪个意图，并返回对应的意图 ID 和分类原因。

  意图列表：
  1. 品牌名称意图 (用户表达或提及品牌名称)
  2. 品牌核心价值意图 (用户表达品牌核心价值或理念)
  3. 设计风格偏好意图 (用户表达对 LOGO 设计风格的偏好)
  4. 应用场景意图 (用户提及 LOGO 应用场景)
  5. 其他意图 (用户表达了其他与 LOGO 设计相关的信息，例如预算、截止日期等)
  6. 无效意图 (用户输入与 LOGO 设计无关)

  请根据用户输入，选择最合适的意图，并返回 JSON 格式的结果：
  {
    "classificationId": 意图ID (1-6),
    "reason": 分类原因
  }

  用户输入： {{user_input}}

  ```

- **输出结果：**
  - 意图识别节点 应该输出 JSON 格式的结果， 例如：
  ```json
  {
    "classificationId": 1,
    "reason": "用户提到了品牌名称 'Coffee Day'"
  }
  ```

## **3. 工作流路由 (Workflow Routing):**

**Coze 平台的 Condition Node 或其他 路由节点 应该 _监听 领域分类 AI 的输出结果_， 并 *根据 领域分类结果*  将 工作流 路由到 不同的领域分支。**

**例如：**

- 如果 `domain` 为 `"平面设计"`， 则将工作流路由到 "平面设计" 领域分支。
- 如果 `domain` 为 `"软件开发"`， 则将工作流路由到 "软件开发" 领域分支。
- 如果 `domain` 为 `"UI/UX 设计"`， 则将工作流路由到 "UI/UX 设计" 领域分支。
- 等等。

## **4. 采集 AI (Collection AI) - 分支处理流程:**

```mermaid
graph TD
    A[用户输入] --> B(意图识别)
    B --> C{加载关注点}
    C --> D{构建Prompt}
    D --> E((Qwen生成话术))
    E --> F[呈现给用户]
    F --> G[用户回复]
    G --> H{所有关注点已覆盖?}
    H -- 是 --> I[生成文档]
    I --> J{客户确认}
    J -- 确认 --> K[结束]
    J -- 修改 --> G
    H -- 否 --> B

```

**流程图说明：**

1. **用户输入 (A):**  用户提供初始需求描述。
2. **意图识别 (B):**
   - Coze 意图识别节点 接收用户输入。
   - 识别用户意图 (例如：LOGO 设计、海报设计)。
   - 输出意图 ID。
3. **加载关注点 (C):**
   - 根据意图 ID，从 "需求校准" 知识库加载对应的类别 (例如：LOGO 设计) 的 "关注点" 列表。
   - 合并 "通用关注点" 和 "类别特有关注点"。
4. **构建 Prompt (D):**
   - 使用 Prompt 模板，结合以下信息构建完整的 Prompt：
     - 预定义的 Prompt 头部 (角色设定、任务目标等)。
     - "关注点" 列表。
     - 用户已提供的信息 (可选)。
     - 关键词 (可选)。
5. **Qwen 生成话术 (E):**
   - 将构建好的 Prompt 发送给 NLG 模型 (例如 Qwen)。
   - NLG 模型根据 Prompt 生成 提问话术。
6. **呈现给用户 (F):**  将生成的 提问话术 呈现给 用户。
7. **用户回复 (G):**  用户根据 提问话术 提供更多信息。
8. **所有关注点已覆盖？ (H):**
   - 判断是否已经收集到所有 "关注点" 的信息。
   - 如果是 (是)， 则 进入 生成文档 步骤 (I)。
   - 如果否 (否)， 则 返回 意图识别 步骤 (B)， 进行 下一轮 意图识别 和 提问。
9. **生成文档 (I):**  将 用户 提供 的 信息 整理成 结构化的 Markdown 文档。
10. **客户确认 (J):**
    - 将 生成的 Markdown 文档 呈现给 客户， 请 客户 确认 信息 是否 准确 和 完整。
    - 如果 客户 确认 信息 无误 (确认)， 则 流程 结束 (K)。
    - 如果 客户 提出 修改 意见 (修改)， 则 返回 用户回复 步骤 (G)， 重新 采集 信息。
11. **结束 (K):**  需求采集 完成。

# **采集 AI 如何将 "关注点" 转化为具体的提问或引导话术**

1. **动态构建 Prompt**

我们需要 动态 地 构建 Prompt， 将 实际 的 数据 插入 到 Prompt 模板 中。 这 可以 通过 以下 步骤 实现：

- **从 "需求校准" 知识库 中 获取 当前 "关注点" 的 信息 (描述 和 关键词)。**
- **从 Coze 平台 的 对话历史 功能 中 获取 对话 历史 记录。**
- **从 Coze 平台 的 用户输入 变量 中 获取 用户 最新 的 输入。**
- **(可选) 根据 意图识别结果 和 已覆盖的 "关注点" 列表， 调整 Prompt 的 内容。** 例如， 如果 用户 已经 提供了 预算 信息， 则 可以 在 Prompt 中 感谢 用户 提供 该 信息， 并 引导 用户 提供 更 详细 的 预算 范围。
- **将 获取到的 数据 插入 到 Prompt 模板 中， 构建 完整的 Prompt 字符串。**

```
 角色定位

您是一位拥有 12 年实战经验 的 平面设计 顾问，擅长：

- 深入理解并挖掘客户真实需求
- 用简明易懂的语言进行专业交流
- 将抽象创意转化为具体设计方案
- 在预算范围内提供最优设计解决方案

## 任务要求

请根据以下优先级排序的关注点，采集客户需求：
{{关注点描述及优先级}}

优先级说明：

- P0：核心关注点，必须首先明确
- P1：重要关注点，应在核心关注点后立即询问
- P2：辅助关注点，用于完善需求细节
- P3：可选关注点，时间允许时询问

请注意：

1. 首先分析用户输入是否已包含与"关注点"相关信息，已包含则不再询问
2. 优先询问更高优先级（P0、P1）的关注点
3. 每次对话最多询问 1-2 个关注点，避免用户信息超载

## 处理不确定回答策略

当用户回答模糊或表示不确定时：

1. 提供 2-3 个典型选项供用户参考
2. 使用"也许您想要的是..."引导用户思考
3. 分享相关案例或行业洞见帮助用户明确需求
4. 暂时搁置该问题，转向其他关注点，稍后再回来讨论

例如，当用户对"设计风格"不确定时：

- "很多客户在风格选择上会犹豫。我可以提供几个参考方向：现代简约风格强调干净线条和留白；复古风格注重怀旧元素和质感；手绘风格则更有亲和力和独特性。您更倾向于哪种方向？"

## 引导原则

- 用轻松友好的语气与用户交流，就像朋友聊天一样，让用户感到舒适自在。
- 如果用户对某些概念不太确定，使用生动的例子或类比来帮助用户理解，校准模块也提供了一些启发，但绝不是限制。
- 逐步引导用户提供信息，确保充分理解用户的需求，但绝不会让用户感到有压力或被束缚。
- 对于用户提供的任何信息，及时确认，确保理解一致，避免误解。
- 避免使用任何可能引起冲突的语言，始终保持友好和中立的态度。
- 使用积极的语言，鼓励用户表达想法，用户的每一个想法都至关重要。
- 确保提出的问题清晰明确，避免任何模糊不清的地方。

## 示例问答参考

**核心价值提问示例：**

- 问："您希望 LOGO 体现什么样的核心价值或理念？"
- 优答："我们是一家环保科技公司，希望 LOGO 能体现创新与可持续发展的理念。"
- 弱答："不太确定，好看就行。"
- 引导："理解您的感受。也许可以从您的业务出发思考：您的产品/服务为客户解决了什么问题？您与竞争对手的不同之处是什么？这些都可以转化为 LOGO 中的核心价值。例如，苹果的 LOGO 传达简约创新，Nike 的勾形象征速度与动感。"

**目标受众提问示例：**

- 问："您的目标客户群体是谁？"
- 优答："25-35 岁的年轻专业人士，熟悉科技产品，注重品质与设计感。"
- 弱答："所有人。"
- 引导："定位'所有人'通常会导致设计失焦。考虑一下：谁最需要您的产品/服务？谁最有可能为此付费？例如，是年轻人还是中老年人？是专业人士还是普通消费者？他们的教育背景、收入水平、生活方式如何？这些信息将帮助我们设计出更具吸引力的作品。"

## 输出规范

- 在收集完所有信息后，我会为您起草一份 结构化、易读 的要求文件。
- 文件将使用 通俗易懂的语言，避免专业术语，确保您能轻松理解。
- 文件将 清晰、完整 地包含所有必要信息，并使用 Markdown 格式，便于阅读。

## 请牢记以下原则

- 用户可能并不完全明确自己的真实需求，您要通过引导和发现，帮助用户找到真正的需求和目标。
- 这个过程也是一个展示您专业服务的机会，您要始终关注用户的 **痛点**，并提供最优解决方案。
- 文档生成不仅是信息收集，更是一个 **营销机会**，您需要展现您的的专业性与用心。
```
