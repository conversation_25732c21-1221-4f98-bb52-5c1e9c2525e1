# Log File Highlighter 使用演示

## 🎯 安装完成后的使用步骤

### 1. 确认配置已生效
打开 VSCode，确认您的 `.vscode/settings.json` 文件包含了我们的配置。如果没有，请将以下配置添加到您的设置文件中：

```json
{
    // 日志文件关联配置
    "files.associations": {
        "*.log": "log",
        "logs/*.log": "log",
        "logs/*.log.*": "log"
    },
    
    // Log File Highlighter 自定义配置
    "logFileHighlighter.customPatterns": [
        {
            "pattern": "ERROR",
            "foreground": "#ff6b6b",
            "background": "#2d1b1b"
        },
        {
            "pattern": "CRITICAL",
            "foreground": "#ffffff",
            "background": "#d63031"
        },
        {
            "pattern": "WARNING",
            "foreground": "#fdcb6e",
            "background": "#2d2a1b"
        },
        {
            "pattern": "INFO",
            "foreground": "#74b9ff",
            "background": "#1b2a2d"
        },
        {
            "pattern": "DEBUG",
            "foreground": "#a29bfe",
            "background": "#1e1b2d"
        },
        {
            "pattern": "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2},\\d{3}",
            "foreground": "#00b894"
        },
        {
            "pattern": "session_id\":\\s*\"[^\"]+\"",
            "foreground": "#e17055"
        },
        {
            "pattern": "user_id\":\\s*\"[^\"]+\"",
            "foreground": "#fd79a8"
        },
        {
            "pattern": "\\[会话开始\\]|\\[会话结束\\]|\\[用户输入\\]|\\[AI回复\\]",
            "foreground": "#00cec9",
            "fontWeight": "bold"
        }
    ]
}
```

### 2. 打开日志文件
在 VSCode 中打开任何一个日志文件：
- `logs/app.log` - 应用日志
- `logs/error.log` - 错误日志  
- `logs/session.log` - 会话日志

### 3. 查看高亮效果

#### 🎨 您将看到以下高亮效果：

**日志级别高亮：**
- 🔴 **ERROR**: 红色背景，白色文字
- 🟠 **CRITICAL**: 深红色背景，白色文字
- 🟡 **WARNING**: 黄色背景，深色文字
- 🔵 **INFO**: 蓝色背景，白色文字
- 🟣 **DEBUG**: 紫色背景，白色文字

**特殊内容高亮：**
- 🟢 **时间戳**: 绿色文字 (如: `2025-06-16 15:33:36,927`)
- 🟠 **session_id**: 橙色文字 (如: `"session_id":"edb25946-2005-4dce-a429-29b2810a52e5"`)
- 🟣 **user_id**: 粉色文字 (如: `"user_id":"test_user_123"`)
- 🔵 **会话操作**: 青色加粗文字 (如: `[会话开始]`, `[用户输入]`, `[AI回复]`)

## 🔍 实际使用示例

### 示例1: 查看app.log
打开 `logs/app.log`，您会看到：

```json
{"timestamp": "2025-06-16 15:33:36,927", "level": "INFO", "module": "backend.api.main", "message": "收到聊天请求: session_id=edb25946-2005-4dce-a429-29b2810a52e5, text=我需要开发一个电商应用", "session_id": "edb25946-2005-4dce-a429-29b2810a52e5", "stage": "api"}
```

**高亮效果：**
- `2025-06-16 15:33:36,927` - 绿色时间戳
- `INFO` - 蓝色背景
- `session_id=edb25946-2005-4dce-a429-29b2810a52e5` - 橙色高亮

### 示例2: 查看error.log
打开 `logs/error.log`，您会看到：

```json
{"timestamp": "2025-06-17 15:14:06,327", "level": "ERROR", "logger": "__main__", "message": "[外部依赖失败] deepseek_api.chat_completion: LLM服务响应超时", "severity": "high", "session_id": "test_session_1750144446"}
```

**高亮效果：**
- `ERROR` - 红色背景，白色文字
- `2025-06-17 15:14:06,327` - 绿色时间戳
- `[外部依赖失败]` - 特殊标记高亮

### 示例3: 查看session.log
打开 `logs/session.log`，您会看到：

```json
{"timestamp": "2025-06-17 15:30:10,526", "level": "INFO", "message": "[用户输入] 你好，我想了解一下你们的产品", "session_id": "test_session_1750145410", "user_id": "test_user_123", "type": "user_input"}
```

**高亮效果：**
- `[用户输入]` - 青色加粗文字
- `"session_id":"test_session_1750145410"` - 橙色高亮
- `"user_id":"test_user_123"` - 粉色高亮

## 🛠️ 高级使用技巧

### 1. 快速搜索特定内容
使用 `Ctrl+F` (Windows/Linux) 或 `Cmd+F` (Mac) 搜索：

```bash
# 搜索特定会话
"session_id":"your-session-id"

# 搜索错误日志
"level":"ERROR"

# 搜索用户输入
"[用户输入]"

# 搜索AI回复
"[AI回复]"
```

### 2. 使用正则表达式搜索
在搜索框中点击正则表达式按钮 (.*) 然后搜索：

```regex
# 搜索所有会话开始
\[会话开始\]

# 搜索特定时间范围
2025-06-17 1[5-6]:\d{2}:\d{2}

# 搜索长时间的LLM调用 (>5秒)
"duration":[5-9]\.\d+
```

### 3. 折叠和展开JSON
- `Ctrl+Shift+[` - 折叠当前区域
- `Ctrl+Shift+]` - 展开当前区域
- `Ctrl+K Ctrl+0` - 折叠所有
- `Ctrl+K Ctrl+J` - 展开所有

### 4. 多文件对比
1. 打开两个日志文件
2. 右键第一个文件选择 "Select for Compare"
3. 右键第二个文件选择 "Compare with Selected"

## 🎯 针对不同日志文件的查阅策略

### app.log 查阅技巧
- **目标**: 了解完整的业务流程
- **搜索建议**: 
  ```bash
  # 按会话ID追踪完整链路
  "session_id":"specific-session-id"
  
  # 查看业务流程
  "stage":"business_flow"
  
  # 查看LLM调用
  "LLM调用"
  ```

### error.log 查阅技巧
- **目标**: 快速定位和解决问题
- **搜索建议**:
  ```bash
  # 查看所有错误
  "level":"ERROR"
  
  # 查看严重错误
  "level":"CRITICAL"
  
  # 查看特定组件错误
  "component":"deepseek_api"
  ```

### session.log 查阅技巧
- **目标**: 分析用户行为和会话质量
- **搜索建议**:
  ```bash
  # 查看用户交互
  "type":"user_input"
  "type":"ai_response"
  
  # 查看会话生命周期
  "[会话开始]"
  "[会话结束]"
  
  # 查看状态变更
  "type":"state_change"
  ```

## 🚨 故障排除

### 问题1: 高亮不显示
**解决方案:**
1. 确认 Log File Highlighter 扩展已安装并启用
2. 检查文件是否被正确识别为 `.log` 类型
3. 重新加载 VSCode 窗口 (`Ctrl+Shift+P` → "Developer: Reload Window")

### 问题2: 自定义模式不工作
**解决方案:**
1. 检查 `.vscode/settings.json` 中的配置是否正确
2. 确认正则表达式语法正确
3. 重启 VSCode

### 问题3: 性能问题
**解决方案:**
1. 对于大文件，考虑使用文件分割
2. 关闭不必要的扩展
3. 增加内存限制：`"files.maxMemoryForLargeFilesMB": 4096`

## 📈 提升效率的小贴士

1. **使用书签**: `Ctrl+Shift+P` → "Bookmarks: Toggle" 标记重要位置
2. **快速跳转**: `Ctrl+G` 跳转到指定行号
3. **多光标编辑**: `Ctrl+Alt+Down` 添加多个光标
4. **侧边栏预览**: 使用 minimap 快速导航大文件
5. **实时监控**: 文件会自动刷新显示最新内容

通过这些配置和技巧，您现在可以在 VSCode 中高效地查阅和分析日志文件，快速定位问题并了解系统运行状况！
