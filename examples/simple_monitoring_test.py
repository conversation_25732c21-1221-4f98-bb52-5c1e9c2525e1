#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的监控系统测试
用于验证核心功能
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agents.reply_monitoring_system import (
    ReplyMonitoringSystem, ReplyMetric
)
from backend.data.db.database_manager import DatabaseManager


async def test_basic_monitoring():
    """测试基础监控功能"""
    print("=== 测试基础监控功能 ===")
    
    # 使用文件数据库
    db_path = "/tmp/test_monitoring.db"
    if os.path.exists(db_path):
        os.remove(db_path)
    
    try:
        db_manager = DatabaseManager(db_path)
        monitoring_system = ReplyMonitoringSystem(db_manager)
        
        # 初始化数据库
        await monitoring_system.initialize_database()
        print("✅ 监控系统初始化成功")
        
        # 创建测试指标
        test_metric = ReplyMetric(
            session_id="test_session",
            user_id="test_user",
            reply_key="greeting",
            reply_type="static",
            content="您好！欢迎使用我们的服务。",
            response_time=0.5,
            success=True,
            satisfaction_score=4,
            quality_score=0.85,
            timestamp=datetime.now(),
            context={"test": True}
        )
        
        # 记录指标
        await monitoring_system.record_reply_metric(test_metric)
        print("✅ 指标记录成功")
        
        # 记录满意度反馈
        await monitoring_system.record_satisfaction_feedback(
            session_id="test_session",
            user_id="test_user",
            reply_key="greeting",
            satisfaction_score=4,
            feedback_text="很好的回复",
            context={"test": True}
        )
        print("✅ 满意度反馈记录成功")
        
        # 获取实时指标
        real_time_metrics = await monitoring_system.get_real_time_metrics(60)
        print(f"✅ 实时指标获取成功: {real_time_metrics['total_replies']}个回复")
        
        # 获取系统统计
        stats = monitoring_system.get_stats()
        print(f"✅ 系统统计获取成功: 总回复{stats['total_replies']}次")
        
        # 获取活跃告警
        alerts = await monitoring_system.get_active_alerts()
        print(f"✅ 告警系统正常: {len(alerts)}个活跃告警")
        
        print("\n📊 监控系统核心功能验证:")
        print(f"  - 数据库初始化: ✅")
        print(f"  - 指标记录: ✅")
        print(f"  - 满意度跟踪: ✅")
        print(f"  - 实时监控: ✅")
        print(f"  - 统计分析: ✅")
        print(f"  - 告警系统: ✅")
        
        # 清理测试文件
        if os.path.exists(db_path):
            os.remove(db_path)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_performance_report():
    """测试性能报告生成"""
    print("\n=== 测试性能报告生成 ===")
    
    db_path = "/tmp/test_monitoring_report.db"
    if os.path.exists(db_path):
        os.remove(db_path)
    
    try:
        db_manager = DatabaseManager(db_path)
        monitoring_system = ReplyMonitoringSystem(db_manager)
        
        await monitoring_system.initialize_database()
        
        # 添加多个测试指标
        test_metrics = [
            ReplyMetric(
                session_id=f"session_{i}",
                user_id=f"user_{i}",
                reply_key="greeting",
                reply_type="static",
                content="测试回复",
                response_time=0.1 + i * 0.1,
                success=i % 4 != 0,  # 75%成功率
                satisfaction_score=3 + (i % 3),
                quality_score=0.7 + (i % 3) * 0.1,
                timestamp=datetime.now(),
                context={"test": True}
            )
            for i in range(10)
        ]
        
        for metric in test_metrics:
            await monitoring_system.record_reply_metric(metric)
        
        print(f"✅ 已添加 {len(test_metrics)} 个测试指标")
        
        # 生成性能报告
        report = await monitoring_system.get_performance_report(days=1)
        
        print("📊 性能报告生成成功:")
        print(f"  - 报告周期: {report.get('report_period', 'N/A')}")
        
        overall_score = report.get('overall_score', {})
        print(f"  - 总体评分: {overall_score.get('score', 0)}分 ({overall_score.get('grade', 'N/A')})")
        
        summary = report.get('summary', {})
        print(f"  - 总回复数: {summary.get('total_replies', 0)}")
        print(f"  - 成功率: {summary.get('success_rate', 0):.1%}")
        print(f"  - 平均响应时间: {summary.get('average_response_time', 0):.2f}s")
        
        recommendations = report.get('recommendations', [])
        print(f"  - 改进建议: {len(recommendations)}条")
        
        print(f"  - 系统健康状态: {report.get('system_health', 'N/A')}")
        
        # 清理测试文件
        if os.path.exists(db_path):
            os.remove(db_path)
        
        return True
        
    except Exception as e:
        print(f"❌ 性能报告测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("简化的监控系统测试")
    print("=" * 40)
    
    try:
        # 测试基础功能
        basic_test = await test_basic_monitoring()
        
        # 测试性能报告
        report_test = await test_performance_report()
        
        if basic_test and report_test:
            print("\n🎉 所有测试通过！")
            print("\n📋 监控系统功能验证:")
            print("1. ✅ 数据库初始化和表创建")
            print("2. ✅ 回复指标记录和统计")
            print("3. ✅ 用户满意度跟踪")
            print("4. ✅ 实时监控数据获取")
            print("5. ✅ 告警系统基础功能")
            print("6. ✅ 性能报告生成")
            print("7. ✅ 系统健康状态评估")
            print("8. ✅ 改进建议生成")
        else:
            print("\n❌ 部分测试失败")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
