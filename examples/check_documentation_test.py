#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查文档注释完整性测试
验证类和方法的注释是否完整和规范
"""

import ast
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def check_class_documentation(file_path):
    """检查文件中类的文档注释"""
    print(f"\n=== 检查文件: {os.path.basename(file_path)} ===")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        classes_found = 0
        classes_documented = 0
        methods_found = 0
        methods_documented = 0
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                classes_found += 1
                class_name = node.name
                
                # 检查类文档字符串
                docstring = ast.get_docstring(node)
                if docstring:
                    classes_documented += 1
                    docstring_lines = len(docstring.split('\n'))
                    print(f"✅ 类 {class_name}: 有文档 ({docstring_lines}行)")
                    
                    # 检查文档字符串质量
                    if docstring_lines >= 10:
                        print(f"   🌟 详细文档 (≥10行)")
                    elif docstring_lines >= 5:
                        print(f"   👍 中等文档 (5-9行)")
                    else:
                        print(f"   ⚠️ 简单文档 (<5行)")
                else:
                    print(f"❌ 类 {class_name}: 缺少文档")
                
                # 检查类中的方法
                for item in node.body:
                    if isinstance(item, ast.FunctionDef):
                        methods_found += 1
                        method_name = item.name
                        
                        method_docstring = ast.get_docstring(item)
                        if method_docstring:
                            methods_documented += 1
                            method_doc_lines = len(method_docstring.split('\n'))
                            if method_doc_lines >= 5:
                                print(f"   ✅ 方法 {method_name}: 详细文档 ({method_doc_lines}行)")
                            else:
                                print(f"   ✅ 方法 {method_name}: 简单文档 ({method_doc_lines}行)")
                        else:
                            print(f"   ❌ 方法 {method_name}: 缺少文档")
        
        # 统计结果
        class_doc_rate = (classes_documented / classes_found * 100) if classes_found > 0 else 0
        method_doc_rate = (methods_documented / methods_found * 100) if methods_found > 0 else 0
        
        print(f"\n📊 文档统计:")
        print(f"  - 类文档覆盖率: {classes_documented}/{classes_found} ({class_doc_rate:.1f}%)")
        print(f"  - 方法文档覆盖率: {methods_documented}/{methods_found} ({method_doc_rate:.1f}%)")
        
        return {
            'file': os.path.basename(file_path),
            'classes_found': classes_found,
            'classes_documented': classes_documented,
            'methods_found': methods_found,
            'methods_documented': methods_documented,
            'class_doc_rate': class_doc_rate,
            'method_doc_rate': method_doc_rate
        }
        
    except Exception as e:
        print(f"❌ 检查文件失败: {e}")
        return None


def check_file_header_documentation(file_path):
    """检查文件头部文档"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 检查文件头部注释
        has_shebang = lines[0].startswith('#!')
        has_encoding = any('coding' in line for line in lines[:3])
        has_module_doc = False
        
        # 查找模块级文档字符串
        for i, line in enumerate(lines[:20]):  # 只检查前20行
            if '"""' in line and i > 0:
                has_module_doc = True
                break
        
        print(f"📄 文件头部检查:")
        print(f"  - Shebang: {'✅' if has_shebang else '❌'}")
        print(f"  - 编码声明: {'✅' if has_encoding else '❌'}")
        print(f"  - 模块文档: {'✅' if has_module_doc else '❌'}")
        
        return {
            'has_shebang': has_shebang,
            'has_encoding': has_encoding,
            'has_module_doc': has_module_doc
        }
        
    except Exception as e:
        print(f"❌ 检查文件头部失败: {e}")
        return None


def main():
    """主函数"""
    print("文档注释完整性检查")
    print("=" * 50)
    
    # 要检查的文件列表
    files_to_check = [
        'backend/agents/conversation_flow.py',
        'backend/agents/conversation_flow_reply_mixin.py',
        'backend/agents/conversation_flow_message_mixin.py'
    ]
    
    total_stats = {
        'files_checked': 0,
        'total_classes': 0,
        'total_classes_documented': 0,
        'total_methods': 0,
        'total_methods_documented': 0
    }
    
    file_results = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            # 检查文件头部
            header_result = check_file_header_documentation(file_path)
            
            # 检查类和方法文档
            doc_result = check_class_documentation(file_path)
            
            if doc_result:
                file_results.append(doc_result)
                total_stats['files_checked'] += 1
                total_stats['total_classes'] += doc_result['classes_found']
                total_stats['total_classes_documented'] += doc_result['classes_documented']
                total_stats['total_methods'] += doc_result['methods_found']
                total_stats['total_methods_documented'] += doc_result['methods_documented']
        else:
            print(f"❌ 文件不存在: {file_path}")
    
    # 总体统计
    print(f"\n🎯 总体文档质量报告")
    print("=" * 30)
    
    if total_stats['files_checked'] > 0:
        overall_class_rate = (total_stats['total_classes_documented'] / total_stats['total_classes'] * 100) if total_stats['total_classes'] > 0 else 0
        overall_method_rate = (total_stats['total_methods_documented'] / total_stats['total_methods'] * 100) if total_stats['total_methods'] > 0 else 0
        
        print(f"📁 检查文件数: {total_stats['files_checked']}")
        print(f"📚 总类数: {total_stats['total_classes']}")
        print(f"📖 已文档化类数: {total_stats['total_classes_documented']}")
        print(f"🔧 总方法数: {total_stats['total_methods']}")
        print(f"📝 已文档化方法数: {total_stats['total_methods_documented']}")
        print(f"📊 类文档覆盖率: {overall_class_rate:.1f}%")
        print(f"📊 方法文档覆盖率: {overall_method_rate:.1f}%")
        
        # 评估文档质量
        print(f"\n🏆 文档质量评估:")
        if overall_class_rate >= 90 and overall_method_rate >= 70:
            print("🌟 优秀 - 文档覆盖率很高")
        elif overall_class_rate >= 70 and overall_method_rate >= 50:
            print("👍 良好 - 文档覆盖率较好")
        elif overall_class_rate >= 50 and overall_method_rate >= 30:
            print("⚠️ 一般 - 需要改进文档覆盖率")
        else:
            print("❌ 较差 - 急需改进文档")
        
        # 详细文件报告
        print(f"\n📋 详细文件报告:")
        for result in file_results:
            print(f"  {result['file']}:")
            print(f"    - 类: {result['classes_documented']}/{result['classes_found']} ({result['class_doc_rate']:.1f}%)")
            print(f"    - 方法: {result['methods_documented']}/{result['methods_found']} ({result['method_doc_rate']:.1f}%)")
        
        # 改进建议
        print(f"\n💡 改进建议:")
        if overall_class_rate < 100:
            print("  - 为所有类添加详细的文档字符串")
        if overall_method_rate < 80:
            print("  - 为重要方法添加文档字符串，包括参数和返回值说明")
        print("  - 确保文档字符串包含功能描述、参数说明、返回值和使用示例")
        print("  - 使用标准的文档字符串格式（Google风格或NumPy风格）")
        
        print(f"\n🎉 文档检查完成！")
        
    else:
        print("❌ 没有成功检查任何文件")


if __name__ == "__main__":
    main()
