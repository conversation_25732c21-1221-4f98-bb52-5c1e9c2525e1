#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的模板版本管理系统测试
用于调试数据库连接问题
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.data.db.database_manager import DatabaseManager


async def test_database_creation():
    """测试数据库表创建"""
    print("=== 测试数据库表创建 ===")
    
    # 使用内存数据库
    db_manager = DatabaseManager(":memory:")
    
    # 创建表
    create_sql = """
        CREATE TABLE IF NOT EXISTS template_versions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            template_id TEXT NOT NULL,
            version TEXT NOT NULL,
            content TEXT NOT NULL,
            variables TEXT,
            category TEXT NOT NULL,
            language TEXT DEFAULT 'zh-CN',
            status TEXT NOT NULL,
            created_by TEXT NOT NULL,
            created_at TIMESTAMP NOT NULL,
            updated_at TIMESTAMP NOT NULL,
            description TEXT,
            tags TEXT,
            metadata TEXT,
            UNIQUE(template_id, version)
        )
    """
    
    try:
        # 执行创建表
        result = await db_manager.execute_update(create_sql)
        print(f"创建表结果: {result}")
        
        # 验证表是否存在
        check_sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='template_versions'"
        tables = await db_manager.execute_query(check_sql)
        print(f"表检查结果: {tables}")
        
        # 插入测试数据
        insert_sql = """
            INSERT INTO template_versions (
                template_id, version, content, variables, category, language,
                status, created_by, created_at, updated_at, description, tags, metadata
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        import json
        from datetime import datetime
        
        params = (
            "test_template",
            "1.0",
            "测试内容",
            json.dumps([]),
            "test",
            "zh-CN",
            "active",
            "admin",
            datetime.now().isoformat(),
            datetime.now().isoformat(),
            "测试描述",
            json.dumps([]),
            json.dumps({})
        )
        
        insert_result = await db_manager.execute_update(insert_sql, params)
        print(f"插入数据结果: {insert_result}")
        
        # 查询数据
        select_sql = "SELECT * FROM template_versions WHERE template_id = ?"
        records = await db_manager.execute_query(select_sql, ("test_template",))
        print(f"查询结果: {records}")
        
        print("✅ 数据库操作测试成功")
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        import traceback
        traceback.print_exc()


async def test_template_version_manager():
    """测试模板版本管理器"""
    print("\n=== 测试模板版本管理器 ===")
    
    from backend.agents.template_version_manager import TemplateVersionManager
    
    try:
        # 使用文件数据库而不是内存数据库
        db_path = "/tmp/test_template_versions.db"
        if os.path.exists(db_path):
            os.remove(db_path)
        
        db_manager = DatabaseManager(db_path)
        version_manager = TemplateVersionManager(db_manager)
        
        # 初始化数据库
        await version_manager.initialize_database()
        print("数据库初始化完成")
        
        # 创建模板版本
        template_v1 = await version_manager.create_template_version(
            template_id="greeting_message",
            content="您好！欢迎使用我们的服务。",
            variables=[],
            category="greeting",
            created_by="admin",
            description="基础问候消息"
        )
        
        print(f"创建模板版本: {template_v1.template_id} v{template_v1.version}")
        print(f"内容: {template_v1.content}")
        print(f"状态: {template_v1.status.value}")
        
        # 激活版本
        success = await version_manager.activate_template_version(
            template_id="greeting_message",
            version=template_v1.version
        )
        print(f"激活版本: {'成功' if success else '失败'}")
        
        # 获取激活版本
        active_template = await version_manager.get_template_version("greeting_message")
        if active_template:
            print(f"当前激活版本: v{active_template.version}")
            print(f"内容: {active_template.content}")
        
        print("✅ 模板版本管理器测试成功")
        
        # 清理测试文件
        if os.path.exists(db_path):
            os.remove(db_path)
        
    except Exception as e:
        print(f"❌ 模板版本管理器测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    print("简化的模板版本管理系统测试")
    print("=" * 50)
    
    await test_database_creation()
    await test_template_version_manager()


if __name__ == "__main__":
    asyncio.run(main())
