#!/usr/bin/env python3
"""
测试文档生成器修复效果的脚本
"""

import sys
import os
import asyncio
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.agents.document_generator import DocumentGenerator
from backend.data.db.database_manager import DatabaseManager
from backend.agents.llm_service import AutoGenLLMServiceAgent

async def test_format_list_to_markdown():
    """直接测试 _format_list_to_markdown 方法"""

    print("🧪 开始测试 _format_list_to_markdown 方法...")

    # 创建文档生成器实例
    db_manager = DatabaseManager()
    llm_client = AutoGenLLMServiceAgent()
    doc_generator = DocumentGenerator(llm_client=llm_client, db_manager=db_manager)

    # 测试数据1：简单字符串数组
    test_data_1 = ['目标是获取更多的客户资源。', '企业的老板有想法但缺乏营销策划和实施的能力，且资金有限。']
    print("\n� 测试数据1 (简单字符串数组):")
    print(f"输入: {test_data_1}")
    result_1 = doc_generator._format_list_to_markdown(test_data_1, "requirements")
    print(f"输出: {result_1}")

    # 测试数据2：复杂对象数组
    test_data_2 = [
        {'title': '获得更多客户资源', 'description': '需要通过营销活动吸引更多的潜在客户，提高品牌知名度和用户参与度。'},
        {'title': '有限的营销能力与预算', 'description': '初创企业老板有创意但缺乏营销策划和实施的能力，且总预算仅为3万元。'}
    ]
    print("\n📝 测试数据2 (复杂对象数组):")
    print(f"输入: {test_data_2}")
    result_2 = doc_generator._format_list_to_markdown(test_data_2, "requirements")
    print(f"输出: {result_2}")

    # 测试数据3：字符串
    test_data_3 = "这是一个字符串"
    print("\n� 测试数据3 (字符串):")
    print(f"输入: {test_data_3}")
    result_3 = doc_generator._format_list_to_markdown(test_data_3, "requirements")
    print(f"输出: {result_3}")

    # 验证结果
    print("\n🔍 验证结果:")

    # 检查简单数组是否正确转换为Markdown列表
    if result_1.startswith("- ") and "\n- " in result_1:
        print("✅ 简单数组转换正确")
    else:
        print("❌ 简单数组转换失败")

    # 检查复杂对象是否正确转换为Markdown列表
    if "- **" in result_2 and "**: " in result_2:
        print("✅ 复杂对象转换正确")
    else:
        print("❌ 复杂对象转换失败")

    # 检查字符串是否直接返回
    if result_3 == test_data_3:
        print("✅ 字符串处理正确")
    else:
        print("❌ 字符串处理失败")

if __name__ == "__main__":
    asyncio.run(test_format_list_to_markdown())
