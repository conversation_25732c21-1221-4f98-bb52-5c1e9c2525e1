#!/usr/bin/env python3
"""
测试流程节点日志颜色显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.utils.logging_config import configure_logging, get_logger

# 配置日志系统（只启用控制台，不启用文件）
configure_logging(
    enable_console=True,
    enable_file=False,  # 关闭文件日志，只看控制台效果
    log_level=20  # INFO级别
)

# 获取日志记录器
logger = get_logger(__name__)

print("=== 测试流程节点日志颜色显示 ===")
print("请查看以下日志消息的颜色效果：\n")

print("=== 意图识别节点（橙色）===")
logger.info("开始识别意图: '设计一份餐饮宣传海报'")
logger.info("意图识别结果: {'intent': 'design_request', 'confidence': 0.95}")
logger.info("[意图识别] 处理完成")

print("\n=== 领域分类节点（绿色）===")
logger.info("开始分类文本: '设计一份餐饮宣传海报'")
logger.info("领域分类结果: {'domain': 'design', 'confidence': 0.88}")
logger.info("[领域分类] 分类完成")

print("\n=== 类别分类节点（紫色）===")
logger.info("开始类别分类，文本: '设计一份餐饮宣传海报'")
logger.info("类别分类结果: {'category': 'poster_design', 'confidence': 0.92}")
logger.info("[类别分类] 分类完成")

print("\n=== 信息提取节点（深粉色）===")
logger.info("开始提取信息，关注点数量: 10")
logger.info("信息提取与融合完成, 本次提取/更新了 3 个点")
logger.info("[信息提取] 提取完成")

print("\n=== 业务节点（蓝色）===")
logger.info("[业务节点] chat_request_processing (request_processing): started")
logger.info("[业务节点] document_generation (generation): completed")

print("\n=== 状态切换（金色）===")
logger.info("[状态切换] idle -> processing (触发: user_input)")
logger.info("[状态切换] processing -> completed")

print("\n=== LLM调用（灰色）===")
logger.info("LLM响应 - 模型: doubao-pro-32k, 耗时: 1.23s, Tokens: 150")
logger.info("LLM调用完成，返回结果长度: 500")
logger.info("call_llm 执行成功")

print("\n=== 用户交互（原有颜色）===")
logger.info("[用户输入] 设计一份餐饮宣传海报")
logger.info("用户输入: 这是另一种格式")
logger.info("[输入消息] 这是输入消息格式")
logger.info("输出内容: 这是输出内容格式")

print("\n=== 普通消息（默认颜色）===")
logger.info("这是普通的INFO消息，应该是青色")
logger.info("这条消息不包含特殊关键词")

print("\n=== 不同日志级别 ===")
logger.debug("这是一条DEBUG日志")
logger.info("这是一条INFO日志")
logger.warning("这是一条WARNING日志")
logger.error("这是一条ERROR日志")

print("\n=== 颜色测试完成 ===")
print("如果您看到了不同的颜色，说明流程节点颜色配置生效了！")
print("颜色说明：")
print("- 意图识别：橙色")
print("- 领域分类：绿色") 
print("- 类别分类：紫色")
print("- 信息提取：深粉色")
print("- 业务节点：蓝色")
print("- 状态切换：金色")
print("- LLM调用：灰色")
print("- 用户输入：黄色")
print("- 输入消息：蓝绿色")
print("- 输出内容：紫色")
