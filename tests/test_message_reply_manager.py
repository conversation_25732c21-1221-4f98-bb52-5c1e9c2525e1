#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息回复管理器单元测试
测试MessageReplyManager的各项功能
"""

import unittest
import asyncio
import os
import sys
import tempfile
import json
from unittest.mock import AsyncMock, MagicMock, patch

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agents.message_reply_manager import MessageReplyManager, MessageType, ReplyCategory


class MockLLMClient:
    """模拟LLM客户端"""
    
    def __init__(self, should_fail=False):
        self.should_fail = should_fail
        self.call_count = 0
    
    async def call_llm(self, messages, agent_name=None, temperature=0.7, max_tokens=200, **kwargs):
        """模拟LLM调用"""
        self.call_count += 1
        
        if self.should_fail:
            raise Exception("模拟LLM调用失败")
        
        return {
            "content": f"模拟LLM回复 - agent: {agent_name}, 调用次数: {self.call_count}"
        }


class TestMessageReplyManager(unittest.TestCase):
    """消息回复管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_llm = MockLLMClient()
        self.reply_manager = MessageReplyManager(llm_client=self.mock_llm)
    
    def test_initialization(self):
        """测试初始化"""
        # 测试基本初始化
        manager = MessageReplyManager()
        self.assertIsNotNone(manager)
        self.assertIsNone(manager.llm_client)
        
        # 测试带LLM客户端的初始化
        manager_with_llm = MessageReplyManager(llm_client=self.mock_llm)
        self.assertIsNotNone(manager_with_llm.llm_client)
    
    def test_static_templates_loaded(self):
        """测试静态模板是否正确加载"""
        templates = self.reply_manager.list_available_templates()
        
        # 检查是否加载了预期的静态模板
        static_templates = templates['static_templates']
        expected_templates = [
            'greeting', 'reset_confirmation', 'document_finalized',
            'system_error', 'clarification_request'
        ]
        
        for template in expected_templates:
            self.assertIn(template, static_templates)
    
    def test_dynamic_generators_loaded(self):
        """测试动态生成器是否正确加载"""
        templates = self.reply_manager.list_available_templates()
        
        # 检查是否加载了预期的动态生成器
        dynamic_generators = templates['dynamic_generators']
        expected_generators = [
            'greeting_response', 'empathy_and_clarify',
            'clarification_response', 'apology_response'
        ]
        
        for generator in expected_generators:
            self.assertIn(generator, dynamic_generators)
    
    async def test_static_reply_basic(self):
        """测试基本静态回复"""
        reply = await self.reply_manager.get_reply(
            reply_key="greeting",
            message_type=MessageType.STATIC
        )
        
        self.assertIsInstance(reply, str)
        self.assertGreater(len(reply), 0)
        self.assertEqual(reply, "您好！很高兴为您服务。请问有什么可以帮您？")
    
    async def test_static_reply_with_parameters(self):
        """测试带参数的静态回复"""
        reply = await self.reply_manager.get_reply(
            reply_key="processing_error",
            message_type=MessageType.STATIC,
            context={"error_msg": "测试错误"}
        )
        
        self.assertIn("测试错误", reply)
    
    async def test_dynamic_reply_success(self):
        """测试动态回复成功情况"""
        reply = await self.reply_manager.get_reply(
            reply_key="greeting_response",
            message_type=MessageType.DYNAMIC,
            context={
                "prompt_instruction": "测试提示",
                "message": "测试消息"
            }
        )
        
        self.assertIsInstance(reply, str)
        self.assertIn("模拟LLM回复", reply)
        self.assertEqual(self.mock_llm.call_count, 1)
    
    async def test_dynamic_reply_without_llm_client(self):
        """测试没有LLM客户端时的动态回复"""
        manager_without_llm = MessageReplyManager()
        
        reply = await manager_without_llm.get_reply(
            reply_key="greeting_response",
            message_type=MessageType.DYNAMIC,
            context={"prompt_instruction": "测试提示"}
        )
        
        # 应该返回回退消息
        self.assertIsInstance(reply, str)
        self.assertNotIn("模拟LLM回复", reply)
    
    async def test_dynamic_reply_llm_failure(self):
        """测试LLM调用失败时的回退"""
        failing_llm = MockLLMClient(should_fail=True)
        manager_with_failing_llm = MessageReplyManager(llm_client=failing_llm)
        
        reply = await manager_with_failing_llm.get_reply(
            reply_key="greeting_response",
            message_type=MessageType.DYNAMIC,
            context={"prompt_instruction": "测试提示"}
        )
        
        # 应该返回回退消息
        self.assertIsInstance(reply, str)
        self.assertNotIn("模拟LLM回复", reply)
    
    async def test_fallback_mechanism(self):
        """测试回退机制"""
        # 测试不存在的模板
        reply = await self.reply_manager.get_reply(
            reply_key="non_existent_template",
            message_type=MessageType.STATIC
        )
        
        self.assertIsInstance(reply, str)
        # 应该返回默认的回退消息
        self.assertIn("内部问题", reply)
    
    async def test_statistics_tracking(self):
        """测试统计功能"""
        # 执行一些操作
        await self.reply_manager.get_reply("greeting", MessageType.STATIC)
        await self.reply_manager.get_reply("greeting_response", MessageType.DYNAMIC,
                                          context={"prompt_instruction": "测试"})
        await self.reply_manager.get_reply("non_existent", MessageType.STATIC)
        
        stats = self.reply_manager.get_reply_stats()
        
        self.assertEqual(stats['total_replies'], 3)
        self.assertEqual(stats['success_count'], 2)
        self.assertEqual(stats['fallback_count'], 1)
        self.assertEqual(stats['error_count'], 0)
        self.assertAlmostEqual(stats['success_rate'], 2/3, places=2)
    
    def test_add_static_template(self):
        """测试添加静态模板"""
        template_key = "test_template"
        template_content = "这是一个测试模板"
        
        self.reply_manager.add_static_template(
            key=template_key,
            template=template_content,
            category=ReplyCategory.CUSTOM
        )
        
        # 检查模板是否被添加
        templates = self.reply_manager.list_available_templates()
        self.assertIn(template_key, templates['static_templates'])
    
    def test_add_dynamic_generator(self):
        """测试添加动态生成器"""
        generator_key = "test_generator"
        generator_config = {
            "agent_name": "test_agent",
            "temperature": 0.8,
            "max_tokens": 300
        }
        
        self.reply_manager.add_dynamic_generator(
            key=generator_key,
            config=generator_config
        )
        
        # 检查生成器是否被添加
        templates = self.reply_manager.list_available_templates()
        self.assertIn(generator_key, templates['dynamic_generators'])
    
    async def test_hybrid_reply(self):
        """测试混合类型回复"""
        # 测试混合回复（主要是回退到静态或动态）
        reply = await self.reply_manager.get_reply(
            reply_key="greeting",
            message_type=MessageType.HYBRID
        )
        
        self.assertIsInstance(reply, str)
        self.assertGreater(len(reply), 0)
    
    def test_config_loading(self):
        """测试配置加载"""
        # 创建临时配置文件
        temp_config = {
            "general": {
                "default_language": "en-US",
                "fallback_enabled": True
            },
            "static_templates": {
                "test_template": {
                    "content": "Test message",
                    "category": "test"
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(temp_config, f)
            temp_config_path = f.name
        
        try:
            # 使用临时配置文件创建管理器
            manager = MessageReplyManager(config_path=temp_config_path)
            self.assertIsNotNone(manager.config)
            self.assertEqual(manager.config['general']['default_language'], 'en-US')
        finally:
            os.unlink(temp_config_path)
    
    def test_message_type_enum(self):
        """测试消息类型枚举"""
        self.assertEqual(MessageType.STATIC.value, "static")
        self.assertEqual(MessageType.DYNAMIC.value, "dynamic")
        self.assertEqual(MessageType.HYBRID.value, "hybrid")
        self.assertEqual(MessageType.FALLBACK.value, "fallback")
    
    def test_reply_category_enum(self):
        """测试回复类别枚举"""
        self.assertEqual(ReplyCategory.GREETING.value, "greeting")
        self.assertEqual(ReplyCategory.ERROR.value, "error")
        self.assertEqual(ReplyCategory.CONFIRMATION.value, "confirmation")


class TestMessageReplyManagerAsync(unittest.IsolatedAsyncioTestCase):
    """异步测试类"""
    
    async def asyncSetUp(self):
        """异步测试前准备"""
        self.mock_llm = MockLLMClient()
        self.reply_manager = MessageReplyManager(llm_client=self.mock_llm)
    
    async def test_concurrent_requests(self):
        """测试并发请求"""
        # 创建多个并发请求
        tasks = []
        for i in range(5):
            task = self.reply_manager.get_reply(
                reply_key="greeting",
                message_type=MessageType.STATIC
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks)
        
        # 检查结果
        self.assertEqual(len(results), 5)
        for result in results:
            self.assertIsInstance(result, str)
            self.assertGreater(len(result), 0)
    
    async def test_dynamic_reply_concurrent(self):
        """测试动态回复的并发处理"""
        tasks = []
        for i in range(3):
            task = self.reply_manager.get_reply(
                reply_key="greeting_response",
                message_type=MessageType.DYNAMIC,
                context={"prompt_instruction": f"测试提示 {i}"}
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        # 检查结果
        self.assertEqual(len(results), 3)
        for result in results:
            self.assertIn("模拟LLM回复", result)
        
        # 检查LLM被调用了3次
        self.assertEqual(self.mock_llm.call_count, 3)


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加同步测试
    suite.addTests(loader.loadTestsFromTestCase(TestMessageReplyManager))
    
    # 添加异步测试
    suite.addTests(loader.loadTestsFromTestCase(TestMessageReplyManagerAsync))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
