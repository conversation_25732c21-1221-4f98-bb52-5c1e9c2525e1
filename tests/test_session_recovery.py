#!/usr/bin/env python3
"""
会话恢复功能自动化测试脚本

测试场景：
1. 基础会话恢复
2. 状态恢复
3. 关注点状态恢复
4. 数据完整性验证
"""

import asyncio
import json
import uuid
import time
import logging
from typing import Dict, Any, List
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.agents.conversation_flow import AutoGenConversationFlowAgent
from backend.data.db.database_manager import DatabaseManager
from backend.data.db.message_manager import MessageManager
from backend.data.db.summary_manager import SummaryManager
from backend.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SessionRecoveryTester:
    """会话恢复测试器"""
    
    def __init__(self):
        self.db_manager = DatabaseManager(settings.DATABASE_PATH)
        self.message_manager = MessageManager(self.db_manager)
        self.summary_manager = SummaryManager(self.db_manager)
        self.agent = None
        self.test_session_id = None
        
    async def setup(self):
        """测试环境设置"""
        logger.info("🔧 设置测试环境...")
        
        # 创建测试用的session ID
        self.test_session_id = f"test_session_{uuid.uuid4().hex[:8]}"
        logger.info(f"📝 测试会话ID: {self.test_session_id}")
        
        # 初始化Agent（这里需要根据实际情况调整）
        # 注意：你可能需要根据实际的Agent初始化方式调整这部分代码
        try:
            self.agent = AutoGenConversationFlowAgent(
                db_path=settings.DATABASE_PATH
            )
            logger.info("✅ Agent初始化成功")
        except Exception as e:
            logger.error(f"❌ Agent初始化失败: {e}")
            raise
    
    async def test_basic_session_recovery(self) -> bool:
        """测试基础会话恢复功能"""
        logger.info("\n🧪 测试1: 基础会话恢复")
        
        try:
            # 步骤1: 创建初始会话
            logger.info("📤 发送初始消息...")
            initial_message = {
                "text": "我想开发一个在线教育平台",
                "session_id": self.test_session_id
            }
            
            response1 = await self.agent.process_message(initial_message)
            logger.info(f"📥 收到响应: {response1.get('response', '')[:100]}...")
            
            # 步骤2: 继续对话
            logger.info("📤 发送后续消息...")
            follow_up_message = {
                "text": "主要面向K12学生，包含在线课程和作业系统",
                "session_id": self.test_session_id
            }
            
            response2 = await self.agent.process_message(follow_up_message)
            logger.info(f"📥 收到响应: {response2.get('response', '')[:100]}...")
            
            # 步骤3: 模拟会话中断（重新创建Agent实例）
            logger.info("🔄 模拟会话中断，重新创建Agent...")
            old_agent = self.agent
            self.agent = AutoGenConversationFlowAgent(
                db_path=settings.DATABASE_PATH
            )
            
            # 步骤4: 恢复会话
            logger.info("🔄 尝试恢复会话...")
            recovery_message = {
                "text": "请继续",
                "session_id": self.test_session_id
            }
            
            response3 = await self.agent.process_message(recovery_message)
            logger.info(f"📥 恢复后响应: {response3.get('response', '')[:100]}...")
            
            # 验证恢复是否成功
            if response3 and response3.get('response'):
                logger.info("✅ 基础会话恢复测试通过")
                return True
            else:
                logger.error("❌ 基础会话恢复测试失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 基础会话恢复测试异常: {e}")
            return False
    
    async def test_state_recovery(self) -> bool:
        """测试状态恢复功能"""
        logger.info("\n🧪 测试2: 状态恢复")
        
        try:
            # 检查数据库中的会话状态
            query = "SELECT domain_id, category_id FROM conversations WHERE conversation_id = ?"
            result = await self.db_manager.get_record(query, (self.test_session_id,))
            
            if result:
                logger.info(f"📊 会话状态 - 领域: {result.get('domain_id')}, 类别: {result.get('category_id')}")
                
                # 验证状态是否正确恢复
                if result.get('domain_id') and result.get('category_id'):
                    logger.info("✅ 状态恢复测试通过")
                    return True
                else:
                    logger.warning("⚠️ 状态信息不完整")
                    return False
            else:
                logger.error("❌ 未找到会话状态信息")
                return False
                
        except Exception as e:
            logger.error(f"❌ 状态恢复测试异常: {e}")
            return False
    
    async def test_message_history_recovery(self) -> bool:
        """测试消息历史恢复"""
        logger.info("\n🧪 测试3: 消息历史恢复")
        
        try:
            # 加载消息历史
            history = await self.message_manager.load_conversation_history(
                self.test_session_id, limit=50
            )
            
            logger.info(f"📚 加载到 {len(history)} 条历史消息")
            
            # 验证消息历史
            if len(history) >= 2:  # 至少应该有我们发送的两条消息
                for i, msg in enumerate(history):
                    logger.info(f"  {i+1}. {msg.get('role')}: {msg.get('content', '')[:50]}...")
                
                logger.info("✅ 消息历史恢复测试通过")
                return True
            else:
                logger.error("❌ 消息历史不完整")
                return False
                
        except Exception as e:
            logger.error(f"❌ 消息历史恢复测试异常: {e}")
            return False
    
    async def test_focus_point_recovery(self) -> bool:
        """测试关注点状态恢复"""
        logger.info("\n🧪 测试4: 关注点状态恢复")
        
        try:
            # 查询关注点状态
            query = """
                SELECT focus_id, status, extracted_info, attempts 
                FROM concern_point_coverage 
                WHERE conversation_id = ?
            """
            results = await self.db_manager.execute_query(query, (self.test_session_id,))
            
            logger.info(f"🎯 找到 {len(results)} 个关注点状态记录")
            
            for result in results:
                logger.info(f"  关注点 {result['focus_id']}: {result['status']} (尝试次数: {result['attempts']})")
            
            if results:
                logger.info("✅ 关注点状态恢复测试通过")
                return True
            else:
                logger.warning("⚠️ 未找到关注点状态记录（可能正常，取决于对话进度）")
                return True  # 这种情况可能是正常的
                
        except Exception as e:
            logger.error(f"❌ 关注点状态恢复测试异常: {e}")
            return False
    
    async def cleanup(self):
        """清理测试数据"""
        logger.info("\n🧹 清理测试数据...")
        
        try:
            # 删除测试会话的所有相关数据
            await self.db_manager.execute_update(
                "DELETE FROM messages WHERE conversation_id = ?",
                (self.test_session_id,)
            )
            
            await self.db_manager.execute_update(
                "DELETE FROM concern_point_coverage WHERE conversation_id = ?",
                (self.test_session_id,)
            )
            
            await self.db_manager.execute_update(
                "DELETE FROM conversation_summaries WHERE conversation_id = ?",
                (self.test_session_id,)
            )
            
            await self.db_manager.execute_update(
                "DELETE FROM conversations WHERE conversation_id = ?",
                (self.test_session_id,)
            )
            
            logger.info("✅ 测试数据清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理测试数据失败: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始会话恢复功能测试")
        
        test_results = []
        
        try:
            await self.setup()
            
            # 运行各项测试
            test_results.append(("基础会话恢复", await self.test_basic_session_recovery()))
            test_results.append(("状态恢复", await self.test_state_recovery()))
            test_results.append(("消息历史恢复", await self.test_message_history_recovery()))
            test_results.append(("关注点状态恢复", await self.test_focus_point_recovery()))
            
        except Exception as e:
            logger.error(f"❌ 测试执行异常: {e}")
        finally:
            await self.cleanup()
        
        # 输出测试结果
        logger.info("\n📊 测试结果汇总:")
        logger.info("=" * 50)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            if result:
                passed += 1
        
        logger.info("=" * 50)
        logger.info(f"总计: {passed}/{total} 项测试通过")
        
        if passed == total:
            logger.info("🎉 所有测试通过！会话恢复功能正常工作")
        else:
            logger.warning(f"⚠️ 有 {total - passed} 项测试失败，请检查相关功能")

async def main():
    """主函数"""
    tester = SessionRecoveryTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
