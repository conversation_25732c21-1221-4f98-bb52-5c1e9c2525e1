#!/usr/bin/env python3
"""
快速会话恢复测试脚本

这个脚本通过API接口测试会话恢复功能，更接近实际使用场景
"""

import requests
import json
import time
import uuid

# 配置
API_BASE_URL = "http://localhost:8000"
TEST_SESSION_ID = f"test_{uuid.uuid4().hex[:8]}"

def test_session_recovery():
    """测试会话恢复功能"""
    print("🚀 开始会话恢复测试")
    print(f"📝 测试会话ID: {TEST_SESSION_ID}")
    
    # 测试步骤1: 发送初始消息
    print("\n📤 步骤1: 发送初始消息")
    response1 = send_message("我想开发一个电商网站", TEST_SESSION_ID)
    if response1:
        print(f"✅ 响应1: {response1.get('response', '')[:100]}...")
    else:
        print("❌ 初始消息发送失败")
        return False
    
    # 测试步骤2: 继续对话
    print("\n📤 步骤2: 继续对话")
    response2 = send_message("主要销售服装和配饰，需要支持在线支付", TEST_SESSION_ID)
    if response2:
        print(f"✅ 响应2: {response2.get('response', '')[:100]}...")
    else:
        print("❌ 后续消息发送失败")
        return False
    
    # 模拟等待（模拟用户离开一段时间）
    print("\n⏳ 模拟用户离开30秒...")
    time.sleep(2)  # 实际测试时可以设置更长时间
    
    # 测试步骤3: 恢复会话
    print("\n🔄 步骤3: 恢复会话")
    response3 = send_message("请继续", TEST_SESSION_ID)
    if response3:
        print(f"✅ 恢复响应: {response3.get('response', '')[:100]}...")
        print("🎉 会话恢复测试成功！")
        return True
    else:
        print("❌ 会话恢复失败")
        return False

def send_message(message: str, session_id: str) -> dict:
    """发送消息到API"""
    try:
        url = f"{API_BASE_URL}/chat"
        payload = {
            "message": message,
            "session_id": session_id
        }
        
        print(f"  发送: {message}")
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"  ❌ API错误: {response.status_code} - {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 请求异常: {e}")
        return None
    except Exception as e:
        print(f"  ❌ 其他异常: {e}")
        return None

def check_api_health():
    """检查API健康状态"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务正常")
            return True
        else:
            print(f"❌ API服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到API服务: {e}")
        print("请确保后端服务已启动 (python -m uvicorn api.main:app --reload)")
        return False

def test_database_data():
    """测试数据库数据（需要直接访问数据库）"""
    print(f"\n📊 检查数据库中的会话数据 (session_id: {TEST_SESSION_ID})")
    print("你可以手动执行以下SQL查询来验证数据:")
    print(f"  SELECT * FROM conversations WHERE conversation_id = '{TEST_SESSION_ID}';")
    print(f"  SELECT * FROM messages WHERE conversation_id = '{TEST_SESSION_ID}';")
    print(f"  SELECT * FROM concern_point_coverage WHERE conversation_id = '{TEST_SESSION_ID}';")

def main():
    """主函数"""
    print("🧪 会话恢复功能快速测试")
    print("=" * 50)
    
    # 检查API服务
    if not check_api_health():
        return
    
    # 运行测试
    success = test_session_recovery()
    
    # 显示数据库查询建议
    test_database_data()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试完成！会话恢复功能正常工作")
    else:
        print("❌ 测试失败，请检查系统配置")
    
    print(f"\n💡 提示: 你可以使用相同的session_id ({TEST_SESSION_ID}) 在前端继续测试")

if __name__ == "__main__":
    main()
